#!/usr/bin/env python3
"""
测试通知分离功能：
1. 飞书推送：实时增持数据
2. 邮件推送：每天早上9:00近7天增持数据
"""
import asyncio
from datetime import datetime, date, timedelta
from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.services.notifications.notification_manager import notification_manager
from app.models.holdings import HoldingChange, Stock


async def test_feishu_realtime_notification():
    """测试飞书实时增持通知"""
    db = SessionLocal()
    
    try:
        print("📱 测试飞书实时增持通知...")
        
        # 模拟新增持数据
        recent_increases = db.query(HoldingChange).join(Stock).filter(
            HoldingChange.direction == 'increase',
            HoldingChange.announcement_date >= date.today() - timedelta(days=3)
        ).limit(3).all()
        
        if not recent_increases:
            print("⚠️ 没有找到最近的增持数据用于测试")
            return
        
        print(f"📊 模拟检测到 {len(recent_increases)} 条新增持数据")
        
        # 模拟调用飞书推送方法
        from app.utils.scheduler import SchedulerManager
        scheduler = SchedulerManager()
        
        await scheduler._send_new_increase_holdings_notification(db, recent_increases)
        
        print("✅ 飞书实时增持通知测试完成")
        
    except Exception as e:
        print(f"❌ 飞书通知测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


async def test_email_weekly_notification():
    """测试邮件每周增持通知"""
    db = SessionLocal()
    
    try:
        print("\n📧 测试邮件每周增持通知...")
        
        # 发送每周增持邮件通知
        result = await notification_manager.send_weekly_increase_email_notification(db)
        
        if result:
            if result.success:
                print("✅ 邮件每周增持通知发送成功")
                print(f"📝 消息: {result.message}")
                if result.data:
                    print(f"📊 详情: {result.data}")
            else:
                print("❌ 邮件每周增持通知发送失败")
                print(f"📝 错误: {result.message}")
        else:
            print("⚠️ 没有数据需要发送邮件通知")
        
    except Exception as e:
        print(f"❌ 邮件通知测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


async def test_data_analysis():
    """分析数据情况"""
    db = SessionLocal()
    
    try:
        print("\n📊 数据分析...")
        
        # 分析近7天增持数据
        end_date = date.today()
        start_date = end_date - timedelta(days=7)
        
        total_changes = db.query(HoldingChange).filter(
            HoldingChange.announcement_date >= start_date,
            HoldingChange.announcement_date <= end_date
        ).count()
        
        increase_changes = db.query(HoldingChange).filter(
            HoldingChange.announcement_date >= start_date,
            HoldingChange.announcement_date <= end_date,
            HoldingChange.direction == 'increase'
        ).count()
        
        decrease_changes = db.query(HoldingChange).filter(
            HoldingChange.announcement_date >= start_date,
            HoldingChange.announcement_date <= end_date,
            HoldingChange.direction == 'decrease'
        ).count()
        
        print(f"📅 时间范围: {start_date} 至 {end_date}")
        print(f"📊 总变动记录: {total_changes} 条")
        print(f"📈 增持记录: {increase_changes} 条")
        print(f"📉 减持记录: {decrease_changes} 条")
        
        if increase_changes > 0:
            print(f"✅ 有增持数据，适合发送通知")
        else:
            print(f"⚠️ 无增持数据，不会发送通知")
        
        # 分析今天的新数据
        today_changes = db.query(HoldingChange).filter(
            HoldingChange.announcement_date == date.today()
        ).count()
        
        today_increases = db.query(HoldingChange).filter(
            HoldingChange.announcement_date == date.today(),
            HoldingChange.direction == 'increase'
        ).count()
        
        print(f"\n📅 今日数据:")
        print(f"📊 总变动记录: {today_changes} 条")
        print(f"📈 增持记录: {today_increases} 条")
        
        if today_increases > 0:
            print(f"✅ 今日有增持数据，会触发飞书实时推送")
        else:
            print(f"⚠️ 今日无增持数据，不会触发飞书推送")
        
    except Exception as e:
        print(f"❌ 数据分析失败: {e}")
    finally:
        db.close()


def show_notification_logic():
    """显示通知逻辑说明"""
    print("🔔 通知逻辑说明:")
    print("=" * 50)
    print("📱 飞书推送 (实时):")
    print("  • 触发时机: 每次定时任务抓取到新数据时")
    print("  • 推送内容: 只推送新增持数据（不包括减持）")
    print("  • 推送频率: 实时（根据抓取频率）")
    print("  • 数据范围: 当次抓取的新增持记录")
    print("  • 包含信息: 股票名称、持有人、增持股数、金额、变动比例")
    print()
    print("📧 邮件推送 (定时):")
    print("  • 触发时机: 每天早上9:00")
    print("  • 推送内容: 近7天所有增持数据汇总")
    print("  • 推送频率: 每日一次")
    print("  • 数据范围: 近7天的增持记录")
    print("  • 包含信息: 数据统计、重要增持排行、HTML格式表格")
    print("=" * 50)


if __name__ == "__main__":
    print("🚀 启动通知分离功能测试...")
    
    show_notification_logic()
    
    # 运行测试
    asyncio.run(test_data_analysis())
    asyncio.run(test_feishu_realtime_notification())
    asyncio.run(test_email_weekly_notification())
    
    print("\n🎉 测试完成！")
    print("💡 功能特点:")
    print("  • 飞书：实时推送新增持数据，包含变动比例")
    print("  • 邮件：每天9点推送近7天增持汇总，HTML格式")
    print("  • 分离：两种通知互不干扰，各司其职")
