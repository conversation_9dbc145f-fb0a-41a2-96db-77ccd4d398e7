#!/usr/bin/env python3
"""
调试爬虫数据的脚本
"""
import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.scraper import TongHuaShunScraper
from collections import Counter
from datetime import datetime

async def debug_scraper():
    """调试爬虫抓取的数据"""
    scraper = TongHuaShunScraper()
    
    print("开始抓取数据进行调试...")
    
    # 抓取第1页数据
    data = await scraper.scrape_holdings_data(page=1, max_pages=1)
    
    if not data:
        print("没有抓取到数据")
        return
    
    print(f"抓取到 {len(data)} 条数据")
    print("\n=== 数据分析 ===")
    
    # 分析日期分布
    dates = [item['announcement_date'] for item in data]
    date_counter = Counter(dates)
    print(f"日期分布: {dict(date_counter)}")
    
    # 分析股票分布
    stocks = [f"{item['stock_code']}-{item['stock_name']}" for item in data]
    stock_counter = Counter(stocks)
    print(f"股票数量: {len(stock_counter)} 只")
    print(f"前5只股票: {list(stock_counter.keys())[:5]}")
    
    # 分析持有人分布
    holders = [item['holder_name'] for item in data]
    holder_counter = Counter(holders)
    print(f"持有人数量: {len(holder_counter)} 人")
    
    # 分析增减持方向
    directions = [item['direction'] for item in data]
    direction_counter = Counter(directions)
    print(f"增减持方向分布: {dict(direction_counter)}")
    
    # 显示前3条数据样本
    print("\n=== 前3条数据样本 ===")
    for i, item in enumerate(data[:3]):
        print(f"第{i+1}条:")
        print(f"  股票: {item['stock_code']} {item['stock_name']}")
        print(f"  日期: {item['announcement_date']}")
        print(f"  持有人: {item['holder_name']}")
        print(f"  方向: {item['direction']}")
        print(f"  变动股数: {item['change_shares']}")
        print()

if __name__ == "__main__":
    asyncio.run(debug_scraper())
