#!/usr/bin/env python3
"""
调度管理功能测试脚本
"""
import requests
import json
from datetime import datetime

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def get_admin_token():
    """获取管理员token"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/admin/login", json=login_data)
    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        raise Exception(f"登录失败: {response.text}")

def test_scheduler_apis():
    """测试调度器相关API"""
    print("🚀 开始测试调度管理功能...")
    print("=" * 50)
    
    # 获取token
    token = get_admin_token()
    headers = {"Authorization": f"Bearer {token}"}
    
    # 1. 测试获取调度器状态
    print("1. 测试获取调度器状态...")
    response = requests.get(f"{BASE_URL}/system/scheduler/status", headers=headers)
    if response.status_code == 200:
        status_data = response.json()
        print(f"   ✅ 调度器状态: {status_data.get('status')}")
        print(f"   ✅ 任务数量: {len(status_data.get('jobs', []))}")
    else:
        print(f"   ❌ 获取状态失败: {response.text}")
    
    # 2. 测试获取任务列表
    print("\n2. 测试获取任务列表...")
    response = requests.get(f"{BASE_URL}/system/scheduler/jobs", headers=headers)
    if response.status_code == 200:
        jobs_data = response.json()
        print(f"   ✅ 获取到 {len(jobs_data.get('jobs', []))} 个任务")
        for job in jobs_data.get('jobs', []):
            print(f"   - {job.get('id')}: {job.get('description', 'N/A')}")
            print(f"     下次执行: {job.get('next_run', 'N/A')}")
            print(f"     触发器: {job.get('trigger', 'N/A')}")
    else:
        print(f"   ❌ 获取任务列表失败: {response.text}")
    
    # 3. 测试获取调度器配置
    print("\n3. 测试获取调度器配置...")
    response = requests.get(f"{BASE_URL}/system/config/scheduler", headers=headers)
    if response.status_code == 200:
        config_data = response.json()
        print("   ✅ 当前配置:")
        for key, value in config_data.items():
            print(f"     {key}: {value}")
    else:
        print(f"   ❌ 获取配置失败: {response.text}")
    
    # 4. 测试更新配置
    print("\n4. 测试更新调度器配置...")
    update_config = {
        "scraping_interval_minutes": 40,
        "scraping_max_pages": 3
    }
    response = requests.put(f"{BASE_URL}/system/config/scheduler", 
                          json=update_config, headers=headers)
    if response.status_code == 200:
        result = response.json()
        print(f"   ✅ 配置更新成功: {result.get('message')}")
        print(f"   ✅ 任务重新加载: {result.get('jobs_reloaded')}")
    else:
        print(f"   ❌ 更新配置失败: {response.text}")
    
    # 5. 测试任务控制（立即执行）
    print("\n5. 测试任务控制...")
    control_data = {"action": "trigger"}
    response = requests.post(f"{BASE_URL}/system/scheduler/jobs/health_check/control", 
                           json=control_data, headers=headers)
    if response.status_code == 200:
        result = response.json()
        print(f"   ✅ 任务控制成功: {result.get('message')}")
    else:
        print(f"   ❌ 任务控制失败: {response.text}")
    
    print("\n" + "=" * 50)
    print("🎉 调度管理功能测试完成！")

if __name__ == "__main__":
    try:
        test_scheduler_apis()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
