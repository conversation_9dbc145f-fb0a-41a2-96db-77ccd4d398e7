#!/usr/bin/env python3
"""
测试邮件中的成交均价显示功能
验证：
1. 邮件HTML表格包含成交均价列
2. 成交均价数据正确显示
3. 邮件发送成功
"""
import asyncio
from datetime import datetime, date, timedelta
from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.services.notifications.notification_manager import notification_manager
from app.models.holdings import HoldingChange, Stock


async def test_email_price_avg():
    """测试邮件中的成交均价显示"""
    print("📧 测试邮件中的成交均价显示功能")
    print("=" * 60)
    
    # 1. 检查邮件数据中的成交均价
    print("\n📊 1. 检查邮件数据中的成交均价")
    print("-" * 30)
    
    db = SessionLocal()
    try:
        # 获取近7天的增持数据
        end_date = date.today()
        start_date = end_date - timedelta(days=7)
        
        increases_with_price = db.query(HoldingChange).join(Stock).filter(
            HoldingChange.direction == 'increase',
            HoldingChange.announcement_date >= start_date,
            HoldingChange.announcement_date <= end_date,
            HoldingChange.price_avg.isnot(None),
            HoldingChange.price_avg > 0
        ).order_by(HoldingChange.change_amount.desc()).limit(10).all()
        
        print(f"📋 找到 {len(increases_with_price)} 条有成交均价的近7天增持记录:")
        for i, change in enumerate(increases_with_price, 1):
            print(f"  {i}. {change.stock.name}({change.stock.code}) - {change.holder_name}")
            print(f"     增持: {change.change_shares:,.0f}万股")
            print(f"     成交均价: {change.price_avg:.2f}元")
            if change.change_amount:
                print(f"     变动金额: {change.change_amount:,.0f}万元")
            if change.change_reason:
                print(f"     变动原因: {change.change_reason}")
            print(f"     公告日期: {change.announcement_date}")
            print()
        
        # 检查没有成交均价的记录
        increases_without_price = db.query(HoldingChange).filter(
            HoldingChange.direction == 'increase',
            HoldingChange.announcement_date >= start_date,
            HoldingChange.announcement_date <= end_date,
            (HoldingChange.price_avg.is_(None)) | (HoldingChange.price_avg <= 0)
        ).count()
        
        total_increases = db.query(HoldingChange).filter(
            HoldingChange.direction == 'increase',
            HoldingChange.announcement_date >= start_date,
            HoldingChange.announcement_date <= end_date
        ).count()
        
        price_coverage = ((total_increases - increases_without_price) / total_increases * 100) if total_increases > 0 else 0
        
        print(f"📊 近7天增持数据统计:")
        print(f"  • 总增持记录: {total_increases} 条")
        print(f"  • 有成交均价: {total_increases - increases_without_price} 条")
        print(f"  • 无成交均价: {increases_without_price} 条")
        print(f"  • 成交均价覆盖率: {price_coverage:.1f}%")
        
    except Exception as e:
        print(f"❌ 数据检查失败: {e}")
    finally:
        db.close()
    
    # 2. 测试邮件发送（包含成交均价）
    print("\n📧 2. 测试邮件发送（包含成交均价）")
    print("-" * 30)
    
    db = SessionLocal()
    try:
        result = await notification_manager.send_weekly_increase_email_notification(db)
        
        if result:
            if result.success:
                print("✅ 邮件发送成功！")
                print(f"📝 消息: {result.message}")
                if result.data:
                    print(f"📊 详情: {result.data}")
                print("\n💡 请检查邮箱中的邮件，确认HTML表格是否包含成交均价列")
            else:
                print("❌ 邮件发送失败！")
                print(f"📝 错误: {result.message}")
        else:
            print("⚠️ 没有数据需要发送邮件")
            
    except Exception as e:
        print(f"❌ 邮件发送测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()
    
    # 3. 验证邮件模板结构
    print("\n🔧 3. 验证邮件模板结构")
    print("-" * 30)
    
    try:
        # 读取邮件模板文件，检查是否包含成交均价列
        with open('app/services/notifications/email_service.py', 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # 检查表头是否包含成交均价
        if '成交均价(元)' in template_content:
            print("✅ 邮件模板表头包含成交均价列")
        else:
            print("❌ 邮件模板表头缺少成交均价列")
        
        # 检查数据行是否包含成交均价
        if 'change.price_avg' in template_content:
            print("✅ 邮件模板数据行包含成交均价字段")
        else:
            print("❌ 邮件模板数据行缺少成交均价字段")
        
        # 检查格式化逻辑
        if '"%.2f"|format(change.price_avg' in template_content:
            print("✅ 邮件模板包含成交均价格式化逻辑")
        else:
            print("❌ 邮件模板缺少成交均价格式化逻辑")
            
    except Exception as e:
        print(f"❌ 模板验证失败: {e}")


async def test_email_template_data():
    """测试邮件模板数据结构"""
    print("\n📋 4. 测试邮件模板数据结构")
    print("-" * 30)
    
    db = SessionLocal()
    try:
        # 模拟邮件数据构建过程
        end_date = date.today()
        start_date = end_date - timedelta(days=7)
        
        # 获取增持数据
        all_increases = db.query(HoldingChange).join(Stock).filter(
            HoldingChange.direction == 'increase',
            HoldingChange.announcement_date >= start_date,
            HoldingChange.announcement_date <= end_date
        ).all()
        
        # 构建邮件数据（模拟notification_manager的逻辑）
        top_changes = []
        for change in sorted(all_increases, key=lambda x: x.change_amount or 0, reverse=True)[:5]:
            change_data = {
                'stock_name': change.stock.name,
                'stock_code': change.stock.code,
                'holder_name': change.holder_name,
                'direction': change.direction,
                'change_shares': change.change_shares,
                'change_amount': change.change_amount,
                'change_ratio': change.holding_ratio_after,  # 变动比例
                'change_reason': change.change_reason,  # 变动原因
                'price_avg': change.price_avg,  # 成交均价
                'announcement_date': change.announcement_date.strftime('%Y-%m-%d')
            }
            top_changes.append(change_data)
        
        print(f"📊 邮件模板数据示例（前5条）:")
        for i, change in enumerate(top_changes, 1):
            print(f"  {i}. {change['stock_name']}({change['stock_code']})")
            print(f"     持有人: {change['holder_name']}")
            print(f"     变动股数: {change['change_shares']:,.0f}万股")
            print(f"     变动金额: {change['change_amount']:,.0f}万元" if change['change_amount'] else "     变动金额: 未知")
            print(f"     变动比例: {change['change_ratio']:.2f}%" if change['change_ratio'] else "     变动比例: 未知")
            print(f"     成交均价: {change['price_avg']:.2f}元" if change['price_avg'] and change['price_avg'] > 0 else "     成交均价: -")
            print(f"     变动原因: {change['change_reason'] or '未说明'}")
            print(f"     公告日期: {change['announcement_date']}")
            print()
        
        print("✅ 邮件数据结构包含所有必要字段，包括成交均价")
        
    except Exception as e:
        print(f"❌ 邮件数据测试失败: {e}")
    finally:
        db.close()


async def show_email_implementation_summary():
    """显示邮件成交均价实现总结"""
    print("\n🎯 邮件成交均价显示功能实现总结")
    print("=" * 60)
    print("📧 邮件HTML表格更新:")
    print("   • 表头添加'成交均价(元)'列")
    print("   • 数据行添加成交均价显示")
    print("   • 格式: XX.XX（保留2位小数）")
    print("   • 无价格时显示'-'")
    print()
    print("📊 数据处理:")
    print("   • 邮件数据中已包含price_avg字段")
    print("   • 支持空值和零值处理")
    print("   • 与变动比例、变动原因一起显示")
    print()
    print("🎨 显示效果:")
    print("   • HTML表格美观展示")
    print("   • 与其他列保持一致的样式")
    print("   • 支持大量数据的表格显示")
    print()
    print("🔧 技术实现:")
    print("   • 使用Jinja2模板引擎")
    print("   • 条件格式化：有价格显示数值，无价格显示'-'")
    print("   • 统一的数据源和处理逻辑")


if __name__ == "__main__":
    print("🚀 启动邮件成交均价显示功能测试...")
    print("📧 验证邮件HTML表格包含成交均价列")
    print("💰 验证成交均价数据正确显示")
    print()
    
    # 运行测试
    asyncio.run(test_email_price_avg())
    asyncio.run(test_email_template_data())
    asyncio.run(show_email_implementation_summary())
    
    print("\n🎉 测试完成！")
    print("💡 请检查以下内容:")
    print("  • 邮箱中的邮件HTML表格是否包含成交均价列")
    print("  • 成交均价数据是否正确显示（XX.XX元格式）")
    print("  • 没有成交均价的记录是否显示'-'")
    print("  • 表格样式是否美观一致")
    print()
    print("🎯 现在所有三个渠道都支持成交均价显示:")
    print("  • 📊 前端Holdings页面：成交均价列")
    print("  • 📱 飞书推送：均价信息")
    print("  • 📧 邮件推送：HTML表格成交均价列")
