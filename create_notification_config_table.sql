-- 创建通知配置表
CREATE TABLE IF NOT EXISTS notification_configs (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    service_type VARCHAR(20) NOT NULL CHECK (service_type IN ('EMAIL', 'WECHAT', 'FEISHU')),
    is_enabled BOOLEAN NOT NULL DEFAULT true,
    is_default BOOLEAN NOT NULL DEFAULT false,
    description VARCHAR(500),
    
    -- 邮件配置字段
    smtp_host VARCHAR(100),
    smtp_port INTEGER,
    smtp_username VARCHAR(200),
    smtp_password TEXT,
    from_email VARCHAR(200),
    from_name VARCHAR(100),
    
    -- 企业微信配置字段
    wechat_webhook_url TEXT,
    wechat_mentioned_list TEXT,
    wechat_mentioned_mobile_list TEXT,
    
    -- 飞书配置字段
    feishu_webhook_url TEXT,
    feishu_at_all BOOLEAN DEFAULT false,
    feishu_at_users TEXT,
    
    -- 通用配置字段
    extra_config TEXT,
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_notification_configs_service_type ON notification_configs(service_type);
CREATE INDEX IF NOT EXISTS idx_notification_configs_is_enabled ON notification_configs(is_enabled);
CREATE INDEX IF NOT EXISTS idx_notification_configs_is_default ON notification_configs(is_default);

-- 创建唯一约束：每种服务类型只能有一个默认配置
CREATE UNIQUE INDEX IF NOT EXISTS idx_notification_configs_default_unique 
ON notification_configs(service_type) 
WHERE is_default = true;

-- 添加注释
COMMENT ON TABLE notification_configs IS '通知配置表';
COMMENT ON COLUMN notification_configs.name IS '配置名称';
COMMENT ON COLUMN notification_configs.service_type IS '通知服务类型';
COMMENT ON COLUMN notification_configs.is_enabled IS '是否启用';
COMMENT ON COLUMN notification_configs.is_default IS '是否为默认配置';
COMMENT ON COLUMN notification_configs.description IS '配置描述';
COMMENT ON COLUMN notification_configs.smtp_host IS 'SMTP服务器地址';
COMMENT ON COLUMN notification_configs.smtp_port IS 'SMTP端口';
COMMENT ON COLUMN notification_configs.smtp_username IS 'SMTP用户名';
COMMENT ON COLUMN notification_configs.smtp_password IS 'SMTP密码(加密存储)';
COMMENT ON COLUMN notification_configs.from_email IS '发件人邮箱';
COMMENT ON COLUMN notification_configs.from_name IS '发件人名称';
COMMENT ON COLUMN notification_configs.wechat_webhook_url IS '企业微信Webhook URL';
COMMENT ON COLUMN notification_configs.wechat_mentioned_list IS '默认@用户列表(JSON)';
COMMENT ON COLUMN notification_configs.wechat_mentioned_mobile_list IS '默认@手机号列表(JSON)';
COMMENT ON COLUMN notification_configs.feishu_webhook_url IS '飞书Webhook URL';
COMMENT ON COLUMN notification_configs.feishu_at_all IS '是否@所有人';
COMMENT ON COLUMN notification_configs.feishu_at_users IS '默认@用户ID列表(JSON)';
COMMENT ON COLUMN notification_configs.extra_config IS '额外配置(JSON格式)';
COMMENT ON COLUMN notification_configs.created_at IS '创建时间';
COMMENT ON COLUMN notification_configs.updated_at IS '更新时间';

-- 插入默认配置示例
INSERT INTO notification_configs (name, service_type, is_enabled, is_default, description) VALUES
('默认邮件配置', 'EMAIL', false, true, '系统默认邮件通知配置'),
('默认微信配置', 'WECHAT', false, true, '系统默认企业微信通知配置'),
('默认飞书配置', 'FEISHU', false, true, '系统默认飞书通知配置')
ON CONFLICT DO NOTHING;
