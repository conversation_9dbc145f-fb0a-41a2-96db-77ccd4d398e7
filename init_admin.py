#!/usr/bin/env python3
"""
初始化管理员账户和系统配置
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal, engine
from app.models.admin import Admin, SystemConfig
from app.core.auth import get_password_hash
from app.models.base import Base


def create_default_admin():
    """创建默认管理员账户"""
    db = SessionLocal()
    try:
        # 检查是否已存在管理员
        existing_admin = db.query(Admin).filter(Admin.username == "admin").first()
        if existing_admin:
            print("默认管理员账户已存在")
            return
        
        # 创建默认管理员
        default_admin = Admin(
            username="admin",
            email="<EMAIL>",
            password_hash=get_password_hash("admin123"),
            display_name="系统管理员",
            is_active=True,
            is_superuser=True,
            notes="默认创建的超级管理员账户"
        )
        
        db.add(default_admin)
        db.commit()
        print("默认管理员账户创建成功")
        print("用户名: admin")
        print("密码: admin123")
        print("请及时修改默认密码！")
        
    except Exception as e:
        print(f"创建默认管理员失败: {e}")
        db.rollback()
    finally:
        db.close()


def create_default_configs():
    """创建默认系统配置"""
    db = SessionLocal()
    try:
        default_configs = [
            {
                "config_key": "scraping_enabled",
                "config_value": "true",
                "description": "是否启用数据抓取",
                "config_type": "bool",
                "config_group": "scraping"
            },
            {
                "config_key": "scraping_interval_minutes",
                "config_value": "30",
                "description": "数据抓取间隔（分钟）",
                "config_type": "int",
                "config_group": "scraping"
            },
            {
                "config_key": "scraping_max_pages",
                "config_value": "5",
                "description": "每次抓取的最大页数",
                "config_type": "int",
                "config_group": "scraping"
            },
            {
                "config_key": "data_cleanup_enabled",
                "config_value": "true",
                "description": "是否启用数据清理",
                "config_type": "bool",
                "config_group": "maintenance"
            },
            {
                "config_key": "data_retention_days",
                "config_value": "730",
                "description": "数据保留天数",
                "config_type": "int",
                "config_group": "maintenance"
            },
            {
                "config_key": "impact_score_update_enabled",
                "config_value": "true",
                "description": "是否启用影响力评分更新",
                "config_type": "bool",
                "config_group": "analysis"
            },
            {
                "config_key": "health_check_enabled",
                "config_value": "true",
                "description": "是否启用健康检查",
                "config_type": "bool",
                "config_group": "system"
            },
            {
                "config_key": "log_level",
                "config_value": "INFO",
                "description": "日志级别",
                "config_type": "string",
                "config_group": "system"
            }
        ]
        
        for config_data in default_configs:
            # 检查配置是否已存在
            existing_config = db.query(SystemConfig).filter(
                SystemConfig.config_key == config_data["config_key"]
            ).first()
            
            if not existing_config:
                config = SystemConfig(**config_data)
                db.add(config)
        
        db.commit()
        print("默认系统配置创建成功")
        
    except Exception as e:
        print(f"创建默认系统配置失败: {e}")
        db.rollback()
    finally:
        db.close()


def main():
    """主函数"""
    print("正在初始化管理员系统...")
    
    # 创建数据库表
    try:
        Base.metadata.create_all(bind=engine)
        print("数据库表创建成功")
    except Exception as e:
        print(f"创建数据库表失败: {e}")
        return
    
    # 创建默认管理员
    create_default_admin()
    
    # 创建默认配置
    create_default_configs()
    
    print("管理员系统初始化完成！")


if __name__ == "__main__":
    main()
