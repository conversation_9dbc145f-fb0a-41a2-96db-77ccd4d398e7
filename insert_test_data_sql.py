#!/usr/bin/env python3
"""
使用原生SQL插入测试数据
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from sqlalchemy import text
from app.core.database import get_db

def insert_test_data_sql():
    """使用原生SQL插入测试数据"""
    db = next(get_db())
    
    try:
        print("开始插入测试数据...")
        
        # 1. 插入测试股票（如果不存在）
        print("1. 插入测试股票...")
        stocks_sql = """
        INSERT INTO stocks (code, name, exchange, industry, is_active, created_at, updated_at)
        VALUES 
            ('000001', '平安银行', 'SZSE', '银行', true, NOW(), NOW()),
            ('000002', '万科A', 'SZSE', '房地产', true, NOW(), NOW()),
            ('600000', '浦发银行', 'SSE', '银行', true, NOW(), NOW())
        ON CONFLICT (code) DO NOTHING;
        """
        db.execute(text(stocks_sql))
        
        # 2. 获取股票ID
        print("2. 获取股票ID...")
        result = db.execute(text("SELECT id, code FROM stocks WHERE code IN ('000001', '000002', '600000');"))
        stock_map = {row[1]: row[0] for row in result}
        print(f"股票ID映射: {stock_map}")
        
        # 3. 插入测试增减持记录
        print("3. 插入测试增减持记录...")
        # 分别插入每条记录
        holdings_data = [
            (stock_map['000001'], '2025-06-24', '2025-06-24', '张三', 'executive', 'increase',
             100000.0, 1000000.0, 1.5, 10.0, 12.0, 11.0, 1100000.0, '股权激励'),
            (stock_map['000001'], '2025-06-23', '2025-06-23', '李四', 'executive', 'decrease',
             50000.0, 500000.0, 0.8, 9.5, 10.5, 10.0, 500000.0, '减持套现'),
            (stock_map['000002'], '2025-06-22', '2025-06-22', '王五', 'institution', 'increase',
             200000.0, 2000000.0, 2.0, 15.0, 16.0, 15.5, 3100000.0, '战略投资'),
            (stock_map['600000'], '2025-06-21', '2025-06-21', '赵六', 'major_shareholder', 'decrease',
             300000.0, 3000000.0, 3.0, 8.0, 9.0, 8.5, 2550000.0, '资金需求'),
            (stock_map['000002'], '2025-06-20', '2025-06-20', '孙七', 'other', 'increase',
             80000.0, 800000.0, 0.5, 14.0, 15.0, 14.5, 1160000.0, '看好前景')
        ]

        holdings_sql = """
        INSERT INTO holding_changes (
            stock_id, announcement_date, change_date, holder_name, holder_type, direction,
            change_shares, total_shares_after, holding_ratio_after, price_min, price_max, price_avg,
            change_amount, change_reason, created_at, updated_at
        ) VALUES
            (:stock_id, :announcement_date, :change_date, :holder_name, :holder_type, :direction,
             :change_shares, :total_shares_after, :holding_ratio_after, :price_min, :price_max, :price_avg,
             :change_amount, :change_reason, NOW(), NOW());
        """

        for data in holdings_data:
            db.execute(text(holdings_sql), {
                'stock_id': data[0],
                'announcement_date': data[1],
                'change_date': data[2],
                'holder_name': data[3],
                'holder_type': data[4],
                'direction': data[5],
                'change_shares': data[6],
                'total_shares_after': data[7],
                'holding_ratio_after': data[8],
                'price_min': data[9],
                'price_max': data[10],
                'price_avg': data[11],
                'change_amount': data[12],
                'change_reason': data[13]
            })
        
        # 提交事务
        db.commit()
        
        # 4. 验证插入结果
        print("4. 验证插入结果...")
        result = db.execute(text("SELECT COUNT(*) FROM holding_changes;"))
        total_count = result.fetchone()[0]
        
        result = db.execute(text("SELECT COUNT(*) FROM holding_changes WHERE direction = 'increase';"))
        increase_count = result.fetchone()[0]
        
        result = db.execute(text("SELECT COUNT(*) FROM holding_changes WHERE direction = 'decrease';"))
        decrease_count = result.fetchone()[0]
        
        print(f"✅ 测试数据插入成功！")
        print(f"总记录数: {total_count}")
        print(f"增持记录数: {increase_count}")
        print(f"减持记录数: {decrease_count}")
        
        # 5. 显示样本数据
        print("\n样本数据:")
        result = db.execute(text("""
            SELECT s.code, s.name, hc.holder_name, hc.direction, hc.change_shares, hc.announcement_date
            FROM holding_changes hc
            JOIN stocks s ON hc.stock_id = s.id
            ORDER BY hc.announcement_date DESC
            LIMIT 5;
        """))
        
        for row in result:
            print(f"  {row[0]} {row[1]} - {row[2]} {row[3]} {row[4]}股 ({row[5]})")
        
    except Exception as e:
        print(f"插入测试数据失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    insert_test_data_sql()
