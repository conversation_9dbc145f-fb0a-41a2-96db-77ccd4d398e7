#!/usr/bin/env python3
"""
发送每日增减持数据邮件
"""
import asyncio
from datetime import datetime, date, timedelta
from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.holdings import HoldingChange, Stock
from app.services.notifications.email_service import EmailNotificationService
from app.schemas.notifications import NotificationContentBase, NotificationPriority


async def send_daily_holdings_email():
    """发送每日增减持数据邮件"""
    db = SessionLocal()
    
    try:
        print("📧 开始发送每日增减持数据邮件...")
        
        # 获取最近7天的增减持数据
        end_date = date.today()
        start_date = end_date - timedelta(days=7)
        
        print(f"📅 查询时间范围: {start_date} 至 {end_date}")
        
        holdings = db.query(HoldingChange).join(Stock).filter(
            HoldingChange.announcement_date >= start_date,
            HoldingChange.announcement_date <= end_date
        ).order_by(HoldingChange.announcement_date.desc()).limit(50).all()
        
        if not holdings:
            print("⚠️ 没有找到增减持数据")
            return
        
        print(f"📊 找到 {len(holdings)} 条增减持数据")
        
        # 处理数据
        holdings_data = []
        increase_count = 0
        decrease_count = 0
        total_amount = 0
        
        for holding in holdings:
            direction = 'increase' if holding.change_shares and holding.change_shares > 0 else 'decrease'
            if direction == 'increase':
                increase_count += 1
            else:
                decrease_count += 1
            
            if holding.change_amount:
                total_amount += abs(holding.change_amount)
                
            holdings_data.append({
                'stock_name': holding.stock.name if holding.stock else '未知',
                'stock_code': holding.stock.code if holding.stock else '未知',
                'holder_name': holding.holder_name or '未知',
                'direction': direction,
                'change_shares': holding.change_shares or 0,
                'change_amount': holding.change_amount or 0,
                'change_ratio': holding.holding_ratio_after or 0,
                'announcement_date': holding.announcement_date.strftime('%Y-%m-%d') if holding.announcement_date else '未知',
                'price_avg': holding.price_avg or 0
            })
        
        # 按变动金额排序，取前10条重要变动
        holdings_data.sort(key=lambda x: abs(x['change_amount']), reverse=True)
        top_changes = holdings_data[:10]
        
        # 构造邮件内容
        text_content = f"📈 股票增减持信息每日报告\n\n"
        text_content += f"📊 数据概览：\n"
        text_content += f"• 统计时间：{start_date} 至 {end_date}\n"
        text_content += f"• 总记录数：{len(holdings_data)} 条\n"
        text_content += f"• 增持记录：{increase_count} 条\n"
        text_content += f"• 减持记录：{decrease_count} 条\n"
        text_content += f"• 涉及股票：{len(set(h['stock_name'] for h in holdings_data))} 只\n"
        text_content += f"• 总变动金额：{total_amount/10000:.2f} 万元\n"
        text_content += f"• 报告生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        text_content += "📋 重要变动（按变动金额排序）：\n"
        for i, holding in enumerate(top_changes, 1):
            direction_text = "🔺增持" if holding['direction'] == 'increase' else "🔻减持"
            amount_text = f"{holding['change_amount']/10000:.2f}万元" if holding['change_amount'] else "未知"
            price_text = f"，均价{holding['price_avg']:.2f}元" if holding['price_avg'] > 0 else ""
            
            text_content += f"{i:2d}. {holding['stock_name']}({holding['stock_code']}) - {holding['holder_name']}\n"
            text_content += f"     {direction_text} {holding['change_shares']:.2f}万股，金额{amount_text}{price_text}\n"
            text_content += f"     变动比例：{holding['change_ratio']:.2f}%，公告日期：{holding['announcement_date']}\n\n"
        
        if len(holdings_data) > 10:
            text_content += f"... 还有 {len(holdings_data) - 10} 条记录，详见HTML邮件\n"
        
        text_content += "\n📈 市场分析：\n"
        if increase_count > decrease_count:
            text_content += f"• 本期增持活动较为活跃，增持记录占比 {increase_count/len(holdings_data)*100:.1f}%\n"
        elif decrease_count > increase_count:
            text_content += f"• 本期减持活动较为活跃，减持记录占比 {decrease_count/len(holdings_data)*100:.1f}%\n"
        else:
            text_content += f"• 本期增减持活动相对平衡\n"
        
        text_content += f"• 平均单笔变动金额：{total_amount/len(holdings_data)/10000:.2f} 万元\n"
        
        text_content += "\n---\n"
        text_content += "此邮件由增减持数据分析平台自动生成\n"
        text_content += "数据来源：同花顺财经\n"
        
        # 构造通知内容（包含data字段，用于生成HTML邮件）
        content = NotificationContentBase(
            title="📈 股票增减持信息每日报告",
            content=text_content,
            summary=f"最近{(end_date - start_date).days}天共{len(holdings_data)}条增减持变动，涉及{len(set(h['stock_name'] for h in holdings_data))}只股票，总金额{total_amount/10000:.2f}万元",
            data={
                'date': f"{start_date} 至 {end_date}",
                'total_count': len(holdings_data),
                'increase_count': increase_count,
                'decrease_count': decrease_count,
                'total_amount': total_amount,
                'top_changes': holdings_data,  # 传递所有数据，HTML模板会处理显示
                'summary': f"最近{(end_date - start_date).days}天增减持活动分析",
                'stats': {
                    'avg_amount': total_amount/len(holdings_data) if holdings_data else 0,
                    'stock_count': len(set(h['stock_name'] for h in holdings_data)),
                    'increase_ratio': increase_count/len(holdings_data)*100 if holdings_data else 0,
                    'decrease_ratio': decrease_count/len(holdings_data)*100 if holdings_data else 0
                }
            }
        )
        
        # 直接创建邮件服务
        email_config = {
            'enabled': True,
            'smtp_host': 'smtp.qq.com',
            'smtp_port': 465,
            'smtp_username': '<EMAIL>',
            'smtp_password': 'bftshuinrvbsffdc',
            'from_email': '<EMAIL>',
            'from_name': '增减持数据分析平台'
        }
        
        email_service = EmailNotificationService(email_config)
        
        # 邮件配置
        email_send_config = {
            'to_emails': ['<EMAIL>'],  # 发送给自己
            'subject': f'📊 {start_date} 至 {end_date} 增减持数据每日报告'
        }
        
        print("📧 开始发送邮件...")
        print(f"📮 收件人: {email_send_config['to_emails']}")
        print(f"📝 主题: {email_send_config['subject']}")
        print(f"📊 数据统计: {len(holdings_data)}条记录，{increase_count}条增持，{decrease_count}条减持")
        print(f"💰 总金额: {total_amount/10000:.2f}万元")
        
        # 发送邮件
        result = await email_service.send_notification(
            content=content,
            config=email_send_config,
            priority=NotificationPriority.NORMAL
        )
        
        if result.success:
            print("✅ 每日增减持数据邮件发送成功！")
            print(f"📝 发送结果: {result.message}")
            if result.data:
                print(f"📊 发送详情: {result.data}")
            print("\n📧 邮件内容包含:")
            print("  • 数据概览和统计信息")
            print("  • 前10条重要变动详情")
            print("  • 精美的HTML格式展示")
            print("  • 市场分析和趋势总结")
        else:
            print("❌ 每日增减持数据邮件发送失败！")
            print(f"📝 错误信息: {result.message}")
            
    except Exception as e:
        print(f"❌ 发送过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 启动每日增减持数据邮件发送...")
    print("📧 这将发送一份包含最近7天增减持数据的正式邮件报告")
    print("📊 邮件将包含数据统计、重要变动详情和市场分析")
    print("🎨 邮件采用HTML格式，包含精美的表格和图表")
    print()
    
    asyncio.run(send_daily_holdings_email())
