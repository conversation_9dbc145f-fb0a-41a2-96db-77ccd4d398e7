#!/usr/bin/env python3
"""
最终通知功能测试
验证：
1. 飞书推送：实时增持数据（包含变动比例）
2. 邮件推送：每天早上9:00近7天增持数据（包含变动比例）
"""
import asyncio
from datetime import datetime, date, timedelta
from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.services.notifications.notification_manager import notification_manager
from app.models.holdings import HoldingChange, Stock


async def test_final_notification_system():
    """最终通知系统测试"""
    print("🎉 最终通知功能测试")
    print("=" * 60)
    
    # 1. 测试飞书实时增持通知
    print("\n📱 1. 测试飞书实时增持通知")
    print("-" * 30)
    
    db = SessionLocal()
    try:
        # 获取最近的增持数据用于测试
        recent_increases = db.query(HoldingChange).join(Stock).filter(
            HoldingChange.direction == 'increase',
            HoldingChange.announcement_date >= date.today() - timedelta(days=3)
        ).limit(5).all()
        
        if recent_increases:
            print(f"📊 找到 {len(recent_increases)} 条最近增持数据")
            
            # 模拟飞书推送
            from app.utils.scheduler import SchedulerManager
            scheduler = SchedulerManager()
            
            await scheduler._send_new_increase_holdings_notification(db, recent_increases)
            print("✅ 飞书实时增持通知测试完成")
        else:
            print("⚠️ 没有找到最近的增持数据")
            
    except Exception as e:
        print(f"❌ 飞书通知测试失败: {e}")
    finally:
        db.close()
    
    # 2. 测试邮件每周增持通知
    print("\n📧 2. 测试邮件每周增持通知")
    print("-" * 30)
    
    db = SessionLocal()
    try:
        result = await notification_manager.send_weekly_increase_email_notification(db)
        
        if result:
            if result.success:
                print("✅ 邮件每周增持通知发送成功")
                print(f"📝 消息: {result.message}")
            else:
                print("❌ 邮件每周增持通知发送失败")
                print(f"📝 错误: {result.message}")
        else:
            print("⚠️ 没有数据需要发送邮件通知")
            
    except Exception as e:
        print(f"❌ 邮件通知测试失败: {e}")
    finally:
        db.close()
    
    # 3. 显示功能总结
    print("\n🎯 功能总结")
    print("=" * 60)
    print("📱 飞书推送 (实时):")
    print("  • 触发时机: 每次定时任务抓取到新增持数据时")
    print("  • 推送内容: 只推送新增持数据（不包括减持）")
    print("  • 包含信息: 股票名称、持有人、增持股数、金额、变动比例")
    print("  • 推送频率: 实时（根据抓取频率，当前30分钟）")
    print()
    print("📧 邮件推送 (定时):")
    print("  • 触发时机: 每天早上9:00")
    print("  • 推送内容: 近7天所有增持数据汇总")
    print("  • 包含信息: 数据统计、重要增持排行、HTML格式表格（含变动比例）")
    print("  • 推送频率: 每日一次")
    print()
    print("🔧 技术特点:")
    print("  • 通知分离: 飞书实时，邮件定时，互不干扰")
    print("  • 数据筛选: 只关注增持数据，过滤减持")
    print("  • 变动比例: 两种通知都包含变动比例信息")
    print("  • 配置灵活: 支持数据库配置和环境变量配置")
    print("  • 错误重试: 支持发送失败重试机制")


async def show_data_statistics():
    """显示数据统计"""
    print("\n📊 数据统计")
    print("-" * 30)
    
    db = SessionLocal()
    try:
        # 近7天数据统计
        end_date = date.today()
        start_date = end_date - timedelta(days=7)
        
        total_changes = db.query(HoldingChange).filter(
            HoldingChange.announcement_date >= start_date,
            HoldingChange.announcement_date <= end_date
        ).count()
        
        increase_changes = db.query(HoldingChange).filter(
            HoldingChange.announcement_date >= start_date,
            HoldingChange.announcement_date <= end_date,
            HoldingChange.direction == 'increase'
        ).count()
        
        decrease_changes = db.query(HoldingChange).filter(
            HoldingChange.announcement_date >= start_date,
            HoldingChange.announcement_date <= end_date,
            HoldingChange.direction == 'decrease'
        ).count()
        
        print(f"📅 时间范围: {start_date} 至 {end_date}")
        print(f"📊 总变动记录: {total_changes} 条")
        print(f"📈 增持记录: {increase_changes} 条")
        print(f"📉 减持记录: {decrease_changes} 条")
        
        if increase_changes > 0:
            print(f"✅ 有增持数据，适合发送通知")
            
            # 显示重要增持
            important_increases = db.query(HoldingChange).join(Stock).filter(
                HoldingChange.announcement_date >= start_date,
                HoldingChange.announcement_date <= end_date,
                HoldingChange.direction == 'increase'
            ).order_by(HoldingChange.change_amount.desc()).limit(3).all()
            
            if important_increases:
                print(f"\n🔥 重要增持（前3名）:")
                for i, change in enumerate(important_increases, 1):
                    amount_text = f"{change.change_amount/10000:.2f}万元" if change.change_amount else "未知"
                    ratio_text = f"，变动比例{change.holding_ratio_after:.2f}%" if change.holding_ratio_after else ""
                    print(f"  {i}. {change.stock.name}({change.stock.code}) - {change.holder_name}")
                    print(f"     增持{change.change_shares:.2f}万股，金额{amount_text}{ratio_text}")
        else:
            print(f"⚠️ 无增持数据，不会发送通知")
            
    except Exception as e:
        print(f"❌ 数据统计失败: {e}")
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 启动最终通知功能测试...")
    print("📧 验证邮件通知增加变动比例")
    print("📱 验证飞书推送近7天增持信息")
    print("⏰ 验证每天早上9点推送机制")
    print()
    
    # 运行测试
    asyncio.run(show_data_statistics())
    asyncio.run(test_final_notification_system())
    
    print("\n🎉 测试完成！")
    print("💡 如果测试成功，说明通知分离功能已经正确实现：")
    print("  • 飞书：实时推送新增持数据，包含变动比例")
    print("  • 邮件：每天9点推送近7天增持汇总，HTML格式含变动比例")
    print("  • 定时：每30分钟抓取数据，发现增持立即推送飞书")
    print("  • 分离：两种通知各司其职，互不干扰")
