#!/bin/bash

# 后端Docker部署脚本
# 股票分析系统后端部署

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
IMAGE_NAME="stock-backend"
CONTAINER_NAME="stock_backend"
PORT="8000"
NETWORK_NAME="stock_network"

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    log_success "Docker已安装"
}

# 检查Docker Compose是否安装
check_docker_compose() {
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    log_success "Docker Compose已安装"
}

# 创建网络
create_network() {
    if ! docker network ls | grep -q "$NETWORK_NAME"; then
        log_info "创建Docker网络: $NETWORK_NAME"
        docker network create $NETWORK_NAME
        log_success "网络创建成功"
    else
        log_info "网络 $NETWORK_NAME 已存在"
    fi
}

# 停止并删除旧容器
cleanup_old_container() {
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        log_info "停止并删除旧容器: $CONTAINER_NAME"
        docker stop $CONTAINER_NAME || true
        docker rm $CONTAINER_NAME || true
        log_success "旧容器清理完成"
    fi
}

# 删除旧镜像
cleanup_old_image() {
    if docker images | grep -q "$IMAGE_NAME"; then
        log_info "删除旧镜像: $IMAGE_NAME"
        docker rmi $IMAGE_NAME || true
        log_success "旧镜像清理完成"
    fi
}

# 构建Docker镜像
build_image() {
    log_info "构建Docker镜像: $IMAGE_NAME"
    docker build -t $IMAGE_NAME .
    log_success "镜像构建完成"
}

# 运行容器
run_container() {
    log_info "启动容器: $CONTAINER_NAME"
    docker run -d \
        --name $CONTAINER_NAME \
        --network $NETWORK_NAME \
        -p $PORT:$PORT \
        -v $(pwd)/logs:/app/logs \
        -e DATABASE_URL="***********************************************/stock_analysis" \
        -e REDIS_URL="redis://redis:6379/0" \
        -e DEBUG="False" \
        -e API_HOST="0.0.0.0" \
        -e API_PORT="8000" \
        --restart unless-stopped \
        $IMAGE_NAME
    log_success "容器启动完成"
}

# 检查容器状态
check_container_status() {
    log_info "检查容器状态..."
    sleep 5
    
    if docker ps | grep -q "$CONTAINER_NAME"; then
        log_success "容器运行正常"
        
        # 检查健康状态
        log_info "等待健康检查..."
        for i in {1..30}; do
            if docker inspect --format='{{.State.Health.Status}}' $CONTAINER_NAME 2>/dev/null | grep -q "healthy"; then
                log_success "容器健康检查通过"
                break
            fi
            if [ $i -eq 30 ]; then
                log_warning "健康检查超时，请手动检查容器状态"
            fi
            sleep 2
        done
        
        # 显示容器信息
        echo ""
        log_info "容器信息:"
        docker ps | grep $CONTAINER_NAME
        
        echo ""
        log_info "容器日志 (最后10行):"
        docker logs --tail 10 $CONTAINER_NAME
        
    else
        log_error "容器启动失败"
        log_info "查看容器日志:"
        docker logs $CONTAINER_NAME
        exit 1
    fi
}

# 显示访问信息
show_access_info() {
    echo ""
    log_success "=== 部署完成 ==="
    echo "后端API地址: http://localhost:$PORT"
    echo "健康检查: http://localhost:$PORT/health"
    echo "API文档: http://localhost:$PORT/docs"
    echo ""
    echo "常用命令:"
    echo "  查看日志: docker logs -f $CONTAINER_NAME"
    echo "  停止容器: docker stop $CONTAINER_NAME"
    echo "  重启容器: docker restart $CONTAINER_NAME"
    echo "  进入容器: docker exec -it $CONTAINER_NAME bash"
}

# 主函数
main() {
    log_info "开始部署股票分析系统后端..."
    
    check_docker
    create_network
    cleanup_old_container
    cleanup_old_image
    build_image
    run_container
    check_container_status
    show_access_info
    
    log_success "后端部署完成！"
}

# 处理命令行参数
case "${1:-}" in
    "build")
        build_image
        ;;
    "run")
        run_container
        ;;
    "restart")
        cleanup_old_container
        run_container
        check_container_status
        ;;
    "stop")
        docker stop $CONTAINER_NAME
        log_success "容器已停止"
        ;;
    "logs")
        docker logs -f $CONTAINER_NAME
        ;;
    "clean")
        cleanup_old_container
        cleanup_old_image
        log_success "清理完成"
        ;;
    *)
        main
        ;;
esac
