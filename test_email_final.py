#!/usr/bin/env python3
"""
最终邮件测试
"""
import asyncio
from datetime import datetime, date, timedelta
from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.services.notifications.notification_manager import notification_manager


async def test_email_configuration():
    """测试邮件配置"""
    print("📧 测试邮件配置...")
    
    db = SessionLocal()
    try:
        # 重新初始化通知管理器
        notification_manager._initialize_services_from_db(db)
        
        print(f"✅ 已启用的通知服务: {list(notification_manager.services.keys())}")
        
        # 检查邮件服务配置
        if notification_manager.services.get('email'):
            email_service = notification_manager.services['email']
            print(f"📧 邮件服务配置:")
            print(f"  - SMTP主机: {email_service.smtp_host}")
            print(f"  - SMTP端口: {email_service.smtp_port}")
            print(f"  - 用户名: {email_service.smtp_username}")
            print(f"  - 发件人: {email_service.from_email}")
        else:
            print("❌ 邮件服务未启用")
            
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


async def test_email_sending():
    """测试邮件发送"""
    print("\n📧 测试邮件发送...")
    
    db = SessionLocal()
    try:
        # 发送测试邮件
        result = await notification_manager.send_weekly_increase_email_notification(db)
        
        if result:
            if result.success:
                print("✅ 邮件发送成功！")
                print(f"📝 消息: {result.message}")
                if result.data:
                    print(f"📊 详情: {result.data}")
            else:
                print("❌ 邮件发送失败！")
                print(f"📝 错误: {result.message}")
        else:
            print("⚠️ 没有数据需要发送邮件")
            
    except Exception as e:
        print(f"❌ 邮件发送测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


async def test_direct_email_service():
    """直接测试邮件服务"""
    print("\n📧 直接测试邮件服务...")
    
    try:
        from app.services.notifications.email_service import EmailNotificationService
        from app.schemas.notifications import NotificationContentBase, NotificationPriority
        
        # 使用正确的配置创建邮件服务
        email_config = {
            'enabled': True,
            'smtp_host': 'smtp.qq.com',
            'smtp_port': 465,  # 使用465端口
            'smtp_username': '<EMAIL>',
            'smtp_password': 'bftshuinrvbsffdc',
            'from_email': '<EMAIL>',
            'from_name': '增减持数据分析平台'
        }
        
        email_service = EmailNotificationService(email_config)
        
        print(f"📧 邮件服务配置:")
        print(f"  - SMTP主机: {email_service.smtp_host}")
        print(f"  - SMTP端口: {email_service.smtp_port}")
        print(f"  - 用户名: {email_service.smtp_username}")
        print(f"  - 发件人: {email_service.from_email}")
        
        # 构造测试内容
        content = NotificationContentBase(
            title="📧 邮件配置测试",
            content=f"""这是一条邮件配置测试消息

📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🔧 SMTP配置: {email_config['smtp_host']}:{email_config['smtp_port']}
✅ 配置状态: 使用465端口SSL连接
🎯 测试目的: 验证邮件配置是否正确

如果您收到这条邮件，说明邮件配置已经修复成功！

---
此邮件由增减持数据分析平台发送
""",
            summary="邮件配置测试"
        )
        
        # 发送测试邮件
        result = await email_service.send_notification(
            content=content,
            config={
                'to_emails': ['<EMAIL>'],
                'subject': '📧 邮件配置测试 - 增减持数据分析平台'
            },
            priority=NotificationPriority.NORMAL
        )
        
        if result.success:
            print("✅ 直接邮件服务测试成功！")
            print(f"📝 消息: {result.message}")
        else:
            print("❌ 直接邮件服务测试失败！")
            print(f"📝 错误: {result.message}")
            
    except Exception as e:
        print(f"❌ 直接邮件服务测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 启动最终邮件测试...")
    print("🔧 验证邮件配置是否正确（465端口）")
    print("📧 测试邮件发送功能")
    print()
    
    # 运行测试
    asyncio.run(test_email_configuration())
    asyncio.run(test_direct_email_service())
    asyncio.run(test_email_sending())
    
    print("\n🎉 邮件测试完成！")
    print("💡 如果测试成功，说明邮件功能已经修复：")
    print("  • 使用465端口SSL连接")
    print("  • 正确的QQ邮箱配置")
    print("  • 包含变动比例的HTML邮件")
    print("  • 每天早上9点自动发送")
