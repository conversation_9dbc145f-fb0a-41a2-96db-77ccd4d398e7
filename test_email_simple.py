#!/usr/bin/env python3
"""
简单的邮件通知测试
"""
import asyncio
from datetime import datetime
from app.services.notifications.notification_manager import notification_manager
from app.schemas.notifications import NotificationContentBase, NotificationType, NotificationPriority


async def test_simple_email():
    """测试简单邮件发送"""
    try:
        print("📧 开始测试简单邮件通知功能...")
        
        # 构造测试消息内容
        content = NotificationContentBase(
            title="🧪 邮件通知测试",
            content=f"""这是一条来自股票增减持分析平台的测试邮件

📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🔧 服务类型: EMAIL
✅ 配置状态: 正常
🎯 测试目的: 验证邮件服务连通性

如果您收到这条邮件，说明邮件通知配置工作正常！

---
此邮件由增减持数据分析平台自动发送
""",
            summary="如果您收到这条邮件，说明邮件通知配置工作正常！"
        )
        
        # 邮件配置
        test_config = {
            'to_emails': ['<EMAIL>'],  # 发送给自己测试
            'subject': '🧪 邮件通知测试 - 增减持数据分析平台'
        }
        
        print("📧 开始发送测试邮件...")
        print(f"📮 收件人: {test_config['to_emails']}")
        print(f"📝 主题: {test_config['subject']}")
        
        # 发送邮件
        result = await notification_manager.send_notification(
            notification_type=NotificationType.EMAIL,
            content=content,
            config=test_config,
            priority=NotificationPriority.NORMAL
        )
        
        if result.success:
            print("✅ 邮件发送成功！")
            print(f"📝 发送结果: {result.message}")
            if result.data:
                print(f"📊 发送详情: {result.data}")
        else:
            print("❌ 邮件发送失败！")
            print(f"📝 错误信息: {result.message}")
            
    except Exception as e:
        print(f"❌ 发送过程中出现异常: {e}")
        import traceback
        traceback.print_exc()


async def test_html_email():
    """测试HTML格式邮件"""
    try:
        print("\n📧 开始测试HTML格式邮件...")
        
        # 构造包含数据的测试消息（会自动生成HTML）
        content = NotificationContentBase(
            title="📊 HTML邮件测试",
            content="这是一条HTML格式的测试邮件",
            summary="HTML邮件测试",
            data={
                'date': '2025-07-01',
                'total_count': 10,
                'increase_count': 6,
                'decrease_count': 4,
                'top_changes': [
                    {
                        'stock_name': '测试股票A',
                        'stock_code': '000001',
                        'holder_name': '测试投资者A',
                        'direction': 'increase',
                        'change_shares': 1000.0,
                        'change_amount': 50000.0,
                        'change_ratio': 2.5,
                        'announcement_date': '2025-07-01'
                    },
                    {
                        'stock_name': '测试股票B',
                        'stock_code': '000002',
                        'holder_name': '测试投资者B',
                        'direction': 'decrease',
                        'change_shares': 500.0,
                        'change_amount': 25000.0,
                        'change_ratio': 1.2,
                        'announcement_date': '2025-07-01'
                    }
                ],
                'summary': '今日测试数据显示增持活动较为活跃'
            }
        )
        
        # 邮件配置
        test_config = {
            'to_emails': ['<EMAIL>'],  # 发送给自己测试
            'subject': '📊 HTML邮件测试 - 增减持数据分析平台'
        }
        
        print("📧 开始发送HTML测试邮件...")
        print(f"📮 收件人: {test_config['to_emails']}")
        print(f"📝 主题: {test_config['subject']}")
        
        # 发送邮件
        result = await notification_manager.send_notification(
            notification_type=NotificationType.EMAIL,
            content=content,
            config=test_config,
            priority=NotificationPriority.NORMAL
        )
        
        if result.success:
            print("✅ HTML邮件发送成功！")
            print(f"📝 发送结果: {result.message}")
            if result.data:
                print(f"📊 发送详情: {result.data}")
        else:
            print("❌ HTML邮件发送失败！")
            print(f"📝 错误信息: {result.message}")
            
    except Exception as e:
        print(f"❌ 发送过程中出现异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 启动邮件通知测试...")
    print("⚠️ 请确保已在 .env 文件中配置了邮件相关环境变量：")
    print("   - ENABLE_EMAIL_NOTIFICATION=true")
    print("   - SMTP_HOST=smtp.qq.com")
    print("   - SMTP_PORT=587")
    print("   - SMTP_USERNAME=<EMAIL>")
    print("   - SMTP_PASSWORD=your_app_password")
    print("   - SMTP_FROM_EMAIL=<EMAIL>")
    print("   - SMTP_FROM_NAME=增减持数据分析平台")
    print()
    print("⚠️ 请修改脚本中的 <EMAIL> 为实际的测试邮箱地址")
    print()
    
    async def run_tests():
        await test_simple_email()
        await test_html_email()
    
    asyncio.run(run_tests())
