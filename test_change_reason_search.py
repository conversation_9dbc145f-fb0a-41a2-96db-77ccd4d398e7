#!/usr/bin/env python3
"""
测试变动原因搜索功能
"""
import sys
import os
import requests
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_api_endpoints():
    """测试API端点"""
    base_url = 'http://localhost:8000'
    
    print("🔍 测试变动原因搜索功能")
    print("=" * 50)
    
    # 测试基本API调用
    print("\n1. 测试基本API调用:")
    try:
        response = requests.get(f'{base_url}/api/v1/holdings/?page=1&page_size=3', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功获取 {len(data['items'])} 条记录")
            for item in data['items']:
                reason = item.get('change_reason', '无')
                print(f"   - {item['stock']['name']}: {reason}")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
        return
    
    # 测试变动原因搜索
    test_reasons = ['竞价交易', '二级市场买卖', '股权激励实施', '大宗交易']
    
    for reason in test_reasons:
        print(f"\n2. 测试搜索 '{reason}':")
        try:
            response = requests.get(
                f'{base_url}/api/v1/holdings/?change_reason={reason}&page=1&page_size=3',
                timeout=5
            )
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ 找到 {len(data['items'])} 条记录")
                for item in data['items']:
                    print(f"   - {item['stock']['name']}: {item.get('change_reason', '无')}")
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 搜索失败: {e}")

if __name__ == "__main__":
    test_api_endpoints()
