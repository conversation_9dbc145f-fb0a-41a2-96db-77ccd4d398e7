-- 创建简化的通知配置表
DROP TABLE IF EXISTS notification_configs CASCADE;

CREATE TABLE notification_configs (
    service_type VARCHAR(20) PRIMARY KEY CHECK (service_type IN ('EMAIL', 'WECHAT', 'FEISHU')),
    is_enabled BOOLEAN NOT NULL DEFAULT false,
    description VARCHAR(500),
    notification_time VARCHAR(10) DEFAULT '09:00',
    
    -- 邮件特定配置
    email_recipients TEXT,
    
    -- 微信特定配置  
    wechat_mentioned_users TEXT,
    wechat_mentioned_mobiles TEXT,
    
    -- 飞书特定配置
    feishu_at_all BOOLEAN DEFAULT false,
    feishu_at_users TEXT,
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 添加注释
COMMENT ON TABLE notification_configs IS '通知配置表 - 简化版';
COMMENT ON COLUMN notification_configs.service_type IS '通知服务类型';
COMMENT ON COLUMN notification_configs.is_enabled IS '是否启用';
COMMENT ON COLUMN notification_configs.description IS '配置描述';
COMMENT ON COLUMN notification_configs.notification_time IS '每日通知时间';
COMMENT ON COLUMN notification_configs.email_recipients IS '邮件收件人列表(JSON)';
COMMENT ON COLUMN notification_configs.wechat_mentioned_users IS '微信@用户列表(JSON)';
COMMENT ON COLUMN notification_configs.wechat_mentioned_mobiles IS '微信@手机号列表(JSON)';
COMMENT ON COLUMN notification_configs.feishu_at_all IS '飞书是否@所有人';
COMMENT ON COLUMN notification_configs.feishu_at_users IS '飞书@用户ID列表(JSON)';
COMMENT ON COLUMN notification_configs.created_at IS '创建时间';
COMMENT ON COLUMN notification_configs.updated_at IS '更新时间';

-- 插入默认配置
INSERT INTO notification_configs (service_type, is_enabled, description) VALUES
('EMAIL', false, '邮件通知服务 - 敏感配置请在环境变量中设置'),
('WECHAT', false, '企业微信通知服务 - 敏感配置请在环境变量中设置'),
('FEISHU', false, '飞书通知服务 - 敏感配置请在环境变量中设置');
