#!/bin/bash

# 更新requirements.txt脚本

set -e

echo "📦 更新Python依赖列表..."

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "❌ 虚拟环境不存在，请先运行 ./setup.sh"
    exit 1
fi

# 激活虚拟环境
source venv/bin/activate

# 更新requirements.txt
echo "📝 生成requirements.txt..."
pip freeze > requirements.txt

echo "✅ requirements.txt 已更新"
echo "📋 当前安装的包："
cat requirements.txt

echo ""
echo "💡 提示："
echo "   - 请检查requirements.txt确保只包含必要的依赖"
echo "   - 提交代码前请确认依赖版本的兼容性"
