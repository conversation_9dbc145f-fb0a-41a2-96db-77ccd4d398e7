#!/usr/bin/env python3
"""
测试分页功能
"""
import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.scraper import TongHuaShunScraper

async def test_pagination():
    """测试分页功能"""
    scraper = TongHuaShunScraper()
    
    print("开始测试分页功能...")
    
    # 测试抓取前6页，验证新的分页逻辑
    data = await scraper.scrape_holdings_data(page=1, max_pages=6)
    
    print(f"总共抓取到 {len(data)} 条数据")
    
    # 分析数据重复情况
    unique_records = set()
    for item in data:
        key = f"{item['stock_code']}-{item['holder_name']}-{item['announcement_date']}-{item['direction']}-{item['change_shares']}-{item['change_reason']}"
        unique_records.add(key)
    
    print(f"唯一记录数: {len(unique_records)}")
    print(f"重复记录数: {len(data) - len(unique_records)}")
    
    if len(unique_records) < len(data):
        print("❌ 存在重复数据，分页功能有问题")
    else:
        print("✅ 没有重复数据，分页功能正常")
    
    # 显示前几条数据
    print("\n前5条数据:")
    for i, item in enumerate(data[:5]):
        print(f"{i+1}. {item['stock_code']} {item['stock_name']} - {item['holder_name']} - {item['announcement_date']}")

if __name__ == "__main__":
    asyncio.run(test_pagination())
