-- 初始化数据库脚本
-- 股票分析系统数据库初始化

-- 设置数据库编码
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建数据库（如果不存在）
-- 注意：在Docker环境中，数据库已经通过环境变量创建

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建用户（如果需要）
-- DO $$
-- BEGIN
--     IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'stock_user') THEN
--         CREATE ROLE stock_user LOGIN PASSWORD 'stock_password';
--     END IF;
-- END
-- $$;

-- 授权
-- GRANT ALL PRIVILEGES ON DATABASE stock_analysis TO stock_user;

-- 创建表结构（与本地数据库保持一致）

-- 创建 stocks 表
CREATE TABLE IF NOT EXISTS stocks (
    code character varying(10) NOT NULL,
    name character varying(100) NOT NULL,
    exchange character varying(10) NOT NULL,
    industry character varying(100),
    is_active boolean,
    id integer NOT NULL,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL
);

-- 创建 stocks 表的序列
CREATE SEQUENCE IF NOT EXISTS stocks_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE stocks_id_seq OWNED BY stocks.id;
ALTER TABLE ONLY stocks ALTER COLUMN id SET DEFAULT nextval('stocks_id_seq'::regclass);

-- 创建 holding_changes 表
CREATE TABLE IF NOT EXISTS holding_changes (
    id integer NOT NULL,
    stock_id integer NOT NULL,
    announcement_date date NOT NULL,
    change_date date,
    holder_name character varying(255) NOT NULL,
    holder_type character varying(50) NOT NULL,
    direction character varying(20) NOT NULL,
    change_shares numeric(20,2),
    total_shares_after numeric(20,2),
    holding_ratio_after numeric(20,4),
    price_min numeric(10,2),
    price_max numeric(10,2),
    price_avg numeric(10,2),
    change_amount numeric(20,2),
    change_reason text,
    source_url text,
    impact_score numeric(5,2),
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);

-- 创建 holding_changes 表的序列
CREATE SEQUENCE IF NOT EXISTS holding_changes_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE holding_changes_id_seq OWNED BY holding_changes.id;
ALTER TABLE ONLY holding_changes ALTER COLUMN id SET DEFAULT nextval('holding_changes_id_seq'::regclass);

-- 创建主键约束
ALTER TABLE ONLY stocks ADD CONSTRAINT stocks_pkey PRIMARY KEY (id);
ALTER TABLE ONLY holding_changes ADD CONSTRAINT holding_changes_pkey PRIMARY KEY (id);
ALTER TABLE ONLY admins ADD CONSTRAINT admins_pkey PRIMARY KEY (id);
ALTER TABLE ONLY system_configs ADD CONSTRAINT system_configs_pkey PRIMARY KEY (id);
ALTER TABLE ONLY notification_configs ADD CONSTRAINT notification_configs_pkey PRIMARY KEY (service_type);

-- 创建外键约束
ALTER TABLE ONLY holding_changes ADD CONSTRAINT holding_changes_stock_id_fkey FOREIGN KEY (stock_id) REFERENCES stocks(id);

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS ix_stocks_code ON stocks USING btree (code);
CREATE INDEX IF NOT EXISTS ix_stocks_id ON stocks USING btree (id);
CREATE INDEX IF NOT EXISTS ix_holding_changes_announcement_date ON holding_changes USING btree (announcement_date);
CREATE INDEX IF NOT EXISTS ix_holding_changes_direction ON holding_changes USING btree (direction);
CREATE INDEX IF NOT EXISTS ix_holding_changes_holder_name ON holding_changes USING btree (holder_name);
CREATE INDEX IF NOT EXISTS ix_holding_changes_holder_type ON holding_changes USING btree (holder_type);
CREATE INDEX IF NOT EXISTS ix_holding_changes_stock_id ON holding_changes USING btree (stock_id);

-- 管理员表索引
CREATE UNIQUE INDEX IF NOT EXISTS ix_admins_username ON admins USING btree (username);
CREATE UNIQUE INDEX IF NOT EXISTS ix_admins_email ON admins USING btree (email);
CREATE INDEX IF NOT EXISTS ix_admins_id ON admins USING btree (id);

-- 系统配置表索引
CREATE UNIQUE INDEX IF NOT EXISTS ix_system_configs_config_key ON system_configs USING btree (config_key);
CREATE INDEX IF NOT EXISTS ix_system_configs_id ON system_configs USING btree (id);
CREATE INDEX IF NOT EXISTS ix_system_configs_config_group ON system_configs USING btree (config_group);

-- 创建管理员表
CREATE TABLE IF NOT EXISTS admins (
    id integer NOT NULL,
    username character varying(50) NOT NULL,
    email character varying(100) NOT NULL,
    password_hash character varying(255) NOT NULL,
    display_name character varying(100) NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    is_superuser boolean DEFAULT false NOT NULL,
    last_login timestamp with time zone,
    notes text,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);

-- 创建管理员表的序列
CREATE SEQUENCE IF NOT EXISTS admins_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE admins_id_seq OWNED BY admins.id;
ALTER TABLE ONLY admins ALTER COLUMN id SET DEFAULT nextval('admins_id_seq'::regclass);

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id integer NOT NULL,
    config_key character varying(100) NOT NULL,
    config_value text NOT NULL,
    description character varying(255),
    config_type character varying(20) DEFAULT 'string' NOT NULL,
    is_editable boolean DEFAULT true NOT NULL,
    config_group character varying(50) DEFAULT 'general' NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);

-- 创建系统配置表的序列
CREATE SEQUENCE IF NOT EXISTS system_configs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE system_configs_id_seq OWNED BY system_configs.id;
ALTER TABLE ONLY system_configs ALTER COLUMN id SET DEFAULT nextval('system_configs_id_seq'::regclass);

-- 创建通知服务类型枚举
CREATE TYPE IF NOT EXISTS notificationservicetype AS ENUM ('EMAIL', 'WECHAT', 'FEISHU');

-- 创建通知配置表
CREATE TABLE IF NOT EXISTS notification_configs (
    service_type notificationservicetype NOT NULL,
    is_enabled boolean DEFAULT false NOT NULL,
    description character varying(500),
    notification_time character varying(10) DEFAULT '09:00',
    email_recipients text,
    wechat_mentioned_users text,
    wechat_mentioned_mobiles text,
    feishu_at_all boolean DEFAULT false,
    feishu_at_users text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- 创建 alembic_version 表
CREATE TABLE IF NOT EXISTS alembic_version (
    version_num character varying(32) NOT NULL
);

ALTER TABLE ONLY alembic_version ADD CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num);

-- 插入当前迁移版本
INSERT INTO alembic_version (version_num) VALUES ('6a40b856b20e') ON CONFLICT DO NOTHING;

-- 添加表注释
COMMENT ON TABLE notification_configs IS '通知配置表 - 简化版';
COMMENT ON COLUMN notification_configs.service_type IS '通知服务类型';
COMMENT ON COLUMN notification_configs.is_enabled IS '是否启用';
COMMENT ON COLUMN notification_configs.description IS '配置描述';
COMMENT ON COLUMN notification_configs.notification_time IS '每日通知时间';
COMMENT ON COLUMN notification_configs.email_recipients IS '邮件收件人列表(JSON)';
COMMENT ON COLUMN notification_configs.wechat_mentioned_users IS '微信@用户列表(JSON)';
COMMENT ON COLUMN notification_configs.wechat_mentioned_mobiles IS '微信@手机号列表(JSON)';
COMMENT ON COLUMN notification_configs.feishu_at_all IS '飞书是否@所有人';
COMMENT ON COLUMN notification_configs.feishu_at_users IS '飞书@用户ID列表(JSON)';
COMMENT ON COLUMN notification_configs.created_at IS '创建时间';
COMMENT ON COLUMN notification_configs.updated_at IS '更新时间';

-- 插入默认管理员账户（密码: admin123）
INSERT INTO admins (username, email, password_hash, display_name, is_active, is_superuser, notes)
VALUES (
    'admin',
    '<EMAIL>',
    '$2b$12$xgAOSwHlo5KGsQR/rhq6m.crWqj1S7Jvnw29U6cxX.GDLwPGLq84C',
    '系统管理员',
    true,
    true,
    '默认创建的超级管理员账户'
) ON CONFLICT (username) DO NOTHING;

-- 插入默认系统配置
INSERT INTO system_configs (config_key, config_value, description, config_type, config_group) VALUES
('scraping_enabled', 'true', '是否启用数据抓取', 'bool', 'scraping'),
('scraping_interval_minutes', '30', '数据抓取间隔（分钟）', 'int', 'scraping'),
('scraping_max_pages', '5', '每次抓取的最大页数', 'int', 'scraping'),
('data_cleanup_enabled', 'true', '是否启用数据清理', 'bool', 'maintenance'),
('data_retention_days', '730', '数据保留天数', 'int', 'maintenance'),
('impact_score_update_enabled', 'true', '是否启用影响力评分更新', 'bool', 'analysis'),
('health_check_enabled', 'true', '是否启用健康检查', 'bool', 'system'),
('log_level', 'INFO', '日志级别', 'string', 'system'),
('notification_enabled', 'true', '是否启用通知功能', 'bool', 'notification'),
('notification_daily_time', '09:00', '每日通知时间', 'string', 'notification'),
('notification_email_enabled', 'false', '是否启用邮件通知', 'bool', 'notification'),
('notification_wechat_enabled', 'false', '是否启用微信通知', 'bool', 'notification'),
('notification_feishu_enabled', 'false', '是否启用飞书通知', 'bool', 'notification')
ON CONFLICT (config_key) DO NOTHING;

-- 插入默认通知配置
INSERT INTO notification_configs (service_type, is_enabled, description) VALUES
('EMAIL', false, '邮件通知服务 - 敏感配置请在环境变量中设置'),
('WECHAT', false, '企业微信通知服务 - 敏感配置请在环境变量中设置'),
('FEISHU', false, '飞书通知服务 - 敏感配置请在环境变量中设置')
ON CONFLICT (service_type) DO NOTHING;

-- 输出初始化信息
DO $$
BEGIN
    RAISE NOTICE '数据库初始化完成';
    RAISE NOTICE '数据库名称: stock_analysis (容器环境)';
    RAISE NOTICE '字符编码: UTF8';
    RAISE NOTICE '时区设置: Asia/Shanghai';
    RAISE NOTICE '表结构已创建: stocks, holding_changes, admins, system_configs, notification_configs, alembic_version';
    RAISE NOTICE '所有索引和约束已创建';
    RAISE NOTICE '默认管理员账户已创建: admin/admin123';
    RAISE NOTICE '默认系统配置已插入';
    RAISE NOTICE '默认通知配置已插入';
    RAISE NOTICE '⚠️  请及时修改默认管理员密码！';
END
$$;
