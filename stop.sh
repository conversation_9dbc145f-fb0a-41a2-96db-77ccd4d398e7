#!/bin/bash

# 停止股票分析系统服务脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测Docker Compose命令
if command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
elif docker compose version &> /dev/null; then
    COMPOSE_CMD="docker compose"
else
    log_error "Docker Compose未安装或不可用"
    exit 1
fi

log_info "使用命令: $COMPOSE_CMD"

# 停止服务
log_info "正在停止所有服务..."
$COMPOSE_CMD down --remove-orphans

# 检查是否还有运行的容器
RUNNING_CONTAINERS=$(docker ps --filter "name=stock_" --format "{{.Names}}" | wc -l)

if [ "$RUNNING_CONTAINERS" -gt 0 ]; then
    log_warning "仍有相关容器在运行，尝试强制停止..."
    docker ps --filter "name=stock_" --format "{{.Names}}" | xargs -r docker stop
    docker ps --filter "name=stock_" --format "{{.Names}}" | xargs -r docker rm
fi

log_success "所有服务已停止"

# 显示状态
log_info "当前容器状态:"
docker ps --filter "name=stock_" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""
log_info "如需完全清理（包括数据卷），请运行:"
echo "  $COMPOSE_CMD down -v"
echo ""
log_info "如需重新启动服务，请运行:"
echo "  ./deploy-full.sh"
