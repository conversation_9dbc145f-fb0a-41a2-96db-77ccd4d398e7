#!/bin/bash

# 增减持数据分析平台 - 开发环境设置脚本

set -e

echo "🚀 设置增减持数据分析平台开发环境..."

# 检查Python版本
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python版本需要 >= 3.8，当前版本: $python_version"
    exit 1
fi

echo "✅ Python版本检查通过: $python_version"

# 创建虚拟环境
if [ ! -d "venv" ]; then
    echo "📦 创建Python虚拟环境..."
    python3 -m venv venv
else
    echo "✅ 虚拟环境已存在"
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source venv/bin/activate

# 升级pip
echo "⬆️ 升级pip..."
pip install --upgrade pip

# 安装依赖
echo "📚 安装Python依赖..."
pip install -r requirements.txt

# 创建环境配置文件
if [ ! -f ".env" ]; then
    echo "📝 创建环境配置文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件配置数据库连接等信息"
else
    echo "✅ 环境配置文件已存在"
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p logs
mkdir -p alembic/versions

echo ""
echo "✅ 开发环境设置完成！"
echo ""
echo "🔧 下一步操作:"
echo "1. 编辑 .env 文件配置数据库连接"
echo "2. 启动PostgreSQL数据库"
echo "3. 运行数据库迁移: alembic upgrade head"
echo "4. 启动开发服务器: python run.py"
echo ""
echo "💡 常用命令:"
echo "   激活虚拟环境: source venv/bin/activate"
echo "   退出虚拟环境: deactivate"
echo "   安装新依赖: pip install <package> && pip freeze > requirements.txt"
echo "   运行测试: pytest"
echo ""
echo "🌐 访问地址:"
echo "   API服务: http://localhost:8000"
echo "   API文档: http://localhost:8000/docs"
