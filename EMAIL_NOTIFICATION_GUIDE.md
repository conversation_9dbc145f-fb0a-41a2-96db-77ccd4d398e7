# 📧 邮件通知功能使用指南

## 🎯 功能概述

邮件通知功能为股票增减持分析平台提供了完整的邮件通知服务，支持：

- 📧 **HTML格式邮件**: 精美的邮件模板，包含数据统计和重要变动表格
- 📊 **数据可视化**: 自动生成增减持数据的HTML报告
- 📮 **多收件人支持**: 支持配置多个收件人、抄送、密送
- 🔄 **自动重试**: 发送失败时自动重试，确保邮件送达
- ⏰ **定时发送**: 每日自动发送增减持数据摘要
- 🧪 **测试功能**: 支持发送测试邮件验证配置

## 🚀 快速开始

### 1. 环境变量配置

在 `.env` 文件中添加邮件配置：

```bash
# 邮件通知配置
ENABLE_EMAIL_NOTIFICATION=true
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=增减持数据分析平台
```

### 2. 初始化数据库配置

```bash
# 运行初始化脚本
python init_simple_notification_config.py
```

### 3. 配置收件人

访问 `http://localhost:3000/notifications` 进行配置：

1. 点击邮件通知的"编辑"按钮
2. 启用邮件通知
3. 配置收件人邮箱列表
4. 保存配置

### 4. 测试邮件发送

在配置页面点击"测试"按钮，或运行测试脚本：

```bash
python test_email_holdings.py
```

## 📋 详细配置说明

### SMTP服务器配置

| 邮箱服务商 | SMTP服务器 | 端口 | 加密方式 |
|-----------|-----------|------|---------|
| QQ邮箱 | smtp.qq.com | 587 | TLS |
| 163邮箱 | smtp.163.com | 587 | TLS |
| Gmail | smtp.gmail.com | 587 | TLS |
| Outlook | smtp-mail.outlook.com | 587 | TLS |

### 获取应用专用密码

#### QQ邮箱
1. 登录QQ邮箱 → 设置 → 账户
2. 开启"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
3. 生成授权码作为密码使用

#### 163邮箱
1. 登录163邮箱 → 设置 → POP3/SMTP/IMAP
2. 开启"POP3/SMTP服务"
3. 设置客户端授权密码

#### Gmail
1. 开启两步验证
2. 生成应用专用密码
3. 使用应用密码作为SMTP密码

## 🎨 邮件模板功能

### HTML邮件模板

系统会自动为增减持数据生成精美的HTML邮件，包含：

- **数据统计概览**: 总记录数、增持/减持数量
- **重要变动表格**: 前10条重要变动详情
- **响应式设计**: 支持桌面和移动端查看
- **品牌样式**: 统一的视觉风格

### 邮件内容示例

```
📊 2025-01-01 增减持数据报告

数据概览：
• 总记录数：25 条
• 增持记录：15 条  
• 减持记录：10 条

重要变动：
1. 平安银行(000001) - 某某投资
   🔺增持 1000.00万股，变动比例：2.50%
   公告日期：2025-01-01
```

## 🔧 API接口使用

### 发送单个邮件通知

```bash
POST /api/v1/notifications/send
```

请求体：
```json
{
  "notification_type": "email",
  "priority": "normal",
  "content": {
    "title": "测试通知",
    "content": "这是一条测试消息",
    "summary": "通知测试"
  },
  "config": {
    "to_emails": ["<EMAIL>"],
    "subject": "测试邮件",
    "cc_emails": ["<EMAIL>"],
    "bcc_emails": ["<EMAIL>"]
  }
}
```

### 测试邮件配置

```bash
POST /api/v1/simple-notifications/configs/email/test
```

### 发送增减持数据邮件

```bash
POST /api/v1/notifications/send/daily-holdings
```

## 🐛 故障排除

### 常见问题

1. **邮件发送失败**
   - 检查SMTP配置是否正确
   - 确认使用应用专用密码而非登录密码
   - 验证网络连接和防火墙设置

2. **收不到邮件**
   - 检查垃圾邮件文件夹
   - 确认收件人邮箱地址正确
   - 查看应用日志获取详细错误信息

3. **HTML格式异常**
   - 检查邮件客户端是否支持HTML
   - 确认邮件内容编码正确

### 日志查看

```bash
# 查看邮件发送日志
tail -f logs/app.log | grep email

# 查看通知相关日志
grep "notification\|EmailNotificationService" logs/app.log
```

## 📈 最佳实践

1. **配置管理**
   - 使用应用专用密码而非登录密码
   - 定期更换密码确保安全
   - 备份重要的配置信息

2. **收件人管理**
   - 合理设置收件人数量
   - 使用抄送功能通知相关人员
   - 定期清理无效邮箱地址

3. **内容优化**
   - 保持邮件主题简洁明了
   - 合理使用HTML格式
   - 避免触发垃圾邮件过滤器

4. **性能优化**
   - 合理设置发送频率
   - 避免在高峰期发送大量邮件
   - 监控邮件发送成功率

## 🔮 高级功能

### 自定义邮件模板

可以通过修改 `EmailNotificationService` 类来自定义邮件模板：

```python
def _generate_html_content(self, content: NotificationContentBase) -> str:
    # 自定义HTML模板逻辑
    pass
```

### 附件支持

支持在邮件中添加附件：

```python
config = {
    'to_emails': ['<EMAIL>'],
    'subject': '带附件的邮件',
    'attachments': ['/path/to/file.pdf']
}
```

### 批量发送

支持同时发送到多个通知渠道：

```bash
POST /api/v1/notifications/send/batch
```

## 📞 技术支持

如果遇到问题，请：

1. 查看本指南的故障排除部分
2. 检查应用日志获取详细错误信息
3. 确认环境变量配置正确
4. 测试SMTP连接是否正常

---

**注意**: 请确保遵守相关法律法规和邮件服务商的使用条款，避免发送垃圾邮件。
