# 环境变量配置指南

## 📋 配置文件说明

项目使用 `.env` 文件管理环境变量配置。已为您创建了包含所有必要配置的 `.env` 文件。

## 🔧 飞书通知配置步骤

### 1. 创建飞书机器人

1. **进入飞书群聊**
   - 打开需要接收通知的飞书群
   - 点击群设置（右上角齿轮图标）
   - 选择"群机器人"

2. **添加自定义机器人**
   - 点击"添加机器人"
   - 选择"自定义机器人"
   - 填写机器人信息：
     - 名称：`增减持数据通知`
     - 描述：`自动发送每日增减持数据摘要`

3. **获取配置信息**
   - 创建完成后会显示 Webhook URL
   - 复制 URL（格式类似：`https://open.feishu.cn/open-apis/bot/v2/hook/xxxxxxxxxx`）

4. **启用签名验证（推荐）**
   - 在机器人设置中找到"签名校验"
   - 启用签名校验
   - 复制生成的签名密钥

### 2. 配置 .env 文件

打开项目根目录下的 `.env` 文件，修改以下配置：

```bash
# 启用飞书通知
ENABLE_FEISHU_NOTIFICATION=true

# 配置Webhook URL（替换为您的实际URL）
FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/your_actual_hook_id

# 配置签名密钥（推荐，提高安全性）
FEISHU_SECRET_KEY=your_actual_secret_key
```

### 3. 其他通知方式配置

#### 邮件通知配置

```bash
# 启用邮件通知
ENABLE_EMAIL_NOTIFICATION=true

# 配置SMTP服务器（以QQ邮箱为例）
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password  # 注意：使用应用专用密码，不是登录密码
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=增减持数据分析平台
```

**获取QQ邮箱应用密码**：
1. 登录QQ邮箱网页版
2. 设置 → 账户 → POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务
3. 开启SMTP服务
4. 生成授权码（应用专用密码）

#### 企业微信通知配置

```bash
# 启用企业微信通知
ENABLE_WECHAT_NOTIFICATION=true

# 配置Webhook URL
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your_key
```

**获取企业微信Webhook URL**：
1. 在企业微信群中添加机器人
2. 选择"自定义机器人"
3. 复制生成的Webhook URL

## 🚀 启动和测试

### 1. 重启服务

配置修改后需要重启后端服务：

```bash
# 停止当前服务（Ctrl+C）
# 重新启动
cd /Volumes/myextend/backend/stock
source venv/bin/activate
python run.py
```

### 2. 测试配置

访问通知配置页面进行测试：

1. **打开配置页面**: http://localhost:3000/notifications
2. **编辑通知配置**: 点击对应服务的编辑按钮
3. **启用服务**: 勾选"启用配置"
4. **测试发送**: 点击"测试"按钮验证配置

### 3. API测试

也可以通过API直接测试：

```bash
# 测试飞书签名
curl -X GET "http://localhost:8000/api/v1/feishu/test-signature"

# 发送飞书测试消息
curl -X POST "http://localhost:8000/api/v1/feishu/send-test"

# 测试所有通知服务
curl -X POST "http://localhost:8000/api/v1/notifications/test" \
  -H "Authorization: Bearer your_admin_token"
```

## 📝 配置模板

### 完整的 .env 配置示例

```bash
# ==================== 数据库配置 ====================
DATABASE_URL=postgresql://stockuser:stockdb@127.0.0.1:5432/stockdb

# ==================== API配置 ====================
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True
SECRET_KEY=your-secret-key-here
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# ==================== 管理员配置 ====================
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=1440

# ==================== 邮件通知配置 ====================
ENABLE_EMAIL_NOTIFICATION=true
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=增减持数据分析平台

# ==================== 企业微信通知配置 ====================
ENABLE_WECHAT_NOTIFICATION=true
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your_key

# ==================== 飞书通知配置 ====================
ENABLE_FEISHU_NOTIFICATION=true
FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/your_hook_id
FEISHU_SECRET_KEY=your_secret_key

# ==================== 通知时间配置 ====================
DAILY_NOTIFICATION_TIME=09:00
NOTIFICATION_TIMEZONE=Asia/Shanghai

# ==================== 数据抓取配置 ====================
SCRAPING_INTERVAL_HOURS=6
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

# ==================== 日志配置 ====================
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
```

## 🔒 安全注意事项

1. **保护敏感信息**: 
   - 不要将 `.env` 文件提交到版本控制系统
   - 生产环境使用强密码和密钥

2. **定期更换密钥**:
   - 定期更换JWT密钥
   - 定期更换飞书签名密钥
   - 定期更换邮箱应用密码

3. **权限控制**:
   - 限制 `.env` 文件的访问权限
   - 使用专用的服务账号

## 🐛 常见问题

1. **配置不生效**: 确保重启了后端服务
2. **邮件发送失败**: 检查是否使用了应用专用密码
3. **飞书消息发送失败**: 检查Webhook URL和签名密钥是否正确
4. **权限错误**: 确保使用管理员账号访问配置页面

现在您可以根据这个指南配置所有的通知服务了！🎉
