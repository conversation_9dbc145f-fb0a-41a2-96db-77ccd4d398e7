#!/usr/bin/env python3
"""
调试配置加载
"""
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_config():
    """调试配置加载"""
    print("🔧 调试配置加载...")
    print(f"当前工作目录: {os.getcwd()}")
    print(f".env 文件是否存在: {os.path.exists('.env')}")

    print("\n🔍 直接读取环境变量:")
    from dotenv import load_dotenv
    load_dotenv()

    print(f"SMTP_USERNAME (直接): {os.getenv('SMTP_USERNAME', '未设置')}")
    print(f"SMTP_PASSWORD (直接): {'*' * len(os.getenv('SMTP_PASSWORD', '')) if os.getenv('SMTP_PASSWORD') else '未设置'}")
    print(f"SMTP_FROM_EMAIL (直接): {os.getenv('SMTP_FROM_EMAIL', '未设置')}")
    print(f"ENABLE_EMAIL_NOTIFICATION (直接): {os.getenv('ENABLE_EMAIL_NOTIFICATION', '未设置')}")

    print("\n📧 重新创建配置实例:")
    try:
        from pydantic_settings import BaseSettings
    except ImportError:
        from pydantic import BaseSettings

    class TestSettings(BaseSettings):
        SMTP_HOST: str = "smtp.qq.com"
        SMTP_PORT: int = 587
        SMTP_USERNAME: str = ""
        SMTP_PASSWORD: str = ""
        SMTP_FROM_EMAIL: str = ""
        SMTP_FROM_NAME: str = "增减持数据分析平台"
        ENABLE_EMAIL_NOTIFICATION: bool = False

        class Config:
            env_file = ".env"
            case_sensitive = True

    test_settings = TestSettings()
    print(f"SMTP_USERNAME (新实例): {test_settings.SMTP_USERNAME}")
    print(f"SMTP_PASSWORD (新实例): {'*' * len(test_settings.SMTP_PASSWORD) if test_settings.SMTP_PASSWORD else '(空)'}")
    print(f"SMTP_FROM_EMAIL (新实例): {test_settings.SMTP_FROM_EMAIL}")
    print(f"ENABLE_EMAIL_NOTIFICATION (新实例): {test_settings.ENABLE_EMAIL_NOTIFICATION}")

    print("\n📧 应用配置:")
    from app.core.config import settings
    print(f"SMTP_HOST: {settings.SMTP_HOST}")
    print(f"SMTP_PORT: {settings.SMTP_PORT}")
    print(f"SMTP_USERNAME: {settings.SMTP_USERNAME}")
    print(f"SMTP_PASSWORD: {'*' * len(settings.SMTP_PASSWORD) if settings.SMTP_PASSWORD else '(空)'}")
    print(f"SMTP_FROM_EMAIL: {settings.SMTP_FROM_EMAIL}")
    print(f"SMTP_FROM_NAME: {settings.SMTP_FROM_NAME}")
    print(f"ENABLE_EMAIL_NOTIFICATION: {settings.ENABLE_EMAIL_NOTIFICATION}")

if __name__ == "__main__":
    debug_config()
