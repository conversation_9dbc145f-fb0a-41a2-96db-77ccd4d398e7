# 增减持数据分析平台

一个专业的股票增减持数据分析平台，提供实时数据抓取、智能分析和可视化展示功能。

## 🚀 功能特性

- **数据抓取**: 自动抓取同花顺增减持数据
- **智能分析**: 分析增减持事件对股价的影响
- **可视化展示**: 交互式图表展示股价变动和事件标注
- **多维筛选**: 支持按股票代码、增减持方向、变动比例等筛选
- **响应式设计**: 完美适配PC和移动端
- **实时更新**: 定时自动更新数据
- **管理后台**: 完整的管理员系统，支持系统配置和数据管理
- **权限控制**: 基于JWT的认证系统，确保数据安全
- **任务调度**: 灵活的定时任务配置和监控

## 🛠️ 技术栈

### 后端
- **FastAPI**: 现代化的Python Web框架
- **SQLAlchemy**: ORM数据库操作
- **PostgreSQL**: 主数据库
- **AkShare**: 股价数据获取
- **APScheduler**: 定时任务调度
- **Playwright**: 网页数据抓取

### 前端
- **React 18**: 现代化前端框架
- **Vite**: 快速构建工具
- **Tailwind CSS**: 实用优先的CSS框架
- **ECharts**: 专业图表库
- **Axios**: HTTP客户端

## 📦 安装部署

### 后端部署

#### 方式一：自动化设置（推荐）

```bash
git clone <repository-url>
cd stock/

# 一键设置开发环境
./setup.sh

# 启动开发服务器
./dev.sh

# 初始化管理员系统
python init_admin.py
```

#### 方式二：手动设置

1. 克隆项目
```bash
git clone <repository-url>
cd stock/
```

2. 创建并激活虚拟环境
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows
```

3. 安装依赖
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

4. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

5. 初始化数据库
```bash
alembic upgrade head
```

6. 启动服务
```bash
python run.py
# 或
uvicorn app.main:app --reload
```

### 前端部署

```bash
cd ../frontend/
npm install
npm run dev
```

## 🔐 管理员系统

### 初始化管理员账户

首次部署后，运行以下命令创建默认管理员账户：

```bash
python init_admin.py
```

默认账户信息：
- 用户名: `admin`
- 密码: `admin123`

**⚠️ 重要：登录后请立即修改默认密码！**

### 管理后台访问

- 管理员登录: `http://localhost:3000/admin/login`
- 管理仪表板: `http://localhost:3000/admin/dashboard`

### 主要功能

1. **系统设置**: 配置定时任务参数、抓取间隔等
2. **数据管理**: 手动抓取、重复记录清理、数据统计
3. **任务调度**: 监控和控制定时任务执行
4. **权限控制**: 管理员认证和权限验证

### 功能测试

运行管理员系统功能测试：

```bash
python test_admin_system.py
```

详细使用说明请参考 [管理员系统使用指南](ADMIN_GUIDE.md)

## 📊 API文档

启动后端服务后，访问 `http://localhost:8000/docs` 查看自动生成的API文档。

## 🔧 开发指南

### 虚拟环境管理

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# 快速激活（Linux/Mac）
./activate.sh

# 退出虚拟环境
deactivate

# 安装新依赖
pip install <package-name>
pip freeze > requirements.txt
```

### 项目结构
```
stock/
├── venv/             # Python虚拟环境
├── app/
│   ├── api/          # API路由
│   ├── core/         # 核心配置
│   ├── models/       # 数据模型
│   ├── schemas/      # Pydantic模式
│   ├── services/     # 业务逻辑
│   └── utils/        # 工具函数
├── alembic/          # 数据库迁移
├── tests/            # 测试文件
├── logs/             # 日志文件
├── setup.sh          # 环境设置脚本
├── dev.sh            # 开发启动脚本
└── activate.sh       # 快速激活脚本
```

### 常用开发命令

```bash
# 激活虚拟环境
source venv/bin/activate

# 启动开发服务器
./dev.sh

# 运行测试
pytest

# 更新依赖列表
./update_requirements.sh

# 数据库迁移
alembic revision --autogenerate -m "描述"
alembic upgrade head

# 代码格式化
black app/
flake8 app/
```

## 📈 数据源

- **增减持数据**: 同花顺财经数据
- **股价数据**: AkShare金融数据接口

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License
