"""
主要功能测试
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.main import app
from app.core.database import get_db, Base

# 测试数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建测试数据库表
Base.metadata.create_all(bind=engine)


def override_get_db():
    """覆盖数据库依赖"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)


def test_read_root():
    """测试根路径"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data


def test_health_check():
    """测试健康检查"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"


def test_get_stocks():
    """测试获取股票列表"""
    response = client.get("/api/v1/stocks/")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)


def test_search_stocks():
    """测试搜索股票"""
    response = client.get("/api/v1/stocks/search?keyword=000001")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)


def test_get_holdings():
    """测试获取增减持记录"""
    response = client.get("/api/v1/holdings/")
    assert response.status_code == 200
    data = response.json()
    assert "total" in data
    assert "items" in data


def test_get_holdings_stats():
    """测试获取增减持统计"""
    response = client.get("/api/v1/holdings/statistics/overview")
    assert response.status_code == 200
    data = response.json()
    assert "total_changes" in data


def test_get_market_overview():
    """测试获取市场概览"""
    response = client.get("/api/v1/analysis/market-overview")
    assert response.status_code == 200
    data = response.json()
    assert "statistics" in data


def test_get_scheduler_status():
    """测试获取调度器状态"""
    response = client.get("/api/v1/scraping/scheduler/status")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data


def test_get_data_sources():
    """测试获取数据源信息"""
    response = client.get("/api/v1/scraping/data-sources")
    assert response.status_code == 200
    data = response.json()
    assert "sources" in data
    assert isinstance(data["sources"], list)
