#!/usr/bin/env python3
"""
测试同花顺数据抓取功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.scraper import TongHuaShunScraper
from app.services.stock_price_service import StockPriceService
from loguru import logger


async def test_tonghuashun_scraper():
    """测试同花顺数据抓取"""
    logger.info("开始测试同花顺数据抓取功能...")
    
    scraper = TongHuaShunScraper()
    
    try:
        # 测试抓取1页数据
        logger.info("测试抓取同花顺增减持数据...")
        data = await scraper.scrape_holdings_data(page=1, max_pages=1)
        
        if data:
            logger.info(f"成功抓取到 {len(data)} 条数据")
            
            # 显示前3条数据作为示例
            for i, item in enumerate(data[:3]):
                logger.info(f"数据 {i+1}: {item}")
        else:
            logger.warning("未抓取到任何数据")
            
    except Exception as e:
        logger.error(f"抓取数据时发生错误: {e}")


def test_stock_price_service():
    """测试股价数据服务"""
    logger.info("开始测试股价数据服务...")
    
    price_service = StockPriceService()
    
    try:
        # 测试获取股票基本信息
        logger.info("测试获取股票基本信息...")
        info_result = price_service.get_stock_basic_info("000001")
        
        if info_result["success"]:
            logger.info("成功获取股票基本信息")
            logger.info(f"信息: {info_result['data']}")
        else:
            logger.warning(f"获取股票基本信息失败: {info_result['message']}")
        
        # 测试获取历史价格数据
        logger.info("测试获取历史价格数据...")
        price_result = price_service.get_stock_price_data(
            stock_code="000001",
            start_date="2024-01-01",
            end_date="2024-12-31"
        )
        
        if price_result["success"]:
            logger.info(f"成功获取 {len(price_result['data'])} 条价格数据")
            
            # 显示前3条数据作为示例
            for i, item in enumerate(price_result['data'][:3]):
                logger.info(f"价格数据 {i+1}: {item}")
        else:
            logger.warning(f"获取价格数据失败: {price_result['message']}")
        
        # 测试获取实时价格
        logger.info("测试获取实时价格...")
        realtime_result = price_service.get_realtime_price("000001")
        
        if realtime_result["success"]:
            logger.info("成功获取实时价格")
            logger.info(f"实时价格: {realtime_result['data']}")
        else:
            logger.warning(f"获取实时价格失败: {realtime_result['message']}")
            
    except Exception as e:
        logger.error(f"测试股价服务时发生错误: {e}")


async def test_combined_data():
    """测试组合数据功能"""
    logger.info("开始测试组合数据功能...")
    
    try:
        # 模拟增减持数据
        mock_holdings = [
            {
                "announcement_date": "2024-06-01",
                "change_date": "2024-06-01",
                "holder_name": "测试股东",
                "holder_type": "高管",
                "direction": "增持",
                "change_shares": 1000000,
                "change_amount": 50000000,
                "change_reason": "看好公司发展"
            }
        ]
        
        price_service = StockPriceService()
        
        # 测试获取带增减持标注的价格数据
        logger.info("测试获取带增减持标注的价格数据...")
        combined_result = price_service.get_stock_price_with_holdings(
            stock_code="000001",
            holding_changes=mock_holdings,
            days_before=30,
            days_after=30
        )
        
        if combined_result["success"]:
            logger.info(f"成功获取组合数据，共 {len(combined_result['data'])} 条记录")
            
            # 查找有增减持事件的日期
            events_found = 0
            for item in combined_result['data']:
                if item.get('holding_changes'):
                    events_found += 1
                    logger.info(f"发现增减持事件: {item['date']} - {item['holding_changes']}")
            
            logger.info(f"共发现 {events_found} 个增减持事件标注")
        else:
            logger.warning(f"获取组合数据失败: {combined_result['message']}")
            
    except Exception as e:
        logger.error(f"测试组合数据时发生错误: {e}")


def test_data_sources_info():
    """测试数据源信息"""
    logger.info("数据源信息:")
    logger.info("主要数据源: 同花顺财经数据 (https://data.10jqka.com.cn/financial/ggjy/)")
    logger.info("备用数据源: AkShare")
    logger.info("股价数据源: AkShare")
    logger.info("数据抓取策略: 优先使用同花顺，失败时回退到AkShare")


async def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("开始测试增减持数据分析平台的数据抓取功能")
    logger.info("=" * 60)
    
    # 显示数据源信息
    test_data_sources_info()
    logger.info("-" * 60)
    
    # 测试同花顺数据抓取
    await test_tonghuashun_scraper()
    logger.info("-" * 60)
    
    # 测试股价数据服务
    test_stock_price_service()
    logger.info("-" * 60)
    
    # 测试组合数据功能
    await test_combined_data()
    logger.info("-" * 60)
    
    logger.info("所有测试完成!")


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # 运行测试
    asyncio.run(main())
