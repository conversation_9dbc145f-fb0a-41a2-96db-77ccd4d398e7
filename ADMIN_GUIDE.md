# 管理员系统使用指南

## 概述

增减持数据分析平台管理员系统提供了完整的后台管理功能，包括：

- 管理员认证和权限控制
- 系统设置和定时任务配置
- 数据管理和维护
- 实时监控和状态查看

## 快速开始

### 1. 初始化管理员系统

首次部署后，运行初始化脚本创建默认管理员账户：

```bash
python init_admin.py
```

这将创建：
- 默认管理员账户（用户名: admin, 密码: admin123）
- 基础系统配置项

### 2. 登录管理后台

访问管理员登录页面：
```
http://localhost:3000/admin-login
```



```bash
python test_admin_system.py
```

## 功能模块

### 管理员认证

#### 登录
- **端点**: `POST /api/v1/admin/login`
- **功能**: 管理员登录获取访问令牌
- **参数**: 
  ```json
  {
    "username": "admin",
    "password": "admin123"
  }
  ```

#### 获取当前用户信息
- **端点**: `GET /api/v1/admin/me`
- **功能**: 获取当前登录管理员的详细信息

#### 更新密码
- **端点**: `PUT /api/v1/admin/me/password`
- **功能**: 修改当前管理员密码
- **参数**:
  ```json
  {
    "old_password": "旧密码",
    "new_password": "新密码"
  }
  ```

### 系统设置

#### 调度器配置
- **端点**: `GET/PUT /api/v1/system/config/scheduler`
- **功能**: 查看和修改定时任务配置

支持的配置项：
- `scraping_enabled`: 是否启用数据抓取
- `scraping_interval_minutes`: 抓取间隔（分钟）
- `scraping_max_pages`: 最大抓取页数
- `data_cleanup_enabled`: 是否启用数据清理
- `data_retention_days`: 数据保留天数
- `impact_score_update_enabled`: 是否启用影响力评分更新
- `health_check_enabled`: 是否启用健康检查

#### 任务控制
- **获取任务状态**: `GET /api/v1/system/scheduler/status`
- **重新加载任务**: `POST /api/v1/system/scheduler/reload`
- **控制单个任务**: `POST /api/v1/system/scheduler/jobs/{job_id}/control`

支持的任务操作：
- `trigger`: 立即触发任务
- `pause`: 暂停任务
- `resume`: 恢复任务

### 数据管理

#### 数据统计
- **端点**: `GET /api/v1/data/stats`
- **功能**: 获取数据库统计信息
- **返回**: 股票总数、增减持记录总数、最近记录数等

#### 手动数据抓取
- **端点**: `POST /api/v1/data/scraping/start`
- **功能**: 启动手动数据抓取任务
- **参数**:
  ```json
  {
    "page": 1,
    "max_pages": 5,
    "force_update": false
  }
  ```

#### 重复记录管理
- **检查重复**: `GET /api/v1/data/duplicates/check`
- **清理重复**: `DELETE /api/v1/data/duplicates/remove`

#### 数据清理
- **预览清理**: `POST /api/v1/data/cleanup/preview`
- **执行清理**: `POST /api/v1/data/cleanup/execute`

## 权限控制

### 管理员权限
所有管理功能都需要管理员权限，包括：
- 数据抓取控制
- 系统配置修改
- 数据维护操作
- 任务调度管理

### 超级管理员权限
某些高级功能需要超级管理员权限：
- 创建/删除管理员账户
- 修改系统核心配置
- 删除系统配置项

### 权限验证
API使用JWT Bearer Token进行认证：
```
Authorization: Bearer <access_token>
```

## 前端管理界面

### 管理员登录页面
- **路径**: `/admin/login`
- **功能**: 管理员登录入口

### 管理仪表板
- **路径**: `/admin/dashboard`
- **功能**: 系统概览和快捷操作

### 系统设置页面
- **路径**: `/admin/system-settings`
- **功能**: 调度器配置管理

### 数据管理页面
- **路径**: `/admin/data-management`
- **功能**: 数据统计、抓取控制、维护操作

## 安全建议

### 1. 修改默认密码
首次登录后立即修改默认管理员密码：
```bash
curl -X PUT http://localhost:8000/api/v1/admin/me/password \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"old_password": "admin123", "new_password": "your-secure-password"}'
```

````
curl -X PUT http://localhost:8000/api/v1/admin/me/password \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImFkbWluX2lkIjoxLCJ0eXBlIjoiYWRtaW4iLCJleHAiOjE3NTM3ODI0Mjd9.JssAakmvOeief-wWWk7VcBAhZ8Z9PazEa8RnG0JALiE" \
  -H "Content-Type: application/json" \
  -d '{"old_password": "admin123", "new_password": "Jkl12345678910@"}'
```

### 2. 定期更新密码
建议定期更新管理员密码，使用强密码策略。

### 3. 监控访问日志
定期检查系统日志，监控异常访问行为。

### 4. 备份配置
定期备份系统配置和管理员账户信息。

## 故障排除

### 1. 登录失败
- 检查用户名和密码是否正确
- 确认管理员账户是否已激活
- 检查数据库连接是否正常

### 2. 权限错误
- 确认使用了正确的访问令牌
- 检查令牌是否已过期
- 验证管理员账户权限级别

### 3. 配置更新失败
- 检查配置值是否在有效范围内
- 确认管理员有修改权限
- 查看系统日志获取详细错误信息

### 4. 任务调度问题
- 检查调度器是否正在运行
- 验证任务配置是否正确
- 查看任务执行日志

## API文档

完整的API文档可以通过以下地址访问：
- Swagger UI: `http://your-domain/docs`
- ReDoc: `http://your-domain/redoc`

## 技术支持

如遇到问题，请：
1. 查看系统日志文件
2. 运行测试脚本验证功能
3. 检查数据库连接状态
4. 确认服务配置正确

## 更新日志

### v1.0.0
- 初始版本发布
- 基础管理员认证功能
- 系统设置和任务调度
- 数据管理和维护功能
