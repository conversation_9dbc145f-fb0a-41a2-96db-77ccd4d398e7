#!/bin/bash

# 完整系统Docker部署脚本
# 股票分析系统 - 前后端 + 数据库一键部署

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 配置变量
PROJECT_NAME="stock-analysis"
COMPOSE_FILE="docker-compose.yml"

# 检查必要工具
check_requirements() {
    log_step "检查系统要求..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        echo "安装指南: https://docs.docker.com/get-docker/"
        exit 1
    fi
    log_success "Docker已安装: $(docker --version)"
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        echo "安装指南: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    if command -v docker-compose &> /dev/null; then
        log_success "Docker Compose已安装: $(docker-compose --version)"
        COMPOSE_CMD="docker-compose"
    else
        log_success "Docker Compose已安装: $(docker compose version)"
        COMPOSE_CMD="docker compose"
    fi
    
    # 检查必要文件
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "docker-compose.yml文件不存在"
        exit 1
    fi
    
    if [ ! -f "Dockerfile" ]; then
        log_error "后端Dockerfile文件不存在"
        exit 1
    fi
    
    if [ ! -f "../frontend/Dockerfile" ]; then
        log_error "前端Dockerfile文件不存在"
        exit 1
    fi
    
    log_success "所有必要文件检查通过"
}

# 检查端口占用
check_ports() {
    log_step "检查端口占用..."
    
    ports=(3000 8000 5433 6379)
    occupied_ports=()
    
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            occupied_ports+=($port)
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        log_warning "以下端口被占用: ${occupied_ports[*]}"
        log_warning "这可能会导致服务启动失败"
        read -p "是否继续部署? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "部署已取消"
            exit 0
        fi
    else
        log_success "所有端口可用"
    fi
}

# 清理旧容器和镜像
cleanup() {
    log_step "清理旧容器和镜像..."
    
    # 停止并删除容器
    $COMPOSE_CMD down --remove-orphans 2>/dev/null || true
    
    # 删除相关镜像（可选）
    if [ "${1:-}" = "clean-images" ]; then
        log_info "删除相关镜像..."
        docker images | grep -E "(stock-|postgres|redis|nginx)" | awk '{print $3}' | xargs -r docker rmi -f 2>/dev/null || true
    fi
    
    log_success "清理完成"
}

# 构建和启动服务
deploy_services() {
    log_step "构建和启动服务..."
    
    # 构建镜像
    log_info "构建Docker镜像..."
    $COMPOSE_CMD build --no-cache
    
    # 启动服务
    log_info "启动服务..."
    $COMPOSE_CMD up -d
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_step "等待服务就绪..."
    
    services=("postgres" "redis" "backend" "frontend")
    
    for service in "${services[@]}"; do
        log_info "等待 $service 服务就绪..."
        
        # 等待容器启动
        for i in {1..60}; do
            if $COMPOSE_CMD ps | grep -q "${service}.*Up"; then
                break
            fi
            if [ $i -eq 60 ]; then
                log_error "$service 服务启动超时"
                show_logs $service
                exit 1
            fi
            sleep 2
        done
        
        # 等待健康检查通过
        for i in {1..30}; do
            health_status=$($COMPOSE_CMD ps --format json | jq -r ".[] | select(.Service==\"$service\") | .Health" 2>/dev/null || echo "")
            if [ "$health_status" = "healthy" ] || [ "$health_status" = "" ]; then
                log_success "$service 服务就绪"
                break
            fi
            if [ $i -eq 30 ]; then
                log_warning "$service 健康检查超时，但服务可能仍在启动中"
                break
            fi
            sleep 2
        done
    done
}

# 显示服务状态
show_status() {
    log_step "服务状态检查..."
    
    echo ""
    log_info "容器状态:"
    $COMPOSE_CMD ps
    
    echo ""
    log_info "网络信息:"
    docker network ls | grep stock
    
    echo ""
    log_info "数据卷信息:"
    docker volume ls | grep stock
}

# 显示日志
show_logs() {
    local service=${1:-}
    if [ -n "$service" ]; then
        log_info "$service 服务日志:"
        $COMPOSE_CMD logs --tail 20 $service
    else
        log_info "所有服务日志:"
        $COMPOSE_CMD logs --tail 10
    fi
}

# 运行数据库迁移
run_migrations() {
    log_step "运行数据库迁移..."
    
    # 等待数据库完全就绪
    sleep 10
    
    # 运行Alembic迁移
    if $COMPOSE_CMD exec -T backend python -c "import alembic" 2>/dev/null; then
        log_info "运行数据库迁移..."
        $COMPOSE_CMD exec -T backend alembic upgrade head || log_warning "数据库迁移失败，请手动检查"
    else
        log_warning "Alembic未安装，跳过数据库迁移"
    fi
}

# 显示访问信息
show_access_info() {
    echo ""
    log_success "=== 部署完成 ==="
    echo ""
    echo "🌐 访问地址:"
    echo "   前端应用: http://localhost:3000"
    echo "   后端API:  http://localhost:8000"
    echo "   API文档:  http://localhost:8000/docs"
    echo "   健康检查: http://localhost:8000/health"
    echo ""
    echo "🗄️  数据库连接:"
    echo "   PostgreSQL: localhost:5432"
    echo "   Redis:      localhost:6379"
    echo "   数据库名:   stock_analysis"
    echo "   用户名:     postgres"
    echo "   密码:       password123"
    echo ""
    echo "📋 常用命令:"
    echo "   查看状态: $COMPOSE_CMD ps"
    echo "   查看日志: $COMPOSE_CMD logs -f [service_name]"
    echo "   停止服务: $COMPOSE_CMD stop"
    echo "   重启服务: $COMPOSE_CMD restart"
    echo "   完全清理: $COMPOSE_CMD down -v"
    echo ""
    echo "🔧 管理命令:"
    echo "   进入后端: $COMPOSE_CMD exec backend bash"
    echo "   进入数据库: $COMPOSE_CMD exec postgres psql -U postgres -d stock_analysis"
    echo "   查看Redis: $COMPOSE_CMD exec redis redis-cli"
}

# 主函数
main() {
    echo ""
    log_success "=== 股票分析系统 Docker 部署脚本 ==="
    echo ""
    
    check_requirements
    check_ports
    cleanup
    deploy_services
    wait_for_services
    run_migrations
    show_status
    show_access_info
    
    echo ""
    log_success "🎉 系统部署完成！"
}

# 处理命令行参数
case "${1:-}" in
    "up"|"start")
        main
        ;;
    "down"|"stop")
        log_info "停止所有服务..."
        # 检查COMPOSE_CMD是否已设置
        if [ -z "$COMPOSE_CMD" ]; then
            if command -v docker-compose &> /dev/null; then
                COMPOSE_CMD="docker-compose"
            else
                COMPOSE_CMD="docker compose"
            fi
        fi
        $COMPOSE_CMD down --remove-orphans
        log_success "服务已停止"
        ;;
    "restart")
        log_info "重启所有服务..."
        $COMPOSE_CMD restart
        wait_for_services
        show_access_info
        ;;
    "clean")
        cleanup clean-images
        ;;
    "logs")
        show_logs ${2:-}
        ;;
    "status")
        show_status
        ;;
    "migrate")
        run_migrations
        ;;
    "help"|"-h"|"--help")
        echo "用法: $0 [命令]"
        echo ""
        echo "命令:"
        echo "  up/start    - 启动所有服务（默认）"
        echo "  down/stop   - 停止所有服务"
        echo "  restart     - 重启所有服务"
        echo "  clean       - 清理容器和镜像"
        echo "  logs [服务] - 查看日志"
        echo "  status      - 查看服务状态"
        echo "  migrate     - 运行数据库迁移"
        echo "  help        - 显示帮助信息"
        ;;
    *)
        main
        ;;
esac
