# 通知模块使用指南

## 概述

通知模块为股票增减持系统提供了多渠道的通知服务，支持邮箱、企业微信、飞书三种通知方式。系统可以自动发送每日增减持数据报告，也支持手动触发通知。

## 功能特性

- 📧 **邮件通知**: 支持HTML格式的精美邮件，包含数据统计和重要变动表格
- 💬 **企业微信通知**: 支持Markdown格式的富文本消息，可@指定用户
- 🚀 **飞书通知**: 支持富文本和卡片格式，颜色丰富的数据展示
- ⏰ **定时发送**: 每日自动发送增减持数据摘要
- 🔄 **失败重试**: 自动重试机制，确保通知送达
- 🎯 **批量发送**: 支持同时发送到多个通知渠道

## 配置说明

### 1. 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# 邮件通知配置
ENABLE_EMAIL_NOTIFICATION=true
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=增减持数据分析平台

# 企业微信通知配置
ENABLE_WECHAT_NOTIFICATION=true
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your_key

# 飞书通知配置
ENABLE_FEISHU_NOTIFICATION=true
FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/your_hook_id

# 通知时间配置
DAILY_NOTIFICATION_TIME=09:00
NOTIFICATION_TIMEZONE=Asia/Shanghai
```

### 2. 获取Webhook URL

#### 企业微信机器人
1. 在企业微信群中添加机器人
2. 获取Webhook URL
3. 配置到 `WECHAT_WEBHOOK_URL`

#### 飞书机器人
1. 在飞书群中添加自定义机器人
2. 获取Webhook URL
3. 配置到 `FEISHU_WEBHOOK_URL`

## API接口使用

### 1. 发送单个通知

```bash
POST /api/v1/notifications/send
```

请求体示例：
```json
{
  "notification_type": "email",
  "priority": "normal",
  "content": {
    "title": "测试通知",
    "content": "这是一条测试消息",
    "summary": "通知测试"
  },
  "config": {
    "to_emails": ["<EMAIL>"],
    "subject": "测试邮件"
  }
}
```

### 2. 批量发送通知

```bash
POST /api/v1/notifications/send/batch
```

请求体示例：
```json
{
  "notification_types": ["email", "wechat", "feishu"],
  "content": {
    "title": "批量通知测试",
    "content": "这是一条批量测试消息"
  },
  "priority": "normal"
}
```

### 3. 发送每日增减持通知

```bash
POST /api/v1/notifications/send/daily-holdings
```

请求体示例：
```json
{
  "target_date": "2025-06-30"
}
```

### 4. 测试通知服务

```bash
POST /api/v1/notifications/test
```

### 5. 获取服务状态

```bash
GET /api/v1/notifications/services/status
```

## 定时任务配置

系统会自动添加每日通知任务，默认在每天09:00发送前一天的增减持数据。

### 查看定时任务状态

```bash
GET /api/v1/system/scheduler/status
```

### 手动触发通知任务

```bash
POST /api/v1/system/scheduler/jobs/daily_notification/control
```

请求体：
```json
{
  "action": "trigger"
}
```

## 通知内容格式

### 邮件格式
- HTML格式，包含样式和表格
- 数据统计概览
- 重要变动详细列表
- 响应式设计，支持移动端查看

### 微信格式
- Markdown格式
- 支持颜色标记
- 简洁的数据展示
- 支持@用户功能

### 飞书格式
- 富文本格式
- 彩色数据展示
- 表格化重要变动
- 支持卡片消息

## 故障排除

### 1. 邮件发送失败
- 检查SMTP配置是否正确
- 确认邮箱密码是否为应用专用密码
- 检查网络连接

### 2. 微信通知失败
- 确认Webhook URL是否正确
- 检查机器人是否被移除
- 验证消息格式是否符合要求

### 3. 飞书通知失败
- 确认Webhook URL是否有效
- 检查机器人权限设置
- 验证消息内容是否超出限制

### 4. 定时任务不执行
- 检查调度器是否启动
- 确认任务配置是否正确
- 查看系统日志获取详细错误信息

## 最佳实践

1. **测试配置**: 部署前先使用测试接口验证配置
2. **监控日志**: 定期检查通知发送日志
3. **备用方案**: 配置多个通知渠道作为备用
4. **内容优化**: 根据接收者需求调整通知内容格式
5. **时间设置**: 选择合适的通知发送时间

## 扩展开发

如需添加新的通知渠道：

1. 继承 `BaseNotificationService` 类
2. 实现 `send_notification` 和 `validate_config` 方法
3. 在 `NotificationManager` 中注册新服务
4. 添加相应的配置项和API接口

示例代码结构：
```python
class CustomNotificationService(BaseNotificationService):
    async def send_notification(self, content, config, priority):
        # 实现发送逻辑
        pass
    
    def validate_config(self, config):
        # 实现配置验证
        pass
```
