#!/usr/bin/env python3
"""
测试发送增减持数据到飞书
"""
import asyncio
from datetime import datetime, date
from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.holdings import HoldingChange, Stock
from app.services.notifications.notification_manager import notification_manager
from app.schemas.notifications import NotificationContentBase, NotificationType, NotificationPriority


async def test_send_holdings_to_feishu():
    """测试发送增减持数据到飞书"""
    
    db = SessionLocal()
    try:
        # 获取最近3天的增减持数据
        recent_date = date(2025, 6, 27)  # 使用有数据的日期
        holdings = db.query(HoldingChange).join(Stock).filter(
            HoldingChange.announcement_date >= recent_date
        ).order_by(HoldingChange.announcement_date.desc()).limit(10).all()
        
        if not holdings:
            print("❌ 没有找到最近的增减持数据")
            return
        
        print(f"📊 找到 {len(holdings)} 条增减持数据，准备发送到飞书...")
        
        # 构造增减持数据内容
        holdings_data = []
        for holding in holdings:
            stock = holding.stock
            # 转换方向为英文
            direction_en = "INCREASE" if holding.direction == 'increase' else "DECREASE"
            direction_emoji = "📈" if holding.direction == 'increase' else "📉"

            holdings_data.append({
                'stock_code': stock.code,
                'stock_name': stock.name,
                'holder_name': holding.holder_name,
                'direction': holding.direction,
                'direction_en': direction_en,
                'direction_emoji': direction_emoji,
                'change_shares': holding.change_shares,
                'change_amount': holding.change_amount,
                'announcement_date': holding.announcement_date.strftime('%Y-%m-%d'),
                'holding_ratio_after': holding.holding_ratio_after
            })
        
        # 构造简单文本内容
        text_content = f"📈 股票增减持信息推送\n\n"
        text_content += f"📊 数据概览：\n"
        text_content += f"• 总记录数：{len(holdings_data)} 条\n"
        text_content += f"• 涉及股票：{len(set(h['stock_name'] for h in holdings_data))} 只\n"
        text_content += f"• 报告时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

        text_content += f"📋 详细信息：\n"
        for i, holding in enumerate(holdings_data[:5], 1):  # 只显示前5条
            direction_emoji = "📈" if holding['direction'] == 'increase' else "📉"
            direction_text = "增持" if holding['direction'] == 'increase' else "减持"

            text_content += f"{i}. {direction_emoji} {holding['stock_name']}({holding['stock_code']})\n"
            text_content += f"   持有人：{holding['holder_name']}\n"
            text_content += f"   变动：{direction_text} {holding['change_shares']:,.0f}股\n"

            # 变动比例单独一行
            if holding['holding_ratio_after'] is not None:
                text_content += f"   变动比例：{holding['holding_ratio_after']:.2f}%\n"

            text_content += f"   金额：{holding['change_amount']:,.0f}万元\n"
            text_content += f"   日期：{holding['announcement_date']}\n\n"

        if len(holdings_data) > 5:
            text_content += f"... 还有 {len(holdings_data) - 5} 条记录\n"

        # 构造通知内容（不包含data字段，使用简单文本格式）
        content = NotificationContentBase(
            title="📈 股票增减持信息推送",
            content=text_content,
            summary=f"共涉及 {len(set(h['stock_name'] for h in holdings_data))} 只股票的增减持变动"
        )
        
        print("🚀 开始发送到飞书...")
        
        # 发送到飞书
        result = await notification_manager.send_notification(
            notification_type=NotificationType.FEISHU,
            content=content,
            config={},  # 使用环境变量配置
            priority=NotificationPriority.NORMAL
        )
        
        if result.success:
            print("✅ 增减持数据发送成功！")
            print(f"📝 发送结果: {result.message}")
        else:
            print("❌ 增减持数据发送失败！")
            print(f"📝 错误信息: {result.message}")
            
    except Exception as e:
        print(f"❌ 发送过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


if __name__ == "__main__":
    print("🧪 开始测试发送增减持数据到飞书...")
    asyncio.run(test_send_holdings_to_feishu())
