#!/usr/bin/env python3
"""
测试飞书签名算法
"""
import hashlib
import base64
import hmac
import time

def gen_sign(timestamp, secret):
    # 拼接timestamp和secret
    string_to_sign = '{}\n{}'.format(timestamp, secret)
    hmac_code = hmac.new(string_to_sign.encode("utf-8"), digestmod=hashlib.sha256).digest()

    # 对结果进行base64处理
    sign = base64.b64encode(hmac_code).decode('utf-8')

    return sign

if __name__ == "__main__":
    # 使用当前时间戳和配置中的密钥进行测试
    timestamp = str(int(time.time()))
    secret = "gU3LvhIl9Vqfam6ubFMOGb"
    
    print(f"时间戳: {timestamp}")
    print(f"密钥: {secret}")
    
    sign = gen_sign(timestamp, secret)
    print(f"生成的签名: {sign}")
    
    # 验证签名字符串
    string_to_sign = f"{timestamp}\n{secret}"
    print(f"待签名字符串: {repr(string_to_sign)}")
