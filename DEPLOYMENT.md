# 股票分析系统 Docker 部署指南

## 概述

本项目提供了完整的 Docker 部署方案，包括前端、后端、数据库和缓存服务。支持一键部署和分别部署。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx (可选)   │    │   Frontend      │    │   Backend       │
│   Port: 80/443  │    │   Port: 3000    │    │   Port: 8000    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────┬─────┴─────┬─────────────────┐
         │                 │           │                 │
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│   PostgreSQL    │ │     Redis       │ │   Docker        │
│   Port: 5432    │ │   Port: 6379    │ │   Network       │
└─────────────────┘ └─────────────────┘ └─────────────────┘
```

## 部署方式

### 1. 一键完整部署（推荐）

使用 `deploy-full.sh` 脚本一键部署整个系统：

```bash
# 完整部署
./deploy-full.sh

# 或者
./deploy-full.sh up
```

### 2. 分别部署

#### 后端部署
```bash
# 进入后端目录
cd /path/to/backend/stock

# 运行后端部署脚本
./deploy-backend.sh
```

#### 前端部署
```bash
# 进入前端目录
cd /path/to/frontend

# 运行前端部署脚本
./deploy-frontend.sh
```

### 3. Docker Compose 部署

```bash
# 启动所有服务
docker-compose up -d

# 启动包含 Nginx 的完整服务
docker-compose --profile nginx up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 部署脚本说明

### deploy-full.sh（完整部署脚本）

**功能：**
- 检查系统要求（Docker、Docker Compose）
- 检查端口占用情况
- 清理旧容器和镜像
- 构建和启动所有服务
- 等待服务就绪
- 运行数据库迁移
- 显示访问信息

**命令参数：**
```bash
./deploy-full.sh [命令]

命令选项：
  up/start    - 启动所有服务（默认）
  down/stop   - 停止所有服务
  restart     - 重启所有服务
  clean       - 清理容器和镜像
  logs [服务] - 查看日志
  status      - 查看服务状态
  migrate     - 运行数据库迁移
  help        - 显示帮助信息
```

### deploy-backend.sh（后端部署脚本）

**功能：**
- 构建后端 Docker 镜像
- 创建 Docker 网络
- 启动后端容器
- 健康检查

**命令参数：**
```bash
./deploy-backend.sh [命令]

命令选项：
  build    - 仅构建镜像
  run      - 仅运行容器
  restart  - 重启容器
  stop     - 停止容器
  logs     - 查看日志
  clean    - 清理容器和镜像
```

### deploy-frontend.sh（前端部署脚本）

**功能：**
- 构建前端 Docker 镜像
- 启动前端容器
- 健康检查

**命令参数：**
```bash
./deploy-frontend.sh [命令]

命令选项：
  build        - 仅构建镜像
  local-build  - 本地构建（需要 Node.js）
  run          - 仅运行容器
  restart      - 重启容器
  stop         - 停止容器
  logs         - 查看日志
  clean        - 清理容器和镜像
```

## 服务配置

### 端口映射

| 服务 | 容器端口 | 主机端口 | 说明 |
|------|----------|----------|------|
| Frontend | 80 | 3000 | 前端应用 |
| Backend | 8000 | 8000 | 后端API |
| PostgreSQL | 5432 | 5432 | 数据库 |
| Redis | 6379 | 6379 | 缓存 |
| Nginx | 80/443 | 80/443 | 反向代理（可选） |

### 环境变量

**后端环境变量：**
- `DATABASE_URL`: PostgreSQL 连接字符串
- `REDIS_URL`: Redis 连接字符串
- `DEBUG`: 调试模式（False）
- `API_HOST`: API 主机地址（0.0.0.0）
- `API_PORT`: API 端口（8000）

**数据库环境变量：**
- `POSTGRES_DB`: 数据库名（stock_analysis）
- `POSTGRES_USER`: 用户名（postgres）
- `POSTGRES_PASSWORD`: 密码（password123）

## 访问地址

部署完成后，可以通过以下地址访问：

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

## 常用管理命令

### 查看服务状态
```bash
docker-compose ps
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
```

### 进入容器
```bash
# 进入后端容器
docker-compose exec backend bash

# 进入数据库容器
docker-compose exec postgres psql -U postgres -d stock_analysis

# 进入Redis容器
docker-compose exec redis redis-cli
```

### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart backend
```

### 停止和清理
```bash
# 停止所有服务
docker-compose stop

# 停止并删除容器
docker-compose down

# 停止并删除容器、网络、数据卷
docker-compose down -v
```

## 故障排除

### 1. 端口占用
如果遇到端口占用错误，可以：
- 修改 `docker-compose.yml` 中的端口映射
- 停止占用端口的其他服务

### 2. 容器启动失败
```bash
# 查看容器日志
docker-compose logs [service_name]

# 查看容器状态
docker-compose ps
```

### 3. 数据库连接失败
- 确保 PostgreSQL 容器正常运行
- 检查数据库连接字符串
- 等待数据库完全启动（可能需要几分钟）

### 4. 前端无法访问后端
- 检查网络配置
- 确保后端服务正常运行
- 检查防火墙设置

## 生产环境部署建议

### 1. 安全配置
- 修改默认密码
- 使用环境变量文件管理敏感信息
- 配置 HTTPS（使用 Nginx 反向代理）
- 限制数据库访问权限

### 2. 性能优化
- 配置适当的资源限制
- 使用 Redis 持久化
- 配置数据库连接池
- 启用 Gzip 压缩

### 3. 监控和日志
- 配置日志轮转
- 设置监控告警
- 定期备份数据

### 4. 高可用性
- 使用多个容器实例
- 配置负载均衡
- 设置数据库主从复制

## 更新和维护

### 更新应用
```bash
# 拉取最新代码
git pull

# 重新构建和部署
./deploy-full.sh
```

### 数据备份
```bash
# 备份数据库
docker-compose exec postgres pg_dump -U postgres stock_analysis > backup.sql

# 恢复数据库
docker-compose exec -T postgres psql -U postgres stock_analysis < backup.sql
```

### 清理旧镜像
```bash
# 清理未使用的镜像
docker image prune

# 清理所有未使用的资源
docker system prune -a
```
