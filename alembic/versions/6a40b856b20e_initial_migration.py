"""Initial migration

Revision ID: 6a40b856b20e
Revises: 
Create Date: 2025-06-24 01:13:52.203489

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6a40b856b20e'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('stocks',
    sa.Column('code', sa.String(length=10), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('exchange', sa.String(length=10), nullable=False),
    sa.Column('industry', sa.String(length=100), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_stocks_code'), 'stocks', ['code'], unique=True)
    op.create_index(op.f('ix_stocks_id'), 'stocks', ['id'], unique=False)
    op.create_table('holding_changes',
    sa.Column('stock_id', sa.Integer(), nullable=False),
    sa.Column('announcement_date', sa.Date(), nullable=False),
    sa.Column('change_date', sa.Date(), nullable=True),
    sa.Column('holder_name', sa.String(length=255), nullable=False),
    sa.Column('holder_type', sa.String(length=50), nullable=False),
    sa.Column('direction', sa.String(length=20), nullable=False),
    sa.Column('change_shares', sa.Numeric(precision=20, scale=2), nullable=True),
    sa.Column('total_shares_after', sa.Numeric(precision=20, scale=2), nullable=True),
    sa.Column('holding_ratio_after', sa.Numeric(precision=20, scale=4), nullable=True),
    sa.Column('price_min', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('price_max', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('price_avg', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('change_amount', sa.Numeric(precision=20, scale=2), nullable=True),
    sa.Column('change_reason', sa.Text(), nullable=True),
    sa.Column('source_url', sa.Text(), nullable=True),
    sa.Column('impact_score', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True, server_default=sa.text('now()')),
    sa.Column('updated_at', sa.DateTime(), nullable=True, server_default=sa.text('now()')),
    sa.ForeignKeyConstraint(['stock_id'], ['stocks.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_holding_changes_announcement_date'), 'holding_changes', ['announcement_date'], unique=False)
    op.create_index(op.f('ix_holding_changes_direction'), 'holding_changes', ['direction'], unique=False)
    op.create_index(op.f('ix_holding_changes_holder_name'), 'holding_changes', ['holder_name'], unique=False)
    op.create_index(op.f('ix_holding_changes_holder_type'), 'holding_changes', ['holder_type'], unique=False)
    op.create_index(op.f('ix_holding_changes_stock_id'), 'holding_changes', ['stock_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_holding_changes_stock_id'), table_name='holding_changes')
    op.drop_index(op.f('ix_holding_changes_holder_type'), table_name='holding_changes')
    op.drop_index(op.f('ix_holding_changes_holder_name'), table_name='holding_changes')
    op.drop_index(op.f('ix_holding_changes_direction'), table_name='holding_changes')
    op.drop_index(op.f('ix_holding_changes_announcement_date'), table_name='holding_changes')
    op.drop_table('holding_changes')
    op.drop_index(op.f('ix_stocks_id'), table_name='stocks')
    op.drop_index(op.f('ix_stocks_code'), table_name='stocks')
    op.drop_table('stocks')
    # ### end Alembic commands ###
