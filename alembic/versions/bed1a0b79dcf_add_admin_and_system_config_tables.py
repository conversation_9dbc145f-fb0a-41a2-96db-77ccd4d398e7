"""add_admin_and_system_config_tables

Revision ID: bed1a0b79dcf
Revises: 6a40b856b20e
Create Date: 2025-06-29 16:25:56.654213

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'bed1a0b79dcf'
down_revision = '6a40b856b20e'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('admins',
    sa.Column('username', sa.String(length=50), nullable=False),
    sa.Column('email', sa.String(length=100), nullable=False),
    sa.Column('password_hash', sa.String(length=255), nullable=False),
    sa.Column('display_name', sa.String(length=100), nullable=False),
    sa.Column('is_active', sa.<PERSON>(), nullable=False),
    sa.Column('is_superuser', sa.<PERSON>an(), nullable=False),
    sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_admins_email'), 'admins', ['email'], unique=True)
    op.create_index(op.f('ix_admins_id'), 'admins', ['id'], unique=False)
    op.create_index(op.f('ix_admins_username'), 'admins', ['username'], unique=True)
    op.create_table('system_configs',
    sa.Column('config_key', sa.String(length=100), nullable=False),
    sa.Column('config_value', sa.Text(), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('config_type', sa.String(length=20), nullable=False),
    sa.Column('is_editable', sa.Boolean(), nullable=False),
    sa.Column('config_group', sa.String(length=50), nullable=False),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_system_configs_config_key'), 'system_configs', ['config_key'], unique=True)
    op.create_index(op.f('ix_system_configs_id'), 'system_configs', ['id'], unique=False)
    op.alter_column('holding_changes', 'change_date',
               existing_type=sa.DATE(),
               nullable=False)
    op.alter_column('holding_changes', 'holder_name',
               existing_type=sa.VARCHAR(length=255),
               type_=sa.String(length=200),
               existing_nullable=False)
    op.alter_column('holding_changes', 'change_shares',
               existing_type=sa.NUMERIC(precision=20, scale=2),
               type_=sa.Float(),
               nullable=False)
    op.alter_column('holding_changes', 'total_shares_after',
               existing_type=sa.NUMERIC(precision=20, scale=2),
               type_=sa.Float(),
               existing_nullable=True)
    op.alter_column('holding_changes', 'holding_ratio_after',
               existing_type=sa.NUMERIC(precision=20, scale=4),
               type_=sa.Float(),
               existing_nullable=True)
    op.alter_column('holding_changes', 'price_min',
               existing_type=sa.NUMERIC(precision=10, scale=2),
               type_=sa.Float(),
               existing_nullable=True)
    op.alter_column('holding_changes', 'price_max',
               existing_type=sa.NUMERIC(precision=10, scale=2),
               type_=sa.Float(),
               existing_nullable=True)
    op.alter_column('holding_changes', 'price_avg',
               existing_type=sa.NUMERIC(precision=10, scale=2),
               type_=sa.Float(),
               existing_nullable=True)
    op.alter_column('holding_changes', 'change_amount',
               existing_type=sa.NUMERIC(precision=20, scale=2),
               type_=sa.Float(),
               existing_nullable=True)
    op.alter_column('holding_changes', 'source_url',
               existing_type=sa.TEXT(),
               type_=sa.String(length=500),
               existing_nullable=True)
    op.alter_column('holding_changes', 'impact_score',
               existing_type=sa.NUMERIC(precision=5, scale=2),
               type_=sa.Float(),
               existing_nullable=True)
    op.alter_column('holding_changes', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('holding_changes', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.drop_index(op.f('ix_holding_changes_holder_name'), table_name='holding_changes')
    op.drop_index(op.f('ix_holding_changes_stock_id'), table_name='holding_changes')
    op.create_index(op.f('ix_holding_changes_change_date'), 'holding_changes', ['change_date'], unique=False)
    op.create_index(op.f('ix_holding_changes_id'), 'holding_changes', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_holding_changes_id'), table_name='holding_changes')
    op.drop_index(op.f('ix_holding_changes_change_date'), table_name='holding_changes')
    op.create_index(op.f('ix_holding_changes_stock_id'), 'holding_changes', ['stock_id'], unique=False)
    op.create_index(op.f('ix_holding_changes_holder_name'), 'holding_changes', ['holder_name'], unique=False)
    op.alter_column('holding_changes', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('holding_changes', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('holding_changes', 'impact_score',
               existing_type=sa.Float(),
               type_=sa.NUMERIC(precision=5, scale=2),
               existing_nullable=True)
    op.alter_column('holding_changes', 'source_url',
               existing_type=sa.String(length=500),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('holding_changes', 'change_amount',
               existing_type=sa.Float(),
               type_=sa.NUMERIC(precision=20, scale=2),
               existing_nullable=True)
    op.alter_column('holding_changes', 'price_avg',
               existing_type=sa.Float(),
               type_=sa.NUMERIC(precision=10, scale=2),
               existing_nullable=True)
    op.alter_column('holding_changes', 'price_max',
               existing_type=sa.Float(),
               type_=sa.NUMERIC(precision=10, scale=2),
               existing_nullable=True)
    op.alter_column('holding_changes', 'price_min',
               existing_type=sa.Float(),
               type_=sa.NUMERIC(precision=10, scale=2),
               existing_nullable=True)
    op.alter_column('holding_changes', 'holding_ratio_after',
               existing_type=sa.Float(),
               type_=sa.NUMERIC(precision=20, scale=4),
               existing_nullable=True)
    op.alter_column('holding_changes', 'total_shares_after',
               existing_type=sa.Float(),
               type_=sa.NUMERIC(precision=20, scale=2),
               existing_nullable=True)
    op.alter_column('holding_changes', 'change_shares',
               existing_type=sa.Float(),
               type_=sa.NUMERIC(precision=20, scale=2),
               nullable=True)
    op.alter_column('holding_changes', 'holder_name',
               existing_type=sa.String(length=200),
               type_=sa.VARCHAR(length=255),
               existing_nullable=False)
    op.alter_column('holding_changes', 'change_date',
               existing_type=sa.DATE(),
               nullable=True)
    op.drop_index(op.f('ix_system_configs_id'), table_name='system_configs')
    op.drop_index(op.f('ix_system_configs_config_key'), table_name='system_configs')
    op.drop_table('system_configs')
    op.drop_index(op.f('ix_admins_username'), table_name='admins')
    op.drop_index(op.f('ix_admins_id'), table_name='admins')
    op.drop_index(op.f('ix_admins_email'), table_name='admins')
    op.drop_table('admins')
    # ### end Alembic commands ###
