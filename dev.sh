#!/bin/bash

# 开发环境启动脚本

set -e

echo "🚀 启动增减持数据分析平台开发环境..."

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "❌ 虚拟环境不存在，正在创建..."
    ./setup.sh
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source venv/bin/activate

# 检查环境配置
if [ ! -f ".env" ]; then
    echo "❌ 环境配置文件不存在，请先运行 ./setup.sh"
    exit 1
fi

# 检查数据库连接
echo "🔍 检查数据库连接..."
python -c "
import os
from sqlalchemy import create_engine
from app.core.config import settings
try:
    engine = create_engine(settings.DATABASE_URL)
    with engine.connect() as conn:
        conn.execute('SELECT 1')
    print('✅ 数据库连接正常')
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
    print('💡 请确保PostgreSQL已启动并配置正确的DATABASE_URL')
    exit(1)
"

# 运行数据库迁移
echo "🗄️ 运行数据库迁移..."
alembic upgrade head

# 启动开发服务器
echo "🌐 启动开发服务器..."
echo "📍 API服务: http://localhost:8000"
echo "📍 API文档: http://localhost:8000/docs"
echo "📍 按 Ctrl+C 停止服务"
echo ""

python run.py
