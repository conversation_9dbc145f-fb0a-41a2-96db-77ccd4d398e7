#!/usr/bin/env python3
"""
更新现有增减持记录的变动金额
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 设置环境变量
os.environ.setdefault('PYTHONPATH', project_root)

from app.core.database import get_db
from app.models.holdings import HoldingChange
from sqlalchemy.orm import Session
from loguru import logger


def update_change_amounts():
    """更新所有变动金额为空的记录"""
    db = next(get_db())
    
    try:
        # 查询所有变动金额为空但有变动股数和平均价格的记录
        records = db.query(HoldingChange).filter(
            HoldingChange.change_amount.is_(None),
            HoldingChange.change_shares.isnot(None),
            HoldingChange.price_avg.isnot(None)
        ).all()
        
        logger.info(f"找到 {len(records)} 条需要更新的记录")
        
        updated_count = 0
        for record in records:
            try:
                # 计算变动金额：变动股数(万股) * 平均价格(元) = 万元
                change_amount = abs(record.change_shares) * record.price_avg
                record.change_amount = change_amount
                updated_count += 1
                
                if updated_count % 100 == 0:
                    logger.info(f"已更新 {updated_count} 条记录")
                    
            except Exception as e:
                logger.error(f"更新记录 {record.id} 失败: {e}")
                continue
        
        # 提交更改
        db.commit()
        logger.info(f"成功更新 {updated_count} 条记录的变动金额")
        
    except Exception as e:
        logger.error(f"更新变动金额失败: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    update_change_amounts()
