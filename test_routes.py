#!/usr/bin/env python3
"""
测试API路由的脚本
用于验证修复后的路由是否正常工作
"""
import requests
import json
from typing import Dict, Any

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def test_route(method: str, endpoint: str, data: Dict[Any, Any] = None) -> Dict[str, Any]:
    """测试单个路由"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, timeout=10)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, timeout=10)
        else:
            return {"error": f"不支持的HTTP方法: {method}"}
        
        return {
            "url": url,
            "method": method,
            "status_code": response.status_code,
            "headers": dict(response.headers),
            "success": response.status_code < 400
        }
    except Exception as e:
        return {
            "url": url,
            "method": method,
            "error": str(e),
            "success": False
        }

def main():
    """主测试函数"""
    print("开始测试API路由...")
    print("=" * 50)
    
    # 测试股票相关路由
    stock_routes = [
        ("GET", "/stocks/list"),
        ("GET", "/stocks/search?keyword=000001"),
        ("GET", "/stocks/detail/000001"),
        ("GET", "/stocks/recent-changes/list"),
        ("GET", "/stocks/statistics/overview"),
    ]
    
    # 测试增减持相关路由
    holding_routes = [
        ("GET", "/holdings/list"),
        ("GET", "/holdings/recent/list"),
        ("GET", "/holdings/top/amount"),
        ("GET", "/holdings/statistics/overview"),
        ("GET", "/holdings/stock/000001"),
    ]
    
    all_routes = stock_routes + holding_routes
    
    results = []
    for method, endpoint in all_routes:
        print(f"测试: {method} {endpoint}")
        result = test_route(method, endpoint)
        results.append(result)
        
        if result.get("success"):
            print(f"  ✅ 成功 - 状态码: {result['status_code']}")
        else:
            print(f"  ❌ 失败 - 状态码: {result.get('status_code', 'N/A')}")
            if "error" in result:
                print(f"     错误: {result['error']}")
        print()
    
    # 统计结果
    success_count = sum(1 for r in results if r.get("success"))
    total_count = len(results)
    
    print("=" * 50)
    print(f"测试完成: {success_count}/{total_count} 成功")
    
    # 显示失败的路由
    failed_routes = [r for r in results if not r.get("success")]
    if failed_routes:
        print("\n失败的路由:")
        for route in failed_routes:
            print(f"  - {route['method']} {route['url']}")
            if route.get('status_code'):
                print(f"    状态码: {route['status_code']}")
            if route.get('error'):
                print(f"    错误: {route['error']}")

if __name__ == "__main__":
    main()
