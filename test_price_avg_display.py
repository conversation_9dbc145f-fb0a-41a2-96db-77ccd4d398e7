#!/usr/bin/env python3
"""
测试成交均价显示功能
验证：
1. 前端Holdings页面显示成交均价
2. 飞书推送包含成交均价
3. 详情页面显示成交均价
"""
import asyncio
from datetime import datetime, date, timedelta
from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.holdings import HoldingChange, Stock


async def test_price_avg_display():
    """测试成交均价显示功能"""
    print("💰 测试成交均价显示功能")
    print("=" * 60)
    
    # 1. 检查数据库中的成交均价数据
    print("\n📊 1. 检查数据库中的成交均价数据")
    print("-" * 30)
    
    db = SessionLocal()
    try:
        # 查询有成交均价的记录
        changes_with_price = db.query(HoldingChange).join(Stock).filter(
            HoldingChange.price_avg.isnot(None),
            HoldingChange.price_avg > 0
        ).limit(10).all()
        
        print(f"📋 找到 {len(changes_with_price)} 条有成交均价的记录:")
        for i, change in enumerate(changes_with_price, 1):
            direction_text = '增持' if change.direction == 'increase' else '减持'
            print(f"  {i}. {change.stock.name}({change.stock.code}) - {change.holder_name}")
            print(f"     方向: {direction_text}")
            print(f"     成交均价: {change.price_avg:.2f}元")
            print(f"     变动股数: {change.change_shares:,.0f}万股")
            if change.change_amount:
                print(f"     变动金额: {change.change_amount:,.0f}万元")
            print(f"     公告日期: {change.announcement_date}")
            print()
        
        # 查询没有成交均价的记录
        changes_without_price = db.query(HoldingChange).filter(
            (HoldingChange.price_avg.is_(None)) | (HoldingChange.price_avg <= 0)
        ).count()
        
        total_changes = db.query(HoldingChange).count()
        price_coverage = ((total_changes - changes_without_price) / total_changes * 100) if total_changes > 0 else 0
        
        print(f"⚠️ 没有成交均价的记录: {changes_without_price} 条")
        print(f"📊 成交均价覆盖率: {price_coverage:.1f}%")
        
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
    finally:
        db.close()
    
    # 2. 测试飞书推送包含成交均价
    print("\n📱 2. 测试飞书推送包含成交均价")
    print("-" * 30)
    
    db = SessionLocal()
    try:
        # 获取最近的增持数据（包含成交均价）
        recent_increases = db.query(HoldingChange).join(Stock).filter(
            HoldingChange.direction == 'increase',
            HoldingChange.announcement_date >= date.today() - timedelta(days=7),
            HoldingChange.price_avg.isnot(None),
            HoldingChange.price_avg > 0
        ).limit(3).all()
        
        if recent_increases:
            print(f"📊 找到 {len(recent_increases)} 条有成交均价的最近增持数据")
            
            # 显示将要推送的数据
            for i, change in enumerate(recent_increases, 1):
                print(f"  {i}. {change.stock.name}({change.stock.code})")
                print(f"     持有人: {change.holder_name}")
                print(f"     增持: {change.change_shares:,.0f}万股")
                print(f"     成交均价: {change.price_avg:.2f}元")
                if change.change_amount:
                    print(f"     金额: {change.change_amount:,.0f}万元")
                print()
            
            # 模拟飞书推送
            from app.utils.scheduler import SchedulerManager
            scheduler = SchedulerManager()
            
            await scheduler._send_new_increase_holdings_notification(db, recent_increases)
            print("✅ 飞书推送测试完成（检查飞书消息是否包含成交均价）")
        else:
            print("⚠️ 没有找到有成交均价的最近增持数据")
            
    except Exception as e:
        print(f"❌ 飞书推送测试失败: {e}")
    finally:
        db.close()
    
    # 3. 显示API数据结构
    print("\n🔧 3. API数据结构验证")
    print("-" * 30)
    
    db = SessionLocal()
    try:
        # 模拟API返回的数据结构
        sample_change = db.query(HoldingChange).join(Stock).filter(
            HoldingChange.price_avg.isnot(None),
            HoldingChange.price_avg > 0
        ).first()
        
        if sample_change:
            api_data = {
                'id': sample_change.id,
                'stock_name': sample_change.stock.name,
                'stock_code': sample_change.stock.code,
                'holder_name': sample_change.holder_name,
                'direction': sample_change.direction,
                'change_shares': sample_change.change_shares,
                'change_amount': sample_change.change_amount,
                'holding_ratio_after': sample_change.holding_ratio_after,
                'change_reason': sample_change.change_reason,
                'price_avg': sample_change.price_avg,  # 成交均价字段
                'announcement_date': sample_change.announcement_date.strftime('%Y-%m-%d')
            }
            
            print("📋 API数据结构示例:")
            for key, value in api_data.items():
                if key == 'price_avg':
                    print(f"  {key}: {value:.2f}元 ⭐")  # 标记成交均价字段
                else:
                    print(f"  {key}: {value}")
            
            print("\n✅ 确认API包含price_avg字段")
        else:
            print("⚠️ 没有找到包含成交均价的记录")
            
    except Exception as e:
        print(f"❌ API数据结构验证失败: {e}")
    finally:
        db.close()


async def show_price_analysis():
    """显示成交均价分析"""
    print("\n📈 4. 成交均价数据分析")
    print("-" * 30)
    
    db = SessionLocal()
    try:
        # 分析成交均价分布
        from sqlalchemy import func
        
        # 按价格区间统计
        price_ranges = [
            (0, 10, "10元以下"),
            (10, 50, "10-50元"),
            (50, 100, "50-100元"),
            (100, 200, "100-200元"),
            (200, float('inf'), "200元以上")
        ]
        
        print("📊 成交均价分布:")
        for min_price, max_price, label in price_ranges:
            if max_price == float('inf'):
                count = db.query(HoldingChange).filter(
                    HoldingChange.price_avg >= min_price,
                    HoldingChange.price_avg.isnot(None)
                ).count()
            else:
                count = db.query(HoldingChange).filter(
                    HoldingChange.price_avg >= min_price,
                    HoldingChange.price_avg < max_price,
                    HoldingChange.price_avg.isnot(None)
                ).count()
            print(f"  {label}: {count} 条记录")
        
        # 最高和最低成交均价
        max_price_record = db.query(HoldingChange).join(Stock).filter(
            HoldingChange.price_avg.isnot(None)
        ).order_by(HoldingChange.price_avg.desc()).first()
        
        min_price_record = db.query(HoldingChange).join(Stock).filter(
            HoldingChange.price_avg.isnot(None),
            HoldingChange.price_avg > 0
        ).order_by(HoldingChange.price_avg.asc()).first()
        
        if max_price_record:
            print(f"\n🔝 最高成交均价: {max_price_record.price_avg:.2f}元")
            print(f"   股票: {max_price_record.stock.name}({max_price_record.stock.code})")
            print(f"   持有人: {max_price_record.holder_name}")
        
        if min_price_record:
            print(f"\n🔻 最低成交均价: {min_price_record.price_avg:.2f}元")
            print(f"   股票: {min_price_record.stock.name}({min_price_record.stock.code})")
            print(f"   持有人: {min_price_record.holder_name}")
        
        # 平均成交均价
        avg_price = db.query(func.avg(HoldingChange.price_avg)).filter(
            HoldingChange.price_avg.isnot(None),
            HoldingChange.price_avg > 0
        ).scalar()
        
        if avg_price:
            print(f"\n📊 整体平均成交均价: {avg_price:.2f}元")
        
    except Exception as e:
        print(f"❌ 成交均价分析失败: {e}")
    finally:
        db.close()


async def show_implementation_summary():
    """显示实现总结"""
    print("\n🎯 成交均价显示功能实现总结")
    print("=" * 60)
    print("1. 📊 前端Holdings页面:")
    print("   • 添加了'成交均价'列到表格中")
    print("   • 右对齐显示，格式: XX.XX元")
    print("   • 没有价格时显示'-'")
    print("   • 列宽100px，适合价格显示")
    print()
    print("2. 📱 飞书推送:")
    print("   • 在增持数据通知中包含成交均价")
    print("   • 格式: '均价：XX.XX元'")
    print("   • 只在有成交均价且大于0时显示")
    print("   • 与金额、变动原因等信息一起显示")
    print()
    print("3. 📋 详情页面:")
    print("   • 已包含成交均价的详细显示")
    print("   • 使用Statistic组件美观展示")
    print("   • 精确到小数点后2位")
    print()
    print("4. 🔧 技术实现:")
    print("   • 数据库模型包含price_avg字段")
    print("   • API返回数据包含成交均价")
    print("   • 前端、飞书两个渠道都支持显示")
    print("   • 统一的数据处理和格式化逻辑")


if __name__ == "__main__":
    print("🚀 启动成交均价显示功能测试...")
    print("💰 验证前端、飞书两个渠道都显示成交均价")
    print()
    
    # 运行测试
    asyncio.run(test_price_avg_display())
    asyncio.run(show_price_analysis())
    asyncio.run(show_implementation_summary())
    
    print("\n🎉 测试完成！")
    print("💡 请检查以下内容:")
    print("  • 前端Holdings页面是否显示成交均价列")
    print("  • 飞书消息是否包含成交均价信息")
    print("  • 详情页面是否正确显示成交均价")
    print("  • 所有渠道的成交均价显示格式是否正确")
