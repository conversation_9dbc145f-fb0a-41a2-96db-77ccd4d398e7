#!/usr/bin/env python3
"""
通过API测试邮件发送
"""
import requests
import json
from datetime import datetime


def test_email_via_api():
    """通过API测试邮件发送"""
    base_url = "http://localhost:8000"
    
    print("📧 通过API测试邮件发送...")
    
    # 测试简单邮件发送
    print("\n1. 测试简单邮件发送")
    
    payload = {
        "notification_type": "email",
        "priority": "normal",
        "content": {
            "title": "🧪 API邮件通知测试",
            "content": f"""这是一条通过API发送的测试邮件

📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🔧 服务类型: EMAIL (API调用)
✅ 配置状态: 正常
🎯 测试目的: 验证API邮件发送功能

如果您收到这条邮件，说明API邮件发送功能工作正常！

---
此邮件由增减持数据分析平台API自动发送
""",
            "summary": "API邮件发送测试"
        },
        "config": {
            "to_emails": ["<EMAIL>"],
            "subject": "🧪 API邮件通知测试 - 增减持数据分析平台"
        }
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/notifications/send",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"📮 收件人: {payload['config']['to_emails']}")
        print(f"📝 主题: {payload['config']['subject']}")
        print(f"🔗 API状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功！")
            print(f"📝 响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print("❌ API调用失败！")
            print(f"📝 错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 测试HTML邮件发送
    print("\n2. 测试HTML邮件发送")
    
    html_payload = {
        "notification_type": "email",
        "priority": "normal",
        "content": {
            "title": "📊 API HTML邮件测试",
            "content": "这是一条HTML格式的API测试邮件",
            "summary": "API HTML邮件测试",
            "data": {
                "date": "2025-07-01",
                "total_count": 5,
                "increase_count": 3,
                "decrease_count": 2,
                "top_changes": [
                    {
                        "stock_name": "测试股票A",
                        "stock_code": "000001",
                        "holder_name": "测试投资者A",
                        "direction": "increase",
                        "change_shares": 1000.0,
                        "change_amount": 50000.0,
                        "change_ratio": 2.5,
                        "announcement_date": "2025-07-01"
                    },
                    {
                        "stock_name": "测试股票B",
                        "stock_code": "000002",
                        "holder_name": "测试投资者B",
                        "direction": "decrease",
                        "change_shares": 500.0,
                        "change_amount": 25000.0,
                        "change_ratio": 1.2,
                        "announcement_date": "2025-07-01"
                    }
                ],
                "summary": "今日测试数据显示增持活动较为活跃"
            }
        },
        "config": {
            "to_emails": ["<EMAIL>"],
            "subject": "📊 API HTML邮件测试 - 增减持数据分析平台"
        }
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/notifications/send",
            json=html_payload,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"📮 收件人: {html_payload['config']['to_emails']}")
        print(f"📝 主题: {html_payload['config']['subject']}")
        print(f"🔗 API状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ HTML邮件API调用成功！")
            print(f"📝 响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print("❌ HTML邮件API调用失败！")
            print(f"📝 错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")


def test_simple_notification_config():
    """测试简化通知配置"""
    base_url = "http://localhost:8000"
    
    print("\n3. 测试简化通知配置")
    
    try:
        # 测试邮件配置
        response = requests.post(
            f"{base_url}/api/v1/simple-notifications/configs/email/test",
            headers={"Content-Type": "application/json"}
        )
        
        print(f"🔗 配置测试API状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 邮件配置测试成功！")
            print(f"📝 响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print("❌ 邮件配置测试失败！")
            print(f"📝 错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")


if __name__ == "__main__":
    print("🚀 启动API邮件测试...")
    print("📧 测试目标: http://localhost:8000")
    print("📮 收件人: <EMAIL>")
    print()
    
    test_email_via_api()
    test_simple_notification_config()
    
    print("\n🎉 API测试完成！")
    print("💡 如果测试成功，请检查邮箱是否收到测试邮件")
