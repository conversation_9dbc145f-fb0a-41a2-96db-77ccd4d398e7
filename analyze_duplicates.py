#!/usr/bin/env python3
"""
分析数据库中的重复记录
"""
import sys
import os
from collections import defaultdict

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.holdings import HoldingChange
from sqlalchemy import and_

def analyze_duplicates():
    """分析重复的增减持记录"""
    db = SessionLocal()
    
    try:
        print("开始分析重复记录...")
        
        # 获取所有记录
        all_records = db.query(HoldingChange).all()
        print(f"总记录数: {len(all_records)}")
        
        # 按唯一标识分组
        groups = defaultdict(list)
        for record in all_records:
            # 创建唯一标识
            key = (
                record.stock_id,
                record.announcement_date,
                record.holder_name,
                record.direction,
                record.change_shares,
                record.change_reason
            )
            groups[key].append(record)
        
        # 统计重复情况
        unique_groups = len(groups)
        duplicate_groups = {k: v for k, v in groups.items() if len(v) > 1}
        total_duplicates = sum(len(v) - 1 for v in duplicate_groups.values())
        
        print(f"唯一记录组数: {unique_groups}")
        print(f"重复记录组数: {len(duplicate_groups)}")
        print(f"重复记录总数: {total_duplicates}")
        print(f"实际唯一记录数: {len(all_records) - total_duplicates}")
        
        # 显示重复最多的几个记录
        print("\n重复最多的记录:")
        sorted_duplicates = sorted(duplicate_groups.items(), key=lambda x: len(x[1]), reverse=True)
        for i, (key, records) in enumerate(sorted_duplicates[:5]):
            stock_id, date, holder, direction, shares, reason = key
            print(f"{i+1}. 股票ID={stock_id}, 持有人={holder}, 日期={date}, 方向={direction}")
            print(f"   变动股数={shares}, 原因={reason}")
            print(f"   重复次数: {len(records)}")
            print(f"   记录ID: {[r.id for r in records]}")
            print()
        
        return duplicate_groups
        
    except Exception as e:
        print(f"分析重复记录失败: {e}")
        raise
    finally:
        db.close()

def remove_duplicates():
    """删除重复记录，只保留第一条"""
    db = SessionLocal()
    
    try:
        duplicate_groups = analyze_duplicates()
        
        if not duplicate_groups:
            print("没有重复记录需要删除")
            return
        
        print(f"\n开始删除重复记录...")
        
        deleted_count = 0
        for key, records in duplicate_groups.items():
            # 保留第一条，删除其余的
            for record in records[1:]:
                db.delete(record)
                deleted_count += 1
        
        # 提交更改
        db.commit()
        print(f"成功删除 {deleted_count} 条重复记录")
        
        # 验证结果
        remaining_count = db.query(HoldingChange).count()
        print(f"剩余记录数: {remaining_count}")
        
    except Exception as e:
        print(f"删除重复记录失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "remove":
        remove_duplicates()
    else:
        analyze_duplicates()
