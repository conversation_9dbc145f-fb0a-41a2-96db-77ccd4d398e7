services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: stock_postgres
    environment:
      POSTGRES_DB: stock_analysis
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password123
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - stock_network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: stock_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - stock_network
    restart: unless-stopped

  # 后端API服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      network: host
    container_name: stock_backend
    env_file:
      - .env
    environment:
      - TZ=Asia/Shanghai
      - DATABASE_URL=***********************************************/stock_analysis
      - REDIS_URL=redis://redis:6379/0
      - DEBUG=False
      - API_HOST=0.0.0.0
      - API_PORT=8000
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs
    networks:
      - stock_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端Web服务
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
    container_name: stock_frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - stock_network
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: stock_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - stock_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  stock_network:
    driver: bridge
