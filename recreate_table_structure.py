#!/usr/bin/env python3
"""
重新创建表结构以解决枚举缓存问题
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.core.database import get_db

def recreate_table_structure():
    """重新创建表结构"""
    db = next(get_db())
    
    try:
        print("开始重新创建表结构...")
        
        # 1. 备份现有数据
        print("1. 备份现有数据...")
        result = db.execute(text("SELECT COUNT(*) FROM holding_changes;"))
        count = result.fetchone()[0]
        print(f"当前数据记录数: {count}")
        
        # 2. 删除表
        print("2. 删除现有表...")
        db.execute(text("DROP TABLE IF EXISTS holding_changes CASCADE;"))
        
        # 3. 删除枚举类型
        print("3. 删除现有枚举类型...")
        db.execute(text("DROP TYPE IF EXISTS changedirection CASCADE;"))
        db.execute(text("DROP TYPE IF EXISTS holdertype CASCADE;"))
        
        # 4. 重新创建表
        print("4. 重新创建表...")
        db.execute(text("""
            CREATE TABLE holding_changes (
                id SERIAL PRIMARY KEY,
                stock_id INTEGER NOT NULL REFERENCES stocks(id),
                announcement_date DATE NOT NULL,
                change_date DATE,
                holder_name VARCHAR(255) NOT NULL,
                holder_type VARCHAR(50) NOT NULL,
                direction VARCHAR(20) NOT NULL,
                change_shares DECIMAL(20,2),
                total_shares_after DECIMAL(20,2),
                holding_ratio_after DECIMAL(10,4),
                price_min DECIMAL(10,2),
                price_max DECIMAL(10,2),
                price_avg DECIMAL(10,2),
                change_amount DECIMAL(20,2),
                change_reason TEXT,
                source_url TEXT,
                impact_score DECIMAL(5,2),
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            );
        """))
        
        # 5. 创建索引
        print("5. 创建索引...")
        db.execute(text("""
            CREATE INDEX ix_holding_changes_stock_id ON holding_changes(stock_id);
        """))
        db.execute(text("""
            CREATE INDEX ix_holding_changes_announcement_date ON holding_changes(announcement_date);
        """))
        db.execute(text("""
            CREATE INDEX ix_holding_changes_direction ON holding_changes(direction);
        """))
        db.execute(text("""
            CREATE INDEX ix_holding_changes_holder_type ON holding_changes(holder_type);
        """))
        db.execute(text("""
            CREATE INDEX ix_holding_changes_holder_name ON holding_changes(holder_name);
        """))
        
        # 提交更改
        db.commit()
        print("✅ 表结构重新创建完成！")
        
        # 验证结果
        print("\n验证结果:")
        result = db.execute(text("""
            SELECT column_name, data_type, character_maximum_length
            FROM information_schema.columns
            WHERE table_name = 'holding_changes'
            AND column_name IN ('direction', 'holder_type')
            ORDER BY column_name;
        """))
        columns = result.fetchall()
        print(f"holding_changes 表的字符串列:")
        for col in columns:
            print(f"  {col[0]}: {col[1]}({col[2]})")

        result = db.execute(text("SELECT COUNT(*) FROM holding_changes;"))
        count = result.fetchone()[0]
        print(f"新表记录数: {count}")

        print("\n✅ 表已重新创建为字符串类型，数据已清空，需要重新插入测试数据")
        
    except Exception as e:
        print(f"重新创建表结构失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    recreate_table_structure()
