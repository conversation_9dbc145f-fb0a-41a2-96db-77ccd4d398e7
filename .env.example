# 增减持数据分析平台 - 环境变量配置文件
# 复制此文件为 .env 并根据实际情况修改配置

# ==================== 数据库配置 ====================
DATABASE_URL=postgresql://username:password@localhost:5432/stock_analysis

# ==================== API配置 ====================
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True

# 安全配置
SECRET_KEY=your-secret-key-here
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# ==================== 管理员配置 ====================
# 默认管理员账号
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# JWT配置
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=1440

# ==================== 数据抓取配置 ====================
SCRAPING_INTERVAL_HOURS=6
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

# ==================== 日志配置 ====================
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# ==================== 邮件通知配置 ====================
# 是否启用邮件通知
ENABLE_EMAIL_NOTIFICATION=false

# SMTP服务器配置
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=增减持数据分析平台

# ==================== 企业微信通知配置 ====================
# 是否启用企业微信通知
ENABLE_WECHAT_NOTIFICATION=false

# 企业微信机器人Webhook URL
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your_key

# ==================== 飞书通知配置 ====================
# 是否启用飞书通知
ENABLE_FEISHU_NOTIFICATION=false

# 飞书机器人Webhook URL
FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/your_hook_id

# 飞书机器人签名密钥（可选，推荐启用以提高安全性）
FEISHU_SECRET_KEY=your_secret_key

# ==================== 通知时间配置 ====================
# 每日通知发送时间 (24小时制)
DAILY_NOTIFICATION_TIME=09:00

# 时区设置
NOTIFICATION_TIMEZONE=Asia/Shanghai

# ==================== 通知加密配置 ====================
# 通知配置加密密钥 (系统会自动生成，也可以手动设置)
# NOTIFICATION_ENCRYPTION_KEY=your-encryption-key
