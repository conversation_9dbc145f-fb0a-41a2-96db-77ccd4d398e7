#!/usr/bin/env python3
"""
测试SMTP连接配置
"""
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 使用应用的配置
from app.core.config import settings

def test_smtp_connection():
    """测试SMTP连接"""
    # 从应用配置获取配置
    smtp_host = settings.SMTP_HOST
    smtp_port = settings.SMTP_PORT
    smtp_username = settings.SMTP_USERNAME
    smtp_password = settings.SMTP_PASSWORD
    from_email = settings.SMTP_FROM_EMAIL
    from_name = settings.SMTP_FROM_NAME
    
    print(f"📧 测试SMTP连接配置:")
    print(f"   服务器: {smtp_host}:{smtp_port}")
    print(f"   用户名: {smtp_username}")
    print(f"   发件人: {from_name} <{from_email}>")
    print()
    
    if not all([smtp_username, smtp_password, from_email]):
        print("❌ 缺少必要的SMTP配置，请检查环境变量:")
        print("   - SMTP_USERNAME")
        print("   - SMTP_PASSWORD") 
        print("   - SMTP_FROM_EMAIL")
        return False
    
    # 测试不同的连接方式
    test_configs = []
    
    if smtp_port == 465:
        test_configs = [
            {"name": "SSL连接", "use_ssl": True, "use_starttls": False},
            {"name": "STARTTLS连接", "use_ssl": False, "use_starttls": True}
        ]
    else:
        test_configs = [
            {"name": "STARTTLS连接", "use_ssl": False, "use_starttls": True},
            {"name": "SSL连接", "use_ssl": True, "use_starttls": False},
            {"name": "明文连接", "use_ssl": False, "use_starttls": False}
        ]
    
    for config in test_configs:
        print(f"🔧 尝试 {config['name']}...")
        
        try:
            if config['use_ssl']:
                # 使用SSL连接
                context = ssl.create_default_context()
                server = smtplib.SMTP_SSL(smtp_host, smtp_port, context=context)
            else:
                # 使用普通连接
                server = smtplib.SMTP(smtp_host, smtp_port)
                
                if config['use_starttls']:
                    # 启用STARTTLS
                    server.starttls()
            
            # 登录
            server.login(smtp_username, smtp_password)
            print(f"✅ {config['name']} 连接成功！")
            
            # 发送测试邮件
            msg = MIMEMultipart()
            msg['From'] = f"{from_name} <{from_email}>"
            msg['To'] = from_email  # 发送给自己
            msg['Subject'] = "🧪 SMTP连接测试"
            
            body = f"""这是一条SMTP连接测试邮件

📧 连接方式: {config['name']}
🔧 服务器: {smtp_host}:{smtp_port}
📅 测试时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

如果您收到这封邮件，说明SMTP配置正确！
"""
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # 发送邮件
            server.send_message(msg)
            print(f"📧 测试邮件发送成功！")
            
            server.quit()
            return True
            
        except Exception as e:
            print(f"❌ {config['name']} 失败: {str(e)}")
            continue
    
    print("❌ 所有连接方式都失败了")
    return False


def show_common_smtp_configs():
    """显示常见邮箱的SMTP配置"""
    print("\n📋 常见邮箱SMTP配置:")
    print("=" * 50)
    
    configs = [
        {
            "name": "QQ邮箱",
            "smtp_host": "smtp.qq.com",
            "ports": "587 (STARTTLS) 或 465 (SSL)",
            "note": "需要开启SMTP服务并使用授权码"
        },
        {
            "name": "163邮箱", 
            "smtp_host": "smtp.163.com",
            "ports": "587 (STARTTLS) 或 465 (SSL)",
            "note": "需要开启SMTP服务并设置客户端授权密码"
        },
        {
            "name": "Gmail",
            "smtp_host": "smtp.gmail.com", 
            "ports": "587 (STARTTLS) 或 465 (SSL)",
            "note": "需要开启两步验证并使用应用专用密码"
        },
        {
            "name": "Outlook/Hotmail",
            "smtp_host": "smtp-mail.outlook.com",
            "ports": "587 (STARTTLS)",
            "note": "使用Microsoft账户密码"
        }
    ]
    
    for config in configs:
        print(f"• {config['name']}")
        print(f"  服务器: {config['smtp_host']}")
        print(f"  端口: {config['ports']}")
        print(f"  说明: {config['note']}")
        print()


def show_troubleshooting_tips():
    """显示故障排除提示"""
    print("\n🔧 故障排除提示:")
    print("=" * 50)
    
    tips = [
        "1. 确认邮箱已开启SMTP服务",
        "2. 使用应用专用密码而非登录密码",
        "3. 检查防火墙是否阻止SMTP端口",
        "4. 确认网络连接正常",
        "5. 尝试不同的端口和加密方式",
        "6. 检查邮箱服务商的SMTP设置文档"
    ]
    
    for tip in tips:
        print(f"  {tip}")
    
    print("\n📞 如果问题持续存在:")
    print("  • 查看邮箱服务商的SMTP配置文档")
    print("  • 确认账户没有被限制SMTP访问")
    print("  • 尝试使用其他邮箱服务")


if __name__ == "__main__":
    print("🚀 SMTP连接测试工具")
    print("=" * 50)
    
    success = test_smtp_connection()
    
    if not success:
        show_common_smtp_configs()
        show_troubleshooting_tips()
    else:
        print("\n🎉 SMTP配置测试成功！")
        print("现在可以使用邮件通知功能了。")
