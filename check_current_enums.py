#!/usr/bin/env python3
"""
检查当前数据库中的枚举定义
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.core.database import get_db

def check_current_enums():
    """检查当前数据库中的枚举定义"""
    db = next(get_db())
    
    try:
        print("检查当前数据库中的枚举定义...")
        
        # 检查 ChangeDirection 枚举
        try:
            result = db.execute(text("SELECT unnest(enum_range(NULL::changedirection));"))
            direction_values = [row[0] for row in result]
            print(f"ChangeDirection 枚举值: {direction_values}")
        except Exception as e:
            print(f"ChangeDirection 枚举不存在或有错误: {e}")
        
        # 检查 HolderType 枚举
        try:
            result = db.execute(text("SELECT unnest(enum_range(NULL::holdertype));"))
            holder_type_values = [row[0] for row in result]
            print(f"HolderType 枚举值: {holder_type_values}")
        except Exception as e:
            print(f"HolderType 枚举不存在或有错误: {e}")
        
        # 检查表结构
        result = db.execute(text("""
            SELECT column_name, data_type, udt_name 
            FROM information_schema.columns 
            WHERE table_name = 'holding_changes' 
            AND column_name IN ('direction', 'holder_type')
            ORDER BY column_name;
        """))
        columns = result.fetchall()
        print(f"\nholding_changes 表的枚举列:")
        for col in columns:
            print(f"  {col[0]}: {col[1]} ({col[2]})")
        
        # 检查数据库中是否有数据
        result = db.execute(text("SELECT COUNT(*) FROM holding_changes;"))
        count = result.fetchone()[0]
        print(f"\nholding_changes 表记录数: {count}")
        
        if count > 0:
            result = db.execute(text("""
                SELECT direction, holder_type, COUNT(*) 
                FROM holding_changes 
                GROUP BY direction, holder_type 
                LIMIT 10;
            """))
            data_samples = result.fetchall()
            print(f"数据样本:")
            for sample in data_samples:
                print(f"  direction={sample[0]}, holder_type={sample[1]}, count={sample[2]}")
        
    except Exception as e:
        print(f"检查过程中出错: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_current_enums()
