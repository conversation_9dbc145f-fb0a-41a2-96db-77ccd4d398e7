# 简化通知配置管理指南

## 🎯 设计理念

新的通知配置系统采用简化设计：
- **页面配置**: 只管理启用状态和基本设置（如收件人、@用户等）
- **敏感信息**: 密码、Webhook URL等敏感信息统一在配置文件中管理
- **安全性**: 避免在数据库中存储敏感信息，提高安全性
- **简单性**: 配置界面更简洁，操作更直观

## 🔧 配置步骤

### 1. 环境变量配置

在 `.env` 文件中配置敏感信息：

```bash
# 邮件通知配置
ENABLE_EMAIL_NOTIFICATION=true
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=增减持数据分析平台

# 企业微信通知配置
ENABLE_WECHAT_NOTIFICATION=true
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your_key

# 飞书通知配置
ENABLE_FEISHU_NOTIFICATION=true
FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/your_hook_id
FEISHU_SECRET_KEY=your_secret_key  # 飞书机器人签名密钥（可选，推荐启用）

# 通知时间配置
DAILY_NOTIFICATION_TIME=09:00
NOTIFICATION_TIMEZONE=Asia/Shanghai
```

### 2. 页面配置管理

访问 http://localhost:3000/notifications 进行配置：

#### 邮件通知配置
- **启用状态**: 开启/关闭邮件通知
- **收件人列表**: 配置接收通知的邮箱地址
- **通知时间**: 设置每日发送时间

#### 企业微信配置
- **启用状态**: 开启/关闭微信通知
- **@用户列表**: 配置需要@的用户名
- **@手机号列表**: 配置需要@的手机号

#### 飞书配置
- **启用状态**: 开启/关闭飞书通知
- **@所有人**: 是否@群内所有人
- **@用户ID列表**: 配置需要@的用户ID

## 📋 飞书机器人配置详解

### 获取飞书Webhook URL

1. **进入飞书群聊**
   - 打开需要接收通知的飞书群
   - 点击群设置 → 群机器人

2. **添加自定义机器人**
   - 点击"添加机器人"
   - 选择"自定义机器人"
   - 填写机器人名称：`增减持数据通知`
   - 填写描述：`自动发送每日增减持数据摘要`

3. **获取Webhook URL**
   - 创建完成后复制Webhook URL
   - 格式：`https://open.feishu.cn/open-apis/bot/v2/hook/xxxxxxxxxx`

4. **配置签名验证（推荐）**
   - 在机器人设置中启用"签名校验"
   - 获取签名密钥（Secret Key）
   - 将密钥配置到环境变量 `FEISHU_SECRET_KEY`
   - 签名验证可以防止恶意请求，提高安全性

### 获取用户ID（用于@功能）

1. **通过飞书管理后台**
   - 登录飞书管理后台
   - 进入"通讯录" → "成员管理"
   - 查看用户详情获取用户ID

2. **通过API获取**
   ```bash
   # 示例用户ID格式
   ou_xxxxxxxxxxxxxxxxxxxxxxxx
   ```

## 🔄 配置流程

### 初始化数据库表

```bash
cd /Volumes/myextend/backend/stock
source venv/bin/activate
python -c "
from sqlalchemy import create_engine, text
from app.core.config import settings
engine = create_engine(settings.DATABASE_URL)
with open('create_simple_notification_config_table.sql', 'r') as f:
    sql = f.read()
with engine.connect() as conn:
    conn.execute(text(sql))
    conn.commit()
print('✅ 简化通知配置表创建成功')
"
```

### 页面配置操作

1. **访问配置页面**: http://localhost:3000/notifications
2. **启用通知服务**: 点击对应服务的"编辑"按钮
3. **配置基本设置**: 
   - 邮件：添加收件人邮箱
   - 微信：配置@用户和手机号
   - 飞书：配置@用户ID或选择@所有人
4. **保存配置**: 点击保存按钮
5. **测试通知**: 点击测试按钮验证配置

## 🎯 配置示例

### 邮件通知示例

**环境变量**:
```bash
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=股票数据分析平台
```

**页面配置**:
- 启用状态: ✅ 开启
- 收件人: `<EMAIL>, <EMAIL>`
- 通知时间: `09:00`

### 飞书通知示例

**环境变量**:
```bash
FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/xxxxxxxxxx
FEISHU_SECRET_KEY=your_secret_key_here  # 签名密钥，提高安全性
```

**页面配置**:
- 启用状态: ✅ 开启
- @所有人: ❌ 关闭
- @用户ID: `ou_user1, ou_user2`

## 🔐 安全优势

1. **敏感信息隔离**: 密码、URL等敏感信息不存储在数据库中
2. **环境变量管理**: 敏感配置通过环境变量管理，便于部署和维护
3. **权限控制**: 只有管理员可以访问配置页面
4. **配置分离**: 业务配置和敏感配置分离，降低泄露风险

## 🚀 使用体验

- ✅ **简单直观**: 配置界面简洁，只需要配置必要的业务参数
- ✅ **安全可靠**: 敏感信息在配置文件中管理，不会意外泄露
- ✅ **灵活配置**: 可以针对不同通知方式配置不同的接收人
- ✅ **实时生效**: 配置修改后立即生效，无需重启服务

## 🔐 飞书签名验证详解

### 签名验证的作用

飞书签名验证是一种安全机制，用于：
- **防止恶意请求**: 确保消息来源的真实性
- **数据完整性**: 验证消息在传输过程中未被篡改
- **身份认证**: 确认请求确实来自飞书服务器

### 签名算法

飞书使用 HMAC-SHA256 算法生成签名：

1. **拼接字符串**: `timestamp + "\n" + secret`
2. **计算HMAC**: 使用SHA256算法计算HMAC值
3. **Base64编码**: 对HMAC结果进行Base64编码

### 验证流程

1. **时间戳检查**: 验证请求时间戳在5分钟内
2. **签名生成**: 使用相同算法生成期望签名
3. **签名比较**: 使用安全比较函数验证签名

### 配置步骤

1. **获取签名密钥**:
   - 在飞书机器人设置中启用"签名校验"
   - 复制生成的签名密钥

2. **配置环境变量**:
   ```bash
   FEISHU_SECRET_KEY=your_secret_key_here
   ```

3. **测试签名**:
   ```bash
   # 测试签名生成
   curl -X GET "http://localhost:8000/api/v1/feishu/test-signature"

   # 发送测试消息
   curl -X POST "http://localhost:8000/api/v1/feishu/send-test"
   ```

### 安全建议

- ✅ **启用签名验证**: 生产环境强烈建议启用
- ✅ **定期更换密钥**: 定期更新签名密钥
- ✅ **监控异常**: 监控签名验证失败的请求
- ✅ **时间同步**: 确保服务器时间准确

### 故障排除

1. **签名验证失败**:
   - 检查签名密钥是否正确
   - 确认服务器时间是否准确
   - 验证算法实现是否正确

2. **时间戳超期**:
   - 检查服务器时间同步
   - 确认网络延迟是否过大

3. **配置问题**:
   - 验证环境变量是否正确设置
   - 检查机器人是否启用签名校验

现在您可以安全、简单地管理通知配置了！🎉
