# 📧 邮件通知功能配置指南

## 🎯 配置步骤

### 1. 邮箱服务配置

#### QQ邮箱配置（推荐）

1. **登录QQ邮箱** → 设置 → 账户
2. **开启服务**：找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
3. **开启SMTP服务**：点击"开启"
4. **获取授权码**：按提示发送短信获取授权码（这个授权码就是SMTP密码）

#### 163邮箱配置

1. **登录163邮箱** → 设置 → POP3/SMTP/IMAP
2. **开启SMTP服务**：勾选"POP3/SMTP服务"
3. **设置客户端授权密码**：设置一个专用密码

#### Gmail配置

1. **开启两步验证**：Google账户 → 安全性 → 两步验证
2. **生成应用专用密码**：Google账户 → 安全性 → 应用专用密码
3. **选择应用**：选择"邮件"和设备类型

### 2. 环境变量配置

编辑 `.env` 文件，修改以下配置：

```bash
# ==================== 邮件通知配置 ====================
# 启用邮件通知
ENABLE_EMAIL_NOTIFICATION=true

# SMTP服务器配置
SMTP_HOST=smtp.qq.com                    # 邮箱服务器
SMTP_PORT=587                           # 端口号
SMTP_USERNAME=<EMAIL>    # 你的真实邮箱地址
SMTP_PASSWORD=your_authorization_code   # 授权码（不是登录密码！）
SMTP_FROM_EMAIL=<EMAIL>  # 发件人邮箱
SMTP_FROM_NAME=增减持数据分析平台         # 发件人名称
```

### 3. 常见邮箱配置参数

| 邮箱服务商 | SMTP服务器 | 端口 | 说明 |
|-----------|-----------|------|------|
| QQ邮箱 | smtp.qq.com | 587 或 465 | 使用授权码 |
| 163邮箱 | smtp.163.com | 587 或 465 | 使用客户端授权密码 |
| Gmail | smtp.gmail.com | 587 或 465 | 使用应用专用密码 |
| Outlook | smtp-mail.outlook.com | 587 | 使用账户密码 |

### 4. 测试配置

配置完成后，运行测试脚本：

```bash
# 测试SMTP连接
python test_smtp_connection.py

# 测试邮件通知功能
python test_email_simple.py
```

### 5. 前端配置

1. 访问 `http://localhost:3000/notifications`
2. 找到"邮件通知"配置
3. 点击"编辑"按钮
4. 启用邮件通知
5. 配置收件人邮箱列表：
   ```json
   ["<EMAIL>", "<EMAIL>"]
   ```
6. 保存配置
7. 点击"测试"按钮验证

## 🔧 故障排除

### 常见错误及解决方案

#### 1. `[SSL: WRONG_VERSION_NUMBER] wrong version number`

**原因**：SSL/TLS配置不正确
**解决方案**：
- 尝试使用465端口（SSL）而不是587端口（STARTTLS）
- 或者尝试使用587端口但确保使用STARTTLS

#### 2. `Connection unexpectedly closed`

**原因**：
- 邮箱服务未开启SMTP
- 用户名或密码错误
- 网络连接问题

**解决方案**：
- 确认已开启SMTP服务
- 检查用户名和授权码是否正确
- 尝试使用其他网络

#### 3. `Authentication failed`

**原因**：认证失败
**解决方案**：
- 确认使用的是授权码而不是登录密码
- 检查用户名格式是否正确
- 确认邮箱账户状态正常

#### 4. `Recipient address rejected`

**原因**：收件人地址被拒绝
**解决方案**：
- 检查收件人邮箱地址格式
- 确认发件人邮箱有发送权限

### 调试步骤

1. **检查环境变量**：
   ```bash
   # 查看当前配置
   grep SMTP .env
   ```

2. **测试网络连接**：
   ```bash
   # 测试端口连通性
   telnet smtp.qq.com 587
   ```

3. **查看应用日志**：
   ```bash
   # 查看邮件发送日志
   tail -f logs/app.log | grep email
   ```

## 📝 配置示例

### QQ邮箱完整配置示例

```bash
# .env 文件
ENABLE_EMAIL_NOTIFICATION=true
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=abcdefghijklmnop  # 这是授权码，不是QQ密码
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=增减持数据分析平台
```

### 163邮箱配置示例

```bash
# .env 文件
ENABLE_EMAIL_NOTIFICATION=true
SMTP_HOST=smtp.163.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_client_password  # 客户端授权密码
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=增减持数据分析平台
```

## 🎉 验证成功

配置成功后，您应该能够：

1. ✅ SMTP连接测试通过
2. ✅ 发送测试邮件成功
3. ✅ 前端测试功能正常
4. ✅ 收到格式化的HTML邮件

## 📞 获取帮助

如果仍然遇到问题：

1. **查看详细日志**：检查应用日志中的错误信息
2. **邮箱服务商文档**：查看官方SMTP配置文档
3. **网络环境**：确认网络环境允许SMTP连接
4. **防火墙设置**：检查防火墙是否阻止相关端口

---

**重要提示**：
- 🔐 **安全性**：授权码是敏感信息，请妥善保管
- 📧 **测试**：建议先发送给自己测试
- 🔄 **更新**：定期更换授权码确保安全
