"""
管理员服务类
"""
from datetime import datetime
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status

from app.models.admin import Admin, SystemConfig
from app.schemas.admin import AdminCreate, AdminUpdate, AdminPasswordUpdate, SystemConfigCreate, SystemConfigUpdate
from app.core.auth import get_password_hash, verify_password


class AdminService:
    """管理员服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_admin_by_id(self, admin_id: int) -> Optional[Admin]:
        """根据ID获取管理员"""
        return self.db.query(Admin).filter(Admin.id == admin_id).first()
    
    def get_admin_by_username(self, username: str) -> Optional[Admin]:
        """根据用户名获取管理员"""
        return self.db.query(Admin).filter(Admin.username == username).first()
    
    def get_admin_by_email(self, email: str) -> Optional[Admin]:
        """根据邮箱获取管理员"""
        return self.db.query(Admin).filter(Admin.email == email).first()
    
    def get_admins(self, skip: int = 0, limit: int = 100) -> List[Admin]:
        """获取管理员列表"""
        return self.db.query(Admin).offset(skip).limit(limit).all()
    
    def create_admin(self, admin_data: AdminCreate) -> Admin:
        """创建管理员"""
        # 检查用户名是否已存在
        if self.get_admin_by_username(admin_data.username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
        
        # 检查邮箱是否已存在
        if self.get_admin_by_email(admin_data.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在"
            )
        
        # 创建管理员
        password_hash = get_password_hash(admin_data.password)
        db_admin = Admin(
            username=admin_data.username,
            email=admin_data.email,
            password_hash=password_hash,
            display_name=admin_data.display_name,
            is_active=admin_data.is_active,
            is_superuser=admin_data.is_superuser,
            notes=admin_data.notes
        )
        
        try:
            self.db.add(db_admin)
            self.db.commit()
            self.db.refresh(db_admin)
            return db_admin
        except IntegrityError:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="创建管理员失败，数据冲突"
            )
    
    def update_admin(self, admin_id: int, admin_data: AdminUpdate) -> Optional[Admin]:
        """更新管理员信息"""
        db_admin = self.get_admin_by_id(admin_id)
        if not db_admin:
            return None
        
        # 检查邮箱是否被其他用户使用
        if admin_data.email and admin_data.email != db_admin.email:
            existing_admin = self.get_admin_by_email(admin_data.email)
            if existing_admin and existing_admin.id != admin_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="邮箱已被其他用户使用"
                )
        
        # 更新字段
        update_data = admin_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_admin, field, value)
        
        try:
            self.db.commit()
            self.db.refresh(db_admin)
            return db_admin
        except IntegrityError:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="更新管理员失败，数据冲突"
            )
    
    def update_admin_password(self, admin_id: int, password_data: AdminPasswordUpdate) -> bool:
        """更新管理员密码"""
        db_admin = self.get_admin_by_id(admin_id)
        if not db_admin:
            return False
        
        # 验证旧密码
        if not verify_password(password_data.old_password, db_admin.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="旧密码不正确"
            )
        
        # 更新密码
        db_admin.password_hash = get_password_hash(password_data.new_password)
        self.db.commit()
        return True
    
    def authenticate_admin(self, username: str, password: str) -> Optional[Admin]:
        """验证管理员登录"""
        admin = self.get_admin_by_username(username)
        if not admin:
            return None
        
        if not admin.is_active:
            return None
        
        if not verify_password(password, admin.password_hash):
            return None
        
        # 更新最后登录时间
        admin.last_login = datetime.utcnow()
        self.db.commit()
        
        return admin
    
    def delete_admin(self, admin_id: int) -> bool:
        """删除管理员"""
        db_admin = self.get_admin_by_id(admin_id)
        if not db_admin:
            return False
        
        self.db.delete(db_admin)
        self.db.commit()
        return True


class SystemConfigService:
    """系统配置服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_config_by_key(self, config_key: str) -> Optional[SystemConfig]:
        """根据键获取配置"""
        return self.db.query(SystemConfig).filter(SystemConfig.config_key == config_key).first()
    
    def get_configs_by_group(self, config_group: str) -> List[SystemConfig]:
        """根据分组获取配置列表"""
        return self.db.query(SystemConfig).filter(SystemConfig.config_group == config_group).all()
    
    def get_all_configs(self) -> List[SystemConfig]:
        """获取所有配置"""
        return self.db.query(SystemConfig).all()
    
    def create_config(self, config_data: SystemConfigCreate) -> SystemConfig:
        """创建配置"""
        # 检查键是否已存在
        if self.get_config_by_key(config_data.config_key):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="配置键已存在"
            )
        
        db_config = SystemConfig(**config_data.dict())
        
        try:
            self.db.add(db_config)
            self.db.commit()
            self.db.refresh(db_config)
            return db_config
        except IntegrityError:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="创建配置失败，数据冲突"
            )
    
    def update_config(self, config_key: str, config_data: SystemConfigUpdate) -> Optional[SystemConfig]:
        """更新配置"""
        db_config = self.get_config_by_key(config_key)
        if not db_config:
            return None
        
        if not db_config.is_editable:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该配置不允许编辑"
            )
        
        # 更新字段
        update_data = config_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_config, field, value)
        
        self.db.commit()
        self.db.refresh(db_config)
        return db_config
    
    def delete_config(self, config_key: str) -> bool:
        """删除配置"""
        db_config = self.get_config_by_key(config_key)
        if not db_config:
            return False
        
        if not db_config.is_editable:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该配置不允许删除"
            )
        
        self.db.delete(db_config)
        self.db.commit()
        return True
