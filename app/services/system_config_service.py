"""
系统配置服务类
"""
import json
from typing import Any, Dict, List, Optional, Union
from sqlalchemy.orm import Session
from app.models.admin import SystemConfig
from app.services.admin_service import SystemConfigService


class SystemConfigManager:
    """系统配置管理器"""
    
    def __init__(self, db: Session):
        self.db = db
        self.config_service = SystemConfigService(db)
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """获取配置值并转换为正确的类型"""
        config = self.config_service.get_config_by_key(key)
        if not config:
            return default
        
        return self._convert_value(config.config_value, config.config_type)
    
    def set_config_value(self, key: str, value: Any, description: str = None, config_type: str = None) -> bool:
        """设置配置值"""
        try:
            # 获取现有配置
            config = self.config_service.get_config_by_key(key)
            
            if config:
                # 更新现有配置
                from app.schemas.admin import SystemConfigUpdate
                update_data = SystemConfigUpdate(
                    config_value=str(value),
                    description=description or config.description
                )
                self.config_service.update_config(key, update_data)
            else:
                # 创建新配置
                from app.schemas.admin import SystemConfigCreate
                create_data = SystemConfigCreate(
                    config_key=key,
                    config_value=str(value),
                    description=description or f"配置项: {key}",
                    config_type=config_type or self._infer_type(value),
                    config_group="dynamic"
                )
                self.config_service.create_config(create_data)
            
            return True
        except Exception:
            return False
    
    def get_scheduler_config(self) -> Dict[str, Any]:
        """获取调度器相关配置"""
        return {
            "scraping_enabled": self.get_config_value("scraping_enabled", True),
            "scraping_interval_minutes": self.get_config_value("scraping_interval_minutes", 30),
            "scraping_max_pages": self.get_config_value("scraping_max_pages", 5),
            "data_cleanup_enabled": self.get_config_value("data_cleanup_enabled", True),
            "data_retention_days": self.get_config_value("data_retention_days", 730),
            "impact_score_update_enabled": self.get_config_value("impact_score_update_enabled", True),
            "health_check_enabled": self.get_config_value("health_check_enabled", True),
        }
    
    def update_scheduler_config(self, config_updates: Dict[str, Any]) -> bool:
        """批量更新调度器配置"""
        try:
            for key, value in config_updates.items():
                if key in [
                    "scraping_enabled", "scraping_interval_minutes", "scraping_max_pages",
                    "data_cleanup_enabled", "data_retention_days", "impact_score_update_enabled",
                    "health_check_enabled"
                ]:
                    self.set_config_value(key, value)
            return True
        except Exception:
            return False
    
    def get_configs_by_group(self, group: str) -> Dict[str, Any]:
        """根据分组获取配置"""
        configs = self.config_service.get_configs_by_group(group)
        result = {}
        for config in configs:
            result[config.config_key] = self._convert_value(config.config_value, config.config_type)
        return result
    
    def _convert_value(self, value: str, config_type: str) -> Any:
        """将字符串值转换为指定类型"""
        try:
            if config_type == "bool":
                return value.lower() in ("true", "1", "yes", "on")
            elif config_type == "int":
                return int(value)
            elif config_type == "float":
                return float(value)
            elif config_type == "json":
                return json.loads(value)
            else:  # string
                return value
        except (ValueError, json.JSONDecodeError):
            return value
    
    def _infer_type(self, value: Any) -> str:
        """推断值的类型"""
        if isinstance(value, bool):
            return "bool"
        elif isinstance(value, int):
            return "int"
        elif isinstance(value, float):
            return "float"
        elif isinstance(value, (dict, list)):
            return "json"
        else:
            return "string"


class SchedulerConfigManager:
    """调度器配置管理器"""
    
    def __init__(self, db: Session):
        self.config_manager = SystemConfigManager(db)
    
    def get_scraping_config(self) -> Dict[str, Any]:
        """获取数据抓取配置"""
        return {
            "enabled": self.config_manager.get_config_value("scraping_enabled", True),
            "interval_minutes": self.config_manager.get_config_value("scraping_interval_minutes", 30),
            "max_pages": self.config_manager.get_config_value("scraping_max_pages", 5),
        }
    
    def get_cleanup_config(self) -> Dict[str, Any]:
        """获取数据清理配置"""
        return {
            "enabled": self.config_manager.get_config_value("data_cleanup_enabled", True),
            "retention_days": self.config_manager.get_config_value("data_retention_days", 730),
        }
    
    def get_analysis_config(self) -> Dict[str, Any]:
        """获取分析配置"""
        return {
            "impact_score_update_enabled": self.config_manager.get_config_value("impact_score_update_enabled", True),
        }

    def get_notification_config(self) -> Dict[str, Any]:
        """获取通知配置"""
        return {
            "enabled": self.config_manager.get_config_value("notification_enabled", True),
            "daily_time": self.config_manager.get_config_value("notification_daily_time", "09:00"),
            "email_enabled": self.config_manager.get_config_value("notification_email_enabled", False),
            "wechat_enabled": self.config_manager.get_config_value("notification_wechat_enabled", False),
            "feishu_enabled": self.config_manager.get_config_value("notification_feishu_enabled", False),
        }
    
    def get_system_config(self) -> Dict[str, Any]:
        """获取系统配置"""
        return {
            "health_check_enabled": self.config_manager.get_config_value("health_check_enabled", True),
            "log_level": self.config_manager.get_config_value("log_level", "INFO"),
        }
    
    def update_scraping_config(self, enabled: bool = None, interval_minutes: int = None, max_pages: int = None) -> bool:
        """更新数据抓取配置"""
        updates = {}
        if enabled is not None:
            updates["scraping_enabled"] = enabled
        if interval_minutes is not None:
            updates["scraping_interval_minutes"] = interval_minutes
        if max_pages is not None:
            updates["scraping_max_pages"] = max_pages
        
        return self.config_manager.update_scheduler_config(updates)
    
    def is_job_enabled(self, job_type: str) -> bool:
        """检查指定任务是否启用"""
        job_config_map = {
            "scraping": "scraping_enabled",
            "cleanup": "data_cleanup_enabled",
            "impact_score": "impact_score_update_enabled",
            "health_check": "health_check_enabled",
        }
        
        config_key = job_config_map.get(job_type)
        if not config_key:
            return True  # 默认启用未知任务
        
        return self.config_manager.get_config_value(config_key, True)
