"""
简化的通知配置服务 - 不使用加密，敏感信息在环境变量中管理
"""
import json
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status
from loguru import logger

from app.models.notification_config import NotificationConfig, NotificationServiceType
from app.schemas.notifications import NotificationType


class SimpleNotificationConfigService:
    """简化的通知配置服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_all_configs(self) -> List[NotificationConfig]:
        """获取所有通知配置"""
        return self.db.query(NotificationConfig).all()
    
    def get_config_by_service_type(self, service_type: NotificationServiceType) -> Optional[NotificationConfig]:
        """根据服务类型获取配置"""
        return self.db.query(NotificationConfig).filter(
            NotificationConfig.service_type == service_type
        ).first()
    
    def create_or_update_config(self, service_type: NotificationServiceType, config_data: Dict[str, Any]) -> NotificationConfig:
        """创建或更新配置"""
        try:
            # 查找现有配置
            existing_config = self.get_config_by_service_type(service_type)
            
            if existing_config:
                # 更新现有配置
                for key, value in config_data.items():
                    if hasattr(existing_config, key):
                        setattr(existing_config, key, value)
                
                self.db.commit()
                self.db.refresh(existing_config)
                return existing_config
            else:
                # 创建新配置
                new_config = NotificationConfig(
                    service_type=service_type,
                    **config_data
                )
                
                self.db.add(new_config)
                self.db.commit()
                self.db.refresh(new_config)
                return new_config
                
        except IntegrityError as e:
            self.db.rollback()
            logger.error(f"创建/更新通知配置失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="配置数据冲突"
            )
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建/更新通知配置异常: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="内部服务器错误"
            )
    
    def delete_config(self, service_type: NotificationServiceType) -> bool:
        """删除配置"""
        try:
            config = self.get_config_by_service_type(service_type)
            if not config:
                return False
            
            self.db.delete(config)
            self.db.commit()
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除通知配置失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除配置失败"
            )
    
    def get_enabled_configs(self) -> List[NotificationConfig]:
        """获取所有启用的配置"""
        return self.db.query(NotificationConfig).filter(
            NotificationConfig.is_enabled == True
        ).all()
    
    async def test_config(self, service_type: NotificationServiceType) -> Dict[str, Any]:
        """测试配置 - 发送真实测试消息"""
        config = self.get_config_by_service_type(service_type)
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="配置不存在"
            )

        if not config.is_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="配置未启用"
            )

        # 发送真实的测试消息
        try:
            from app.services.notifications.notification_manager import notification_manager
            from app.schemas.notifications import NotificationContentBase, NotificationPriority, NotificationType
            from datetime import datetime

            # 转换service_type到NotificationType
            notification_type = NotificationType(service_type.value)

            # 构造测试消息内容
            test_content = NotificationContentBase(
                title="🧪 通知配置测试",
                content=f"这是一条来自股票增减持分析平台的测试消息\n\n" +
                       f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n" +
                       f"🔧 服务类型: {service_type.value.upper()}\n" +
                       f"✅ 配置状态: 正常\n" +
                       f"🎯 测试目的: 验证通知服务连通性",
                summary="如果您收到这条消息，说明通知配置工作正常！"
            )

            # 构造测试配置
            test_config = self._get_test_config(config, service_type)

            # 发送测试通知
            result = await notification_manager.send_notification(
                notification_type=notification_type,
                content=test_content,
                config=test_config,
                priority=NotificationPriority.NORMAL
            )

            if result.success:
                return {
                    "success": True,
                    "message": f"{service_type.value} 测试消息发送成功！请检查您的{service_type.value}是否收到测试消息。",
                    "details": {
                        "service_type": service_type.value,
                        "test_time": datetime.now().isoformat(),
                        "message_sent": True,
                        "config_status": "正常",
                        "recipients": test_config.get('to_emails') if service_type == NotificationServiceType.EMAIL else None
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"{service_type.value} 测试消息发送失败: {result.message}",
                    "details": {
                        "service_type": service_type.value,
                        "test_time": datetime.now().isoformat(),
                        "message_sent": False,
                        "error": result.message
                    }
                }

        except Exception as e:
            logger.error(f"测试通知配置失败: {e}")
            return {
                "success": False,
                "message": f"测试失败: {str(e)}",
                "details": {
                    "service_type": service_type.value,
                    "test_time": datetime.now().isoformat(),
                    "message_sent": False,
                    "error": str(e)
                }
            }

    def _get_test_config(self, config: NotificationConfig, service_type: NotificationServiceType) -> Dict[str, Any]:
        """获取测试配置"""
        if service_type == NotificationServiceType.EMAIL:
            # 邮件测试需要收件人列表
            recipients = []
            if config.email_recipients:
                try:
                    recipients = json.loads(config.email_recipients)
                except (json.JSONDecodeError, TypeError):
                    recipients = []

            if not recipients:
                # 如果没有配置收件人，使用默认测试邮箱
                recipients = ['<EMAIL>']  # 这里应该配置实际的测试邮箱

            return {
                'to_emails': recipients,
                'subject': '🧪 邮件通知测试 - 增减持数据分析平台'
            }

        elif service_type == NotificationServiceType.WECHAT:
            # 微信测试配置
            test_config = {}
            if config.wechat_mentioned_users:
                try:
                    mentioned_users = json.loads(config.wechat_mentioned_users)
                    test_config['mentioned_list'] = mentioned_users
                except (json.JSONDecodeError, TypeError):
                    pass

            if config.wechat_mentioned_mobiles:
                try:
                    mentioned_mobiles = json.loads(config.wechat_mentioned_mobiles)
                    test_config['mentioned_mobile_list'] = mentioned_mobiles
                except (json.JSONDecodeError, TypeError):
                    pass

            return test_config

        elif service_type == NotificationServiceType.FEISHU:
            # 飞书测试配置
            test_config = {}
            if config.feishu_at_all:
                test_config['at_all'] = True

            if config.feishu_at_users:
                try:
                    at_users = json.loads(config.feishu_at_users)
                    test_config['at_users'] = at_users
                except (json.JSONDecodeError, TypeError):
                    pass

            return test_config

        return {}
    
    def initialize_default_configs(self):
        """初始化默认配置"""
        try:
            # 检查是否已有配置
            existing_configs = self.get_all_configs()
            if existing_configs:
                logger.info("通知配置已存在，跳过初始化")
                return
            
            # 创建默认配置
            default_configs = [
                {
                    'service_type': NotificationServiceType.EMAIL,
                    'is_enabled': False,
                    'description': '邮件通知服务 - 敏感配置请在环境变量中设置',
                    'notification_time': '09:00'
                },
                {
                    'service_type': NotificationServiceType.WECHAT,
                    'is_enabled': False,
                    'description': '企业微信通知服务 - 敏感配置请在环境变量中设置',
                    'notification_time': '09:00'
                },
                {
                    'service_type': NotificationServiceType.FEISHU,
                    'is_enabled': False,
                    'description': '飞书通知服务 - 敏感配置请在环境变量中设置',
                    'notification_time': '09:00'
                }
            ]
            
            for config_data in default_configs:
                service_type = config_data.pop('service_type')
                self.create_or_update_config(service_type, config_data)
            
            logger.info("默认通知配置初始化完成")
            
        except Exception as e:
            logger.error(f"初始化默认配置失败: {e}")
    
    def get_notification_configs_for_service(self) -> Dict[str, Any]:
        """获取用于通知服务的配置"""
        from app.core.config import settings
        
        configs = {}
        enabled_configs = self.get_enabled_configs()
        
        for config in enabled_configs:
            service_config = config.get_notification_config()
            if service_config and service_config.get('enabled'):
                configs[config.service_type.value] = service_config
        
        return configs
