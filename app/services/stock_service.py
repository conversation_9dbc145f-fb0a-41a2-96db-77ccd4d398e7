"""
股票相关业务服务
"""
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import or_

from app.models.holdings import Stock
from app.schemas.holdings import StockCreate, StockUpdate


class StockService:
    """股票业务服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_stock_by_code(self, code: str) -> Optional[Stock]:
        """根据股票代码获取股票信息"""
        return self.db.query(Stock).filter(Stock.code == code).first()
    
    def get_stock_by_id(self, stock_id: int) -> Optional[Stock]:
        """根据ID获取股票信息"""
        return self.db.query(Stock).filter(Stock.id == stock_id).first()
    
    def search_stocks(self, keyword: str, limit: int = 20) -> List[Stock]:
        """
        搜索股票
        
        Args:
            keyword: 搜索关键词（股票代码或名称）
            limit: 返回数量限制
            
        Returns:
            股票列表
        """
        query = self.db.query(Stock).filter(
            or_(
                Stock.code.ilike(f"%{keyword}%"),
                Stock.name.ilike(f"%{keyword}%")
            )
        ).filter(Stock.is_active == True)
        
        return query.limit(limit).all()
    
    def get_all_stocks(self, skip: int = 0, limit: int = 100) -> List[Stock]:
        """获取所有股票列表"""
        return self.db.query(Stock).filter(
            Stock.is_active == True
        ).offset(skip).limit(limit).all()
    
    def create_stock(self, stock_data: StockCreate) -> Stock:
        """创建新股票记录"""
        # 检查是否已存在
        existing = self.get_stock_by_code(stock_data.code)
        if existing:
            return existing
        
        # 确定交易所
        exchange = self._determine_exchange(stock_data.code)
        
        db_stock = Stock(
            code=stock_data.code,
            name=stock_data.name,
            exchange=exchange,
            industry=stock_data.industry,
            is_active=stock_data.is_active
        )
        
        self.db.add(db_stock)
        self.db.commit()
        self.db.refresh(db_stock)
        
        return db_stock
    
    def update_stock(self, stock_id: int, stock_data: StockUpdate) -> Optional[Stock]:
        """更新股票信息"""
        db_stock = self.get_stock_by_id(stock_id)
        if not db_stock:
            return None
        
        update_data = stock_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_stock, field, value)
        
        self.db.commit()
        self.db.refresh(db_stock)
        
        return db_stock
    
    def get_or_create_stock(self, code: str, name: str, industry: str = None) -> Stock:
        """获取或创建股票记录"""
        stock = self.get_stock_by_code(code)
        if stock:
            return stock
        
        # 创建新股票
        stock_data = StockCreate(
            code=code,
            name=name,
            exchange=self._determine_exchange(code),
            industry=industry
        )
        
        return self.create_stock(stock_data)
    
    def _determine_exchange(self, code: str) -> str:
        """根据股票代码确定交易所"""
        if code.startswith(('00', '30')):
            return 'SZ'  # 深交所
        elif code.startswith(('60', '68')):
            return 'SH'  # 上交所
        else:
            return 'OTHER'
    
    def get_stocks_with_recent_changes(self, days: int = 30) -> List[Stock]:
        """获取最近有增减持变动的股票"""
        from datetime import date, timedelta
        from app.models.holdings import HoldingChange
        
        recent_date = date.today() - timedelta(days=days)
        
        return self.db.query(Stock).join(HoldingChange).filter(
            HoldingChange.announcement_date >= recent_date,
            Stock.is_active == True
        ).distinct().all()
    
    def get_stock_statistics(self) -> dict:
        """获取股票统计信息"""
        from app.models.holdings import HoldingChange
        
        total_stocks = self.db.query(Stock).filter(Stock.is_active == True).count()
        
        stocks_with_changes = self.db.query(Stock).join(HoldingChange).distinct().count()
        
        return {
            'total_stocks': total_stocks,
            'stocks_with_changes': stocks_with_changes,
            'coverage_rate': round(stocks_with_changes / total_stocks * 100, 2) if total_stocks > 0 else 0
        }
