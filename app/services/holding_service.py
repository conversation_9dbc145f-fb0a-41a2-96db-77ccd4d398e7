"""
增减持相关业务服务
"""
from datetime import date, datetime
from typing import List, Optional, Dict
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func

from app.models.holdings import HoldingChange, Stock, ChangeDirection, HolderType
from app.schemas.holdings import HoldingChangeCreate, HoldingChangeUpdate, HoldingChangeFilter
from app.services.stock_data import StockDataService


class HoldingChangeService:
    """增减持业务服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.stock_data_service = StockDataService()
    
    def get_holding_change_by_id(self, change_id: int) -> Optional[HoldingChange]:
        """根据ID获取增减持记录"""
        return self.db.query(HoldingChange).filter(HoldingChange.id == change_id).first()
    
    def create_holding_change(self, change_data: HoldingChangeCreate) -> HoldingChange:
        """创建增减持记录"""
        db_change = HoldingChange(**change_data.dict())
        
        self.db.add(db_change)
        self.db.commit()
        self.db.refresh(db_change)
        
        # 异步计算影响力评分
        self._calculate_impact_score(db_change)
        
        return db_change
    
    def update_holding_change(
        self, 
        change_id: int, 
        change_data: HoldingChangeUpdate
    ) -> Optional[HoldingChange]:
        """更新增减持记录"""
        db_change = self.get_holding_change_by_id(change_id)
        if not db_change:
            return None
        
        update_data = change_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_change, field, value)
        
        self.db.commit()
        self.db.refresh(db_change)
        
        return db_change
    
    def get_holding_changes(
        self, 
        filters: HoldingChangeFilter
    ) -> Dict:
        """
        根据筛选条件获取增减持记录
        
        Returns:
            包含总数和数据列表的字典
        """
        query = self.db.query(HoldingChange).join(Stock)
        
        # 应用筛选条件
        if filters.stock_code:
            query = query.filter(Stock.code.ilike(f"%{filters.stock_code}%"))
        
        if filters.stock_name:
            query = query.filter(Stock.name.ilike(f"%{filters.stock_name}%"))
        
        if filters.holder_name:
            query = query.filter(HoldingChange.holder_name.ilike(f"%{filters.holder_name}%"))
        
        if filters.holder_type:
            query = query.filter(HoldingChange.holder_type == filters.holder_type)
        
        if filters.direction:
            query = query.filter(HoldingChange.direction == filters.direction)

        if filters.change_reason:
            query = query.filter(HoldingChange.change_reason.ilike(f"%{filters.change_reason}%"))

        if filters.date_from:
            query = query.filter(HoldingChange.announcement_date >= filters.date_from)
        
        if filters.date_to:
            query = query.filter(HoldingChange.announcement_date <= filters.date_to)
        
        if filters.change_amount_min:
            query = query.filter(HoldingChange.change_amount >= filters.change_amount_min)
        
        if filters.change_amount_max:
            query = query.filter(HoldingChange.change_amount <= filters.change_amount_max)
        
        if filters.holding_ratio_min:
            query = query.filter(HoldingChange.holding_ratio_after >= filters.holding_ratio_min)
        
        if filters.holding_ratio_max:
            query = query.filter(HoldingChange.holding_ratio_after <= filters.holding_ratio_max)
        
        # 获取总数
        total = query.count()
        
        # 分页和排序
        items = query.order_by(desc(HoldingChange.announcement_date)).offset(
            (filters.page - 1) * filters.page_size
        ).limit(filters.page_size).all()
        
        return {
            'total': total,
            'page': filters.page,
            'page_size': filters.page_size,
            'items': items
        }
    
    def get_stock_holding_changes(
        self, 
        stock_code: str, 
        limit: int = 50
    ) -> List[HoldingChange]:
        """获取特定股票的增减持记录"""
        return self.db.query(HoldingChange).join(Stock).filter(
            Stock.code == stock_code
        ).order_by(desc(HoldingChange.announcement_date)).limit(limit).all()
    
    def get_recent_changes(self, days: int = 7, limit: int = 100) -> List[HoldingChange]:
        """获取最近的增减持记录"""
        from datetime import timedelta
        
        recent_date = date.today() - timedelta(days=days)
        
        return self.db.query(HoldingChange).filter(
            HoldingChange.announcement_date >= recent_date
        ).order_by(desc(HoldingChange.announcement_date)).limit(limit).all()
    
    def get_top_changes_by_amount(self, limit: int = 20) -> List[HoldingChange]:
        """获取按变动金额排序的增减持记录"""
        return self.db.query(HoldingChange).filter(
            HoldingChange.change_amount.isnot(None)
        ).order_by(desc(HoldingChange.change_amount)).limit(limit).all()
    
    def get_statistics(self) -> Dict:
        """获取增减持统计信息"""
        total_changes = self.db.query(HoldingChange).count()
        
        increase_count = self.db.query(HoldingChange).filter(
            HoldingChange.direction == ChangeDirection.INCREASE.value
        ).count()

        decrease_count = self.db.query(HoldingChange).filter(
            HoldingChange.direction == ChangeDirection.DECREASE.value
        ).count()
        
        # 按持有人类型统计
        holder_type_stats = self.db.query(
            HoldingChange.holder_type,
            func.count(HoldingChange.id).label('count')
        ).group_by(HoldingChange.holder_type).all()
        
        # 最近30天统计
        from datetime import timedelta
        recent_date = date.today() - timedelta(days=30)
        recent_count = self.db.query(HoldingChange).filter(
            HoldingChange.announcement_date >= recent_date
        ).count()
        
        return {
            'total_changes': total_changes,
            'increase_count': increase_count,
            'decrease_count': decrease_count,
            'recent_30d_count': recent_count,
            'holder_type_distribution': {
                item.holder_type: item.count for item in holder_type_stats
            }
        }
    
    def _calculate_impact_score(self, holding_change: HoldingChange) -> float:
        """
        计算增减持事件的影响力评分
        
        评分因子：
        1. 变动金额（权重40%）
        2. 变动比例（权重30%）
        3. 持有人类型（权重20%）
        4. 股价波动（权重10%）
        """
        try:
            score = 0.0
            
            # 1. 变动金额评分 (0-40分)
            if holding_change.change_amount:
                amount_score = min(holding_change.change_amount / 10000, 40)  # 1亿=40分
                score += amount_score
            
            # 2. 变动比例评分 (0-30分)
            if holding_change.holding_ratio_after:
                ratio_score = min(holding_change.holding_ratio_after * 3, 30)  # 10%=30分
                score += ratio_score
            
            # 3. 持有人类型评分 (0-20分)
            type_scores = {
                HolderType.EXECUTIVE.value: 15,
                HolderType.MAJOR_SHAREHOLDER.value: 20,
                HolderType.INSTITUTION.value: 10,
                HolderType.OTHER.value: 5
            }
            score += type_scores.get(holding_change.holder_type, 5)
            
            # 4. 股价波动评分 (0-10分)
            try:
                stock_code = holding_change.stock.code
                price_impact = self.stock_data_service.calculate_price_impact(
                    stock_code, holding_change.announcement_date
                )
                
                if price_impact and 'return_after_5d' in price_impact:
                    volatility = abs(price_impact['return_after_5d'])
                    volatility_score = min(volatility / 2, 10)  # 20%波动=10分
                    score += volatility_score
                    
            except Exception as e:
                # 如果获取股价数据失败，给默认分数
                score += 5
            
            # 更新评分
            holding_change.impact_score = round(score, 2)
            self.db.commit()
            
            return score
            
        except Exception as e:
            return 0.0
    
    def batch_create_holding_changes(self, changes_data: List[Dict]) -> List[HoldingChange]:
        """批量创建增减持记录"""
        from app.services.stock_service import StockService
        
        stock_service = StockService(self.db)
        created_changes = []
        
        for change_data in changes_data:
            try:
                # 获取或创建股票记录
                stock = stock_service.get_or_create_stock(
                    code=change_data['stock_code'],
                    name=change_data['stock_name']
                )
                
                # 转换枚举值为字符串
                direction_str = change_data['direction'].value if hasattr(change_data['direction'], 'value') else str(change_data['direction'])
                holder_type_str = change_data['holder_type'].value if hasattr(change_data['holder_type'], 'value') else str(change_data['holder_type'])

                # 检查是否已存在相同记录（更严格的重复检查）
                change_shares = change_data.get('change_shares')
                change_reason = change_data.get('change_reason', '')

                from loguru import logger
                logger.debug(f"检查重复: {change_data['stock_code']} {change_data['holder_name']} {change_data['announcement_date']} shares={change_shares} reason='{change_reason}'")

                existing = self.db.query(HoldingChange).filter(
                    and_(
                        HoldingChange.stock_id == stock.id,
                        HoldingChange.announcement_date == change_data['announcement_date'],
                        HoldingChange.holder_name == change_data['holder_name'],
                        HoldingChange.direction == direction_str,
                        HoldingChange.change_shares == change_shares,
                        HoldingChange.change_reason == change_reason
                    )
                ).first()

                if existing:
                    logger.info(f"跳过重复记录: {change_data['stock_code']} {change_data['holder_name']} {change_data['announcement_date']} (ID: {existing.id})")
                    continue  # 跳过重复记录

                # 计算变动金额（如果没有提供的话）
                change_amount = change_data.get('change_amount')
                if change_amount is None:
                    change_shares = change_data.get('change_shares')
                    price_avg = change_data.get('price_avg')
                    if change_shares is not None and price_avg is not None:
                        # 变动金额 = 变动股数(万股) * 平均价格(元) = 万元
                        change_amount = abs(change_shares) * price_avg

                # 创建增减持记录
                db_change = HoldingChange(
                    stock_id=stock.id,
                    announcement_date=change_data['announcement_date'],
                    change_date=change_data.get('change_date', change_data['announcement_date']),
                    holder_name=change_data['holder_name'],
                    holder_type=holder_type_str,
                    direction=direction_str,
                    change_shares=change_data.get('change_shares'),
                    total_shares_after=change_data.get('total_shares_after'),
                    holding_ratio_after=change_data.get('holding_ratio_after'),
                    price_min=change_data.get('price_min'),
                    price_max=change_data.get('price_max'),
                    price_avg=change_data.get('price_avg'),
                    change_amount=change_amount,
                    change_reason=change_data.get('change_reason'),
                    source_url=change_data.get('source_url')
                )
                
                self.db.add(db_change)
                created_changes.append(db_change)
                
            except Exception as e:
                from loguru import logger
                logger.error(f"保存增减持记录失败: {e}, 数据: {change_data}")
                continue  # 跳过错误记录
        
        # 批量提交
        if created_changes:
            self.db.commit()
            
            # 批量刷新
            for change in created_changes:
                self.db.refresh(change)
        
        return created_changes
