"""
同花顺增减持数据爬虫
"""
import asyncio
import re
import time
from datetime import datetime, date
from typing import List, Dict, Optional
from urllib.parse import urljoin

import requests
from bs4 import BeautifulSoup
from loguru import logger
from playwright.async_api import async_playwright

from app.core.config import settings
from app.models.holdings import ChangeDirection, HolderType


class TongHuaShunScraper:
    """增减持数据爬虫"""

    def __init__(self):
        from app.core.config import settings
        self.BASE_URL = settings.DATA_SOURCE_BASE_URL
        self.HOLDINGS_URL = settings.DATA_SOURCE_HOLDINGS_URL
        self.AJAX_BASE_URL = "https://data.10jqka.com.cn/ajax/ggjy/field/enddate/order/desc"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': settings.USER_AGENT,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

    async def scrape_holdings_data_ajax(self, page: int = 1, max_pages: int = 10) -> List[Dict]:
        """
        使用AJAX API直接抓取增减持数据

        Args:
            page: 起始页码
            max_pages: 最大抓取页数

        Returns:
            增减持数据列表
        """
        all_data = []

        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context(
                user_agent=settings.USER_AGENT,
                viewport={'width': 1920, 'height': 1080}
            )
            page_obj = await context.new_page()

            try:
                # 首先访问主页面获取cookies和session
                logger.info("访问主页面获取session...")
                await page_obj.goto(self.HOLDINGS_URL, wait_until='networkidle')
                await asyncio.sleep(2)

                for current_page in range(page, page + max_pages):
                    logger.info(f"正在抓取第 {current_page} 页数据...")

                    # 构建AJAX URL - 使用您提供的正确格式
                    if current_page == 1:
                        ajax_url = f"{self.AJAX_BASE_URL}/ajax/1/free/1/"
                    else:
                        ajax_url = f"{self.AJAX_BASE_URL}/page/{current_page}/ajax/1/free/1/"

                    logger.info(f"使用JavaScript请求AJAX URL: {ajax_url}")

                    # 使用JavaScript在页面内发送AJAX请求
                    try:
                        ajax_content = await page_obj.evaluate(f"""
                            async () => {{
                                const response = await fetch('{ajax_url}', {{
                                    method: 'GET',
                                    headers: {{
                                        'X-Requested-With': 'XMLHttpRequest',
                                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                                    }}
                                }});

                                if (!response.ok) {{
                                    throw new Error(`HTTP ${{response.status}}`);
                                }}

                                return await response.text();
                            }}
                        """)

                        if not ajax_content:
                            logger.warning(f"第 {current_page} 页AJAX请求返回空内容")
                            continue

                        # 解析AJAX返回的HTML内容
                        page_data = self._parse_holdings_page(ajax_content)

                    except Exception as e:
                        logger.warning(f"第 {current_page} 页AJAX请求失败: {e}")
                        continue

                    if not page_data:
                        logger.warning(f"第 {current_page} 页没有获取到数据，停止抓取")
                        break

                    all_data.extend(page_data)
                    logger.info(f"第 {current_page} 页获取到 {len(page_data)} 条数据")

                    # 随机延时，避免被反爬
                    await asyncio.sleep(1 + (current_page % 3))

            except Exception as e:
                logger.error(f"抓取数据时发生错误: {e}")
                raise
            finally:
                await browser.close()

        logger.info(f"总共抓取到 {len(all_data)} 条增减持数据")
        return all_data

    async def scrape_holdings_data(self, page: int = 1, max_pages: int = 10) -> List[Dict]:
        """
        抓取增减持数据 - 使用Playwright模拟真实用户点击

        Args:
            page: 起始页码
            max_pages: 最大抓取页数

        Returns:
            增减持数据列表
        """
        all_data = []

        async with async_playwright() as p:
            # 为容器环境配置浏览器启动参数
            browser_args = [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]

            browser = await p.chromium.launch(
                headless=True,
                args=browser_args
            )
            context = await browser.new_context(
                user_agent=settings.USER_AGENT,
                viewport={'width': 1920, 'height': 1080},
                # 添加更多真实浏览器特征
                extra_http_headers={
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                }
            )
            page_obj = await context.new_page()

            # 设置更真实的浏览器行为
            await page_obj.add_init_script("""
                // 移除webdriver标识
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });

                // 添加更多浏览器特征
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en'],
                });

                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
            """)

            try:
                # 访问第一页
                logger.info(f"正在访问页面: {self.HOLDINGS_URL}")
                await page_obj.goto(self.HOLDINGS_URL, wait_until='networkidle')

                # 模拟真实用户行为：随机移动鼠标
                await page_obj.mouse.move(100, 100)
                await asyncio.sleep(2)

                # 手动跟踪当前页码，使用下一页按钮进行分页
                current_page_num = page

                for page_index in range(max_pages):
                    logger.info(f"正在抓取第 {current_page_num} 页数据...")

                    # 如果不是第一页，点击下一页按钮
                    if page_index > 0:
                        success = await self._click_next_page(page_obj)
                        if not success:
                            logger.warning(f"无法点击下一页按钮，停止抓取")
                            break
                        current_page_num += 1
                        logger.info(f"成功导航到第 {current_page_num} 页")

                    # 等待表格数据加载
                    await self._wait_for_table_data(page_obj)

                    # 获取页面内容
                    content = await page_obj.content()
                    page_data = self._parse_holdings_page(content)

                    if not page_data:
                        logger.warning(f"第 {current_page_num} 页没有获取到数据，停止抓取")
                        break

                    # 检查是否与之前页面数据重复
                    if page_index > 0 and self._is_duplicate_data(all_data, page_data):
                        logger.warning(f"第 {current_page_num} 页数据与之前重复，可能已到最后一页")
                        break

                    all_data.extend(page_data)
                    logger.info(f"第 {current_page_num} 页获取到 {len(page_data)} 条数据")

                    # 模拟真实用户行为：随机延时和鼠标移动
                    await asyncio.sleep(2 + (page_index % 3))
                    await page_obj.mouse.move(200 + page_index * 10, 200 + page_index * 10)

            except Exception as e:
                logger.error(f"抓取数据时发生错误: {e}")
                raise
            finally:
                await browser.close()

        logger.info(f"总共抓取到 {len(all_data)} 条增减持数据")
        return all_data

    async def _navigate_to_page(self, page_obj, target_page: int, current_page: int = 1) -> bool:
        """
        导航到指定页码，使用多种策略

        Args:
            page_obj: Playwright页面对象
            target_page: 目标页码
            current_page: 当前页码

        Returns:
            是否成功导航
        """
        try:
            logger.info(f"尝试导航到第 {target_page} 页 (当前第 {current_page} 页)")

            # 策略1: 直接点击页码链接
            if await self._click_page_number(page_obj, target_page):
                return True

            # 策略2: 使用下一页按钮（当找不到具体页码时）
            if target_page == current_page + 1:  # 只有连续翻页时才使用下一页
                logger.info(f"未找到页码 {target_page} 链接，尝试使用下一页按钮")
                if await self._click_next_page(page_obj):
                    return True

            # 策略3: 使用JavaScript直接跳转
            if await self._javascript_navigate(page_obj, target_page):
                return True

            return False

        except Exception as e:
            logger.warning(f"导航到第 {target_page} 页失败: {e}")
            return False

    async def _wait_for_table_data(self, page_obj) -> bool:
        """
        等待表格数据加载完成

        Args:
            page_obj: Playwright页面对象

        Returns:
            是否成功等待到数据
        """
        try:
            # 等待表格出现
            await page_obj.wait_for_selector('table tbody tr', timeout=10000)

            # 等待数据稳定（检查表格行数不再变化）
            stable_count = 0
            last_row_count = 0

            for _ in range(10):  # 最多检查10次
                await asyncio.sleep(0.5)
                rows = await page_obj.query_selector_all('table tbody tr')
                current_row_count = len(rows)

                if current_row_count == last_row_count and current_row_count > 0:
                    stable_count += 1
                    if stable_count >= 3:  # 连续3次相同，认为稳定
                        logger.debug(f"表格数据已稳定，共 {current_row_count} 行")
                        return True
                else:
                    stable_count = 0

                last_row_count = current_row_count

            logger.warning("等待表格数据稳定超时")
            return False

        except Exception as e:
            logger.warning(f"等待表格数据失败: {e}")
            return False

    def _is_duplicate_data(self, existing_data: List[Dict], new_data: List[Dict]) -> bool:
        """
        检查新数据是否与现有数据重复

        Args:
            existing_data: 现有数据列表
            new_data: 新数据列表

        Returns:
            是否重复
        """
        if not existing_data or not new_data:
            return False

        # 创建现有数据的唯一标识集合
        existing_keys = set()
        for item in existing_data:
            key = f"{item['stock_code']}-{item['holder_name']}-{item['announcement_date']}-{item['change_shares']}"
            existing_keys.add(key)

        # 检查新数据是否都已存在
        duplicate_count = 0
        for item in new_data:
            key = f"{item['stock_code']}-{item['holder_name']}-{item['announcement_date']}-{item['change_shares']}"
            if key in existing_keys:
                duplicate_count += 1

        # 如果超过80%重复，认为是重复数据
        duplicate_ratio = duplicate_count / len(new_data)
        logger.debug(f"数据重复率: {duplicate_ratio:.2%} ({duplicate_count}/{len(new_data)})")

        return duplicate_ratio > 0.8

    async def _click_next_page(self, page_obj) -> bool:
        """
        点击下一页按钮

        Args:
            page_obj: Playwright页面对象

        Returns:
            是否成功点击
        """
        try:
            # 多种下一页按钮选择器
            next_selectors = [
                '.m-page .next:not(.disabled)',
                '.m-page a.next:not(.disabled)',
                '.m-page .next-page:not(.disabled)',
                '.m-page a[title="下一页"]:not(.disabled)',
                '.m-page a:has-text("下一页")',
                '.m-page a:has-text(">")',
                '.pagination .next:not(.disabled)',
                '.pagination a:has-text("下一页")'
            ]

            for selector in next_selectors:
                try:
                    next_button = await page_obj.query_selector(selector)
                    if next_button:
                        # 检查按钮是否可点击
                        is_disabled = await next_button.get_attribute('disabled')
                        class_name = await next_button.get_attribute('class') or ''

                        if not is_disabled and 'disabled' not in class_name:
                            logger.info(f"找到下一页按钮: {selector}")

                            # 滚动到按钮位置
                            await next_button.scroll_into_view_if_needed()
                            await asyncio.sleep(0.5)

                            # 模拟鼠标悬停
                            await next_button.hover()
                            await asyncio.sleep(0.3)

                            # 点击按钮
                            await next_button.click()
                            await asyncio.sleep(2)

                            return True
                except Exception as e:
                    logger.debug(f"尝试下一页按钮 {selector} 失败: {e}")
                    continue

            logger.warning("未找到可用的下一页按钮")
            return False

        except Exception as e:
            logger.warning(f"点击下一页失败: {e}")
            return False

    async def _javascript_navigate(self, page_obj, target_page: int) -> bool:
        """
        使用JavaScript直接导航到指定页面

        Args:
            page_obj: Playwright页面对象
            target_page: 目标页码

        Returns:
            是否成功导航
        """
        try:
            logger.info(f"尝试使用JavaScript导航到第 {target_page} 页")

            # 尝试多种JavaScript导航方式
            js_scripts = [
                # 方式1: 直接调用分页函数
                f"if (window.goToPage) {{ window.goToPage({target_page}); }}",

                # 方式2: 触发分页事件
                f"""
                const pageLinks = document.querySelectorAll('.m-page a');
                for (let link of pageLinks) {{
                    if (link.textContent.trim() === '{target_page}') {{
                        link.click();
                        break;
                    }}
                }}
                """,

                # 方式3: 模拟表单提交
                f"""
                const pageInput = document.querySelector('input[name="page"]') || document.querySelector('.page-input');
                if (pageInput) {{
                    pageInput.value = '{target_page}';
                    const form = pageInput.closest('form');
                    if (form) {{
                        form.submit();
                    }} else {{
                        pageInput.dispatchEvent(new Event('change'));
                    }}
                }}
                """
            ]

            for i, script in enumerate(js_scripts):
                try:
                    logger.debug(f"执行JavaScript方式 {i+1}")
                    await page_obj.evaluate(script)
                    await asyncio.sleep(3)

                    # 检查是否成功跳转
                    if await self._verify_page_change(page_obj, target_page):
                        logger.info(f"JavaScript导航成功 (方式 {i+1})")
                        return True

                except Exception as e:
                    logger.debug(f"JavaScript方式 {i+1} 失败: {e}")
                    continue

            return False

        except Exception as e:
            logger.warning(f"JavaScript导航失败: {e}")
            return False

    async def _verify_page_change(self, page_obj, expected_page: int) -> bool:
        """
        验证页面是否已切换到指定页码

        Args:
            page_obj: Playwright页面对象
            expected_page: 期望的页码

        Returns:
            是否成功切换
        """
        try:
            # 等待页面更新
            await asyncio.sleep(1)

            # 方式1: 检查URL变化
            current_url = page_obj.url
            if f'page/{expected_page}' in current_url or f'page={expected_page}' in current_url:
                return True

            # 方式2: 检查当前页码指示器
            current_page_selectors = [
                '.m-page .current',
                '.m-page .active',
                '.pagination .current',
                '.pagination .active'
            ]

            for selector in current_page_selectors:
                try:
                    current_element = await page_obj.query_selector(selector)
                    if current_element:
                        text = await current_element.inner_text()
                        if text.strip() == str(expected_page):
                            return True
                except:
                    continue

            # 方式3: 检查表格数据是否发生变化
            # 这里可以通过比较第一行数据来判断
            try:
                first_row = await page_obj.query_selector('tbody tr:first-child')
                if first_row:
                    # 如果能获取到数据，说明页面已更新
                    return True
            except:
                pass

            return False

        except Exception as e:
            logger.debug(f"验证页面变化失败: {e}")
            return False

    async def _click_page_number(self, page_obj, target_page: int) -> bool:
        """
        点击指定页码，使用智能策略

        Args:
            page_obj: Playwright页面对象
            target_page: 目标页码

        Returns:
            是否成功点击
        """
        try:
            logger.info(f"尝试点击页码 {target_page}")

            # 等待分页元素加载
            await page_obj.wait_for_selector('.m-page', timeout=10000)

            # 多种分页选择器
            page_selectors = [
                '.m-page a',
                '.pagination a',
                '.page-nav a',
                '.pager a'
            ]

            for selector in page_selectors:
                try:
                    page_links = await page_obj.query_selector_all(selector)
                    if not page_links:
                        continue

                    logger.debug(f"使用选择器 {selector} 找到 {len(page_links)} 个分页链接")

                    for link in page_links:
                        try:
                            text = await link.inner_text()
                            href = await link.get_attribute('href')

                            # 检查文本匹配
                            if text.strip() == str(target_page):
                                logger.info(f"找到页码链接: {target_page} (文本匹配)")

                                # 滚动到链接位置
                                await link.scroll_into_view_if_needed()
                                await asyncio.sleep(0.5)

                                # 模拟鼠标悬停
                                await link.hover()
                                await asyncio.sleep(0.3)

                                # 记录点击前的页面状态
                                before_url = page_obj.url
                                before_content = await self._get_first_row_text(page_obj)

                                # 点击链接
                                await link.click()

                                # 等待页面响应
                                await asyncio.sleep(3)

                                # 验证页面是否发生变化
                                after_url = page_obj.url
                                after_content = await self._get_first_row_text(page_obj)

                                if after_url != before_url or after_content != before_content:
                                    logger.info(f"页码 {target_page} 点击成功，页面已更新")
                                    return True
                                else:
                                    logger.warning(f"页码 {target_page} 点击后页面未更新")

                            # 检查href匹配
                            elif href and (f'page/{target_page}' in href or f'page={target_page}' in href):
                                logger.info(f"找到页码链接: {target_page} (href匹配)")

                                await link.scroll_into_view_if_needed()
                                await asyncio.sleep(0.5)
                                await link.hover()
                                await asyncio.sleep(0.3)

                                before_content = await self._get_first_row_text(page_obj)
                                await link.click()
                                await asyncio.sleep(3)
                                after_content = await self._get_first_row_text(page_obj)

                                if after_content != before_content:
                                    logger.info(f"页码 {target_page} 点击成功 (href)")
                                    return True

                        except Exception as e:
                            logger.debug(f"检查分页链接失败: {e}")
                            continue

                except Exception as e:
                    logger.debug(f"使用选择器 {selector} 失败: {e}")
                    continue

            logger.warning(f"未找到页码 {target_page} 的可点击链接")
            return False

        except Exception as e:
            logger.warning(f"点击页码 {target_page} 失败: {e}")
            return False

    async def _get_first_row_text(self, page_obj) -> str:
        """
        获取表格第一行的文本内容，用于检测页面变化

        Args:
            page_obj: Playwright页面对象

        Returns:
            第一行文本内容
        """
        try:
            first_row = await page_obj.query_selector('tbody tr:first-child')
            if first_row:
                return await first_row.inner_text()
            return ""
        except:
            return ""

    async def scrape_holdings_data_old(self, page: int = 1, max_pages: int = 10) -> List[Dict]:
        """
        抓取增减持数据 - 旧的页面点击方式（已废弃）

        Args:
            page: 起始页码
            max_pages: 最大抓取页数

        Returns:
            增减持数据列表
        """
        all_data = []

        async with async_playwright() as p:
            # 为容器环境配置浏览器启动参数
            browser_args = [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]

            browser = await p.chromium.launch(
                headless=True,
                args=browser_args
            )
            context = await browser.new_context(
                user_agent=settings.USER_AGENT,
                viewport={'width': 1920, 'height': 1080}
            )
            page_obj = await context.new_page()

            # 监听网络请求
            requests_log = []

            def log_request(request):
                if 'ggjy' in request.url or 'page' in request.url.lower():
                    requests_log.append({
                        'url': request.url,
                        'method': request.method,
                        'headers': dict(request.headers),
                        'post_data': request.post_data
                    })
                    logger.info(f"网络请求: {request.method} {request.url}")

            page_obj.on('request', log_request)

            try:
                # 首先访问第一页
                logger.info(f"正在抓取第 {page} 页数据...")
                await page_obj.goto(self.HOLDINGS_URL, wait_until='networkidle')
                await asyncio.sleep(3)  # 等待页面完全加载

                for current_page in range(page, page + max_pages):
                    logger.info(f"正在抓取第 {current_page} 页数据... (起始页: {page})")

                    # 如果不是第一页，需要点击分页按钮
                    if current_page > page:
                        logger.info(f"需要执行分页操作: {current_page} > {page}")
                        try:
                            logger.info(f"开始分页操作，目标页码: {current_page}")

                            # 等待分页元素加载
                            await page_obj.wait_for_selector('.m-page', timeout=10000)
                            logger.info("分页元素已加载")

                            # 先尝试直接点击页码
                            page_clicked = False

                            # 查找包含页码的链接
                            page_links = await page_obj.query_selector_all('.m-page a')
                            logger.info(f"找到 {len(page_links)} 个分页链接")

                            for i, link in enumerate(page_links):
                                try:
                                    text = await link.inner_text()
                                    logger.debug(f"分页链接 {i}: '{text.strip()}'")
                                    if text.strip() == str(current_page):
                                        logger.info(f"找到页码链接: {current_page}")

                                        # 获取点击前的第一行数据作为参考
                                        try:
                                            first_row_before = await page_obj.query_selector('tbody tr:first-child')
                                            first_data_before = await first_row_before.inner_text() if first_row_before else ""
                                            logger.debug(f"点击前第一行数据: {first_data_before[:50]}...")
                                        except:
                                            first_data_before = ""

                                        await link.click()
                                        logger.info(f"已点击页码 {current_page}，等待页面更新...")

                                        # 等待页面内容更新，最多等待10秒
                                        for wait_count in range(20):  # 20次 * 0.5秒 = 10秒
                                            await asyncio.sleep(0.5)
                                            try:
                                                first_row_after = await page_obj.query_selector('tbody tr:first-child')
                                                first_data_after = await first_row_after.inner_text() if first_row_after else ""

                                                # 如果第一行数据发生了变化，说明页面已更新
                                                if first_data_after != first_data_before and first_data_after:
                                                    logger.info(f"页面内容已更新 (等待了 {(wait_count + 1) * 0.5} 秒)")
                                                    logger.debug(f"点击后第一行数据: {first_data_after[:50]}...")
                                                    break
                                            except:
                                                continue
                                        else:
                                            logger.warning(f"等待10秒后页面内容仍未更新")

                                        # 额外等待1秒确保数据完全加载
                                        await asyncio.sleep(1)
                                        page_clicked = True
                                        break
                                except Exception as e:
                                    logger.debug(f"检查分页链接 {i} 失败: {e}")
                                    continue

                            # 如果没找到具体页码，尝试点击下一页
                            if not page_clicked:
                                logger.info(f"未找到页码 {current_page}，尝试点击下一页")

                                # 查找下一页按钮
                                next_selectors = [
                                    '.m-page .next',
                                    '.m-page a.next',
                                    '.m-page .next-page',
                                    '.m-page a[title="下一页"]',
                                    '.m-page a[title="Next"]'
                                ]

                                next_clicked = False
                                for selector in next_selectors:
                                    try:
                                        next_button = await page_obj.query_selector(selector)
                                        if next_button:
                                            logger.info(f"找到下一页按钮: {selector}")
                                            await next_button.click()
                                            await asyncio.sleep(3)
                                            next_clicked = True
                                            break
                                    except Exception as e:
                                        logger.debug(f"尝试下一页按钮 {selector} 失败: {e}")
                                        continue

                                if not next_clicked:
                                    logger.warning(f"无法找到第 {current_page} 页的分页按钮")
                                    # 打印分页元素用于调试
                                    try:
                                        page_html = await page_obj.query_selector('.m-page')
                                        if page_html:
                                            html_content = await page_html.inner_html()
                                            logger.info(f"分页HTML: {html_content[:500]}...")
                                    except:
                                        pass
                                    break
                            else:
                                logger.info(f"成功点击页码 {current_page}")

                        except Exception as e:
                            logger.warning(f"分页操作失败: {e}")
                            break

                    # 获取页面内容
                    content = await page_obj.content()
                    page_data = self._parse_holdings_page(content)

                    if not page_data:
                        logger.warning(f"第 {current_page} 页没有获取到数据，停止抓取")
                        break

                    all_data.extend(page_data)
                    logger.info(f"第 {current_page} 页获取到 {len(page_data)} 条数据")

                    # 随机延时，避免被反爬
                    await asyncio.sleep(1 + (current_page % 3))
                    
            except Exception as e:
                logger.error(f"抓取数据时发生错误: {e}")
            finally:
                await browser.close()
        
        logger.info(f"总共抓取到 {len(all_data)} 条增减持数据")
        return all_data
    
    def _parse_holdings_page(self, html_content: str) -> List[Dict]:
        """
        解析增减持页面数据

        Args:
            html_content: 页面HTML内容

        Returns:
            解析后的数据列表
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        data_list = []

        try:
            # 查找数据表格 - 更新选择器以匹配实际的表格
            table = soup.find('table', {'class': 'm-table J-ajax-table J-canvas-table'})
            if not table:
                # 备用选择器
                table = soup.find('table', class_='m-table')
                if not table:
                    logger.warning("未找到数据表格")
                    return data_list

            tbody = table.find('tbody')
            if not tbody:
                logger.warning("未找到表格数据体")
                return data_list

            rows = tbody.find_all('tr')
            logger.info(f"找到 {len(rows)} 行数据")

            for row in rows:
                cells = row.find_all('td')
                if len(cells) < 14:  # 确保有足够的列（实际表格有14列）
                    logger.debug(f"跳过行，列数不足: {len(cells)}")
                    continue

                try:
                    # 解析每一行数据
                    data = self._parse_row_data(cells)
                    if data:
                        data_list.append(data)
                        logger.debug(f"成功解析数据: {data['stock_code']} {data['stock_name']}")
                except Exception as e:
                    logger.warning(f"解析行数据时出错: {e}")
                    continue

        except Exception as e:
            logger.error(f"解析页面数据时发生错误: {e}")

        return data_list
    
    def _parse_row_data(self, cells) -> Optional[Dict]:
        """
        解析表格行数据

        Args:
            cells: 表格单元格列表 (14列)

        Returns:
            解析后的数据字典
        """
        try:
            # 根据实际表格结构提取信息
            # 列索引: 0=序号, 1=股票代码, 2=股票简称, 3=变动人, 4=变动日期,
            #        5=变动股数, 6=成交均价, 7=变动原因, 8=变动比例, 9=变动后股数,
            #        10=董监高, 11=董监高薪酬, 12=董监高职务, 13=董监高关系

            stock_code = cells[1].get_text(strip=True)  # 股票代码
            stock_name = cells[2].get_text(strip=True)  # 股票简称
            holder_name = cells[3].get_text(strip=True)  # 变动人
            change_date = cells[4].get_text(strip=True)  # 变动日期
            change_shares = cells[5].get_text(strip=True)  # 变动股数
            avg_price = cells[6].get_text(strip=True)  # 成交均价
            change_reason = cells[7].get_text(strip=True)  # 变动原因
            change_ratio = cells[8].get_text(strip=True)  # 变动比例
            shares_after = cells[9].get_text(strip=True)  # 变动后股数
            executive_info = cells[10].get_text(strip=True)  # 董监高
            executive_position = cells[12].get_text(strip=True)  # 董监高职务

            # 验证股票代码
            if not stock_code or not re.match(r'\d{6}', stock_code):
                logger.debug(f"无效的股票代码: {stock_code}")
                return None

            # 解析日期
            parsed_date = self._parse_date(change_date)
            if not parsed_date:
                logger.debug(f"无法解析日期: {change_date}")
                return None

            # 解析数值
            shares_num = self._parse_number(change_shares)
            ratio_num = self._parse_percentage(change_ratio)
            price_num = self._parse_number(avg_price)
            shares_after_num = self._parse_number(shares_after)

            # 根据变动股数的正负号判断增减持方向
            if shares_num is not None and shares_num < 0:
                direction = ChangeDirection.DECREASE
                shares_num = abs(shares_num)  # 转为正数存储
            else:
                direction = ChangeDirection.INCREASE

            # 解析持有人类型
            _, holder_type = self._parse_change_info_v2(change_reason, executive_position)

            return {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'announcement_date': parsed_date,
                'change_date': parsed_date,
                'holder_name': holder_name,
                'holder_type': holder_type,
                'direction': direction,
                'change_shares': shares_num,
                'change_amount': None,  # 这个表格中没有变动金额
                'holding_ratio_after': ratio_num,
                'price_min': price_num,
                'price_max': price_num,
                'price_avg': price_num,
                'total_shares_after': shares_after_num,
                'change_reason': change_reason
            }

        except Exception as e:
            logger.warning(f"解析行数据失败: {e}")
            logger.debug(f"单元格内容: {[cell.get_text(strip=True) for cell in cells[:10]]}")
            return None
    
    def _parse_stock_info(self, stock_info: str) -> tuple:
        """解析股票代码和名称"""
        # 匹配格式：000001 平安银行
        match = re.match(r'(\d{6})\s+(.+)', stock_info.strip())
        if match:
            return match.group(1), match.group(2)
        return None, None
    
    def _parse_date(self, date_str: str) -> Optional[date]:
        """解析日期字符串"""
        try:
            # 支持多种日期格式
            for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%Y.%m.%d']:
                try:
                    return datetime.strptime(date_str.strip(), fmt).date()
                except ValueError:
                    continue
            return None
        except Exception:
            return None
    
    def _parse_change_info(self, change_info: str) -> tuple:
        """解析变动信息，确定增减持方向和持有人类型"""
        change_info = change_info.lower()

        # 判断增减持方向
        if '增持' in change_info or '买入' in change_info:
            direction = ChangeDirection.INCREASE
        elif '减持' in change_info or '卖出' in change_info:
            direction = ChangeDirection.DECREASE
        else:
            direction = ChangeDirection.INCREASE  # 默认增持

        # 判断持有人类型
        if any(keyword in change_info for keyword in ['董事', '监事', '高管', '高级管理']):
            holder_type = HolderType.EXECUTIVE
        elif any(keyword in change_info for keyword in ['机构', '基金', '保险', '券商']):
            holder_type = HolderType.INSTITUTION
        elif any(keyword in change_info for keyword in ['大股东', '控股']):
            holder_type = HolderType.MAJOR_SHAREHOLDER
        else:
            holder_type = HolderType.OTHER

        return direction, holder_type

    def _parse_change_info_v2(self, change_reason: str, executive_position: str) -> tuple:
        """解析变动信息，确定增减持方向和持有人类型（新版本）"""
        change_reason = change_reason.lower()
        executive_position = executive_position.lower()

        # 判断增减持方向 - 根据变动股数的正负号判断
        # 这里暂时默认为增持，实际应该根据变动股数的符号判断
        direction = ChangeDirection.INCREASE

        # 判断持有人类型
        if any(keyword in executive_position for keyword in ['董事', '监事', '高管', '高级管理']):
            holder_type = HolderType.EXECUTIVE
        elif any(keyword in change_reason for keyword in ['机构', '基金', '保险', '券商']):
            holder_type = HolderType.INSTITUTION
        elif any(keyword in executive_position for keyword in ['大股东', '控股']):
            holder_type = HolderType.MAJOR_SHAREHOLDER
        else:
            holder_type = HolderType.OTHER

        return direction, holder_type
    
    def _parse_number(self, num_str: str) -> Optional[float]:
        """解析数字字符串，支持万、亿等单位"""
        try:
            if not num_str or num_str.strip() in ['--', '-', '']:
                return None

            num_str = num_str.strip()

            # 检查是否包含万或亿
            multiplier = 1
            if '万' in num_str:
                multiplier = 10000
                num_str = num_str.replace('万', '')
            elif '亿' in num_str:
                multiplier = 100000000
                num_str = num_str.replace('亿', '')

            # 移除非数字字符，保留小数点和负号
            cleaned = re.sub(r'[^\d.-]', '', num_str)
            if cleaned:
                return float(cleaned) * multiplier
            return None
        except (ValueError, AttributeError):
            return None
    
    def _parse_percentage(self, percent_str: str) -> Optional[float]:
        """解析百分比字符串"""
        try:
            # 移除%符号和其他非数字字符
            cleaned = re.sub(r'[^\d.-]', '', percent_str.strip())
            if cleaned:
                return float(cleaned)
            return None
        except (ValueError, AttributeError):
            return None
    
    def _parse_price_range(self, price_str: str) -> tuple:
        """解析价格区间"""
        try:
            # 匹配格式：10.50-12.30 或 11.20
            if '-' in price_str:
                parts = price_str.split('-')
                if len(parts) == 2:
                    min_price = self._parse_number(parts[0])
                    max_price = self._parse_number(parts[1])
                    avg_price = (min_price + max_price) / 2 if min_price and max_price else None
                    return min_price, max_price, avg_price
            else:
                price = self._parse_number(price_str)
                return price, price, price
        except Exception:
            pass
        
        return None, None, None
