"""
通知配置服务
"""
import json
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_
from cryptography.fernet import Fernet
import base64
import os
from loguru import logger

from app.models.notification_config import NotificationConfig, NotificationServiceType
from app.schemas.notifications import NotificationType


class EncryptionService:
    """加密服务"""
    
    def __init__(self):
        # 从环境变量获取加密密钥，如果没有则生成一个
        key = os.getenv('NOTIFICATION_ENCRYPTION_KEY')
        if not key:
            # 生成新密钥并保存到环境变量（生产环境应该预先设置）
            key = Fernet.generate_key().decode()
            logger.warning("未找到加密密钥，已生成新密钥。请将以下密钥保存到环境变量 NOTIFICATION_ENCRYPTION_KEY:")
            logger.warning(f"NOTIFICATION_ENCRYPTION_KEY={key}")
        
        self.fernet = Fernet(key.encode() if isinstance(key, str) else key)
    
    def encrypt(self, data: str) -> str:
        """加密数据"""
        if not data:
            return data
        try:
            encrypted = self.fernet.encrypt(data.encode())
            return base64.b64encode(encrypted).decode()
        except Exception as e:
            logger.error(f"加密失败: {e}")
            return data
    
    def decrypt(self, encrypted_data: str) -> str:
        """解密数据"""
        if not encrypted_data:
            return encrypted_data
        try:
            decoded = base64.b64decode(encrypted_data.encode())
            decrypted = self.fernet.decrypt(decoded)
            return decrypted.decode()
        except Exception as e:
            logger.error(f"解密失败: {e}")
            return encrypted_data


class NotificationConfigService:
    """通知配置服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.encryption = EncryptionService()
    
    def create_config(self, config_data: Dict[str, Any]) -> NotificationConfig:
        """创建通知配置"""
        try:
            # 如果设置为默认配置，先取消其他默认配置
            if config_data.get('is_default', False):
                self._unset_default_config(config_data['service_type'])
            
            # 创建配置对象
            config = NotificationConfig()
            self._update_config_fields(config, config_data)
            
            # 加密敏感字段
            self._encrypt_sensitive_fields(config)
            
            self.db.add(config)
            self.db.commit()
            self.db.refresh(config)
            
            logger.info(f"创建通知配置成功: {config.name} ({config.service_type})")
            return config
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建通知配置失败: {e}")
            raise
    
    def update_config(self, config_id: int, config_data: Dict[str, Any]) -> Optional[NotificationConfig]:
        """更新通知配置"""
        try:
            config = self.db.query(NotificationConfig).filter(NotificationConfig.id == config_id).first()
            if not config:
                return None
            
            # 如果设置为默认配置，先取消其他默认配置
            if config_data.get('is_default', False) and not config.is_default:
                self._unset_default_config(config.service_type)
            
            # 更新字段
            self._update_config_fields(config, config_data)
            
            # 加密敏感字段
            self._encrypt_sensitive_fields(config)
            
            self.db.commit()
            self.db.refresh(config)
            
            logger.info(f"更新通知配置成功: {config.name} ({config.service_type})")
            return config
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新通知配置失败: {e}")
            raise
    
    def delete_config(self, config_id: int) -> bool:
        """删除通知配置"""
        try:
            config = self.db.query(NotificationConfig).filter(NotificationConfig.id == config_id).first()
            if not config:
                return False
            
            # 不允许删除默认配置
            if config.is_default:
                raise ValueError("不能删除默认配置")
            
            self.db.delete(config)
            self.db.commit()
            
            logger.info(f"删除通知配置成功: {config.name} ({config.service_type})")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除通知配置失败: {e}")
            raise
    
    def get_config(self, config_id: int, include_sensitive: bool = False) -> Optional[NotificationConfig]:
        """获取单个通知配置"""
        config = self.db.query(NotificationConfig).filter(NotificationConfig.id == config_id).first()
        if config and include_sensitive:
            self._decrypt_sensitive_fields(config)
        return config
    
    def get_configs(
        self, 
        service_type: Optional[NotificationServiceType] = None,
        is_enabled: Optional[bool] = None,
        include_sensitive: bool = False
    ) -> List[NotificationConfig]:
        """获取通知配置列表"""
        query = self.db.query(NotificationConfig)
        
        if service_type:
            query = query.filter(NotificationConfig.service_type == service_type)
        
        if is_enabled is not None:
            query = query.filter(NotificationConfig.is_enabled == is_enabled)
        
        configs = query.order_by(NotificationConfig.service_type, NotificationConfig.is_default.desc()).all()
        
        if include_sensitive:
            for config in configs:
                self._decrypt_sensitive_fields(config)
        
        return configs
    
    def get_default_config(self, service_type: NotificationServiceType) -> Optional[NotificationConfig]:
        """获取默认配置"""
        config = self.db.query(NotificationConfig).filter(
            and_(
                NotificationConfig.service_type == service_type,
                NotificationConfig.is_default == True
            )
        ).first()
        
        if config:
            self._decrypt_sensitive_fields(config)
        
        return config
    
    def get_enabled_configs(self) -> Dict[NotificationServiceType, NotificationConfig]:
        """获取所有启用的配置"""
        configs = self.db.query(NotificationConfig).filter(
            NotificationConfig.is_enabled == True
        ).all()
        
        result = {}
        for config in configs:
            self._decrypt_sensitive_fields(config)
            # 优先使用默认配置，如果没有默认配置则使用第一个启用的配置
            if config.service_type not in result or config.is_default:
                result[config.service_type] = config
        
        return result
    
    def test_config(self, config_id: int) -> Dict[str, Any]:
        """测试通知配置"""
        config = self.get_config(config_id, include_sensitive=True)
        if not config:
            return {"success": False, "message": "配置不存在"}
        
        try:
            # 这里可以添加实际的连接测试逻辑
            if config.service_type == NotificationServiceType.EMAIL:
                return self._test_email_config(config)
            elif config.service_type == NotificationServiceType.WECHAT:
                return self._test_wechat_config(config)
            elif config.service_type == NotificationServiceType.FEISHU:
                return self._test_feishu_config(config)
            else:
                return {"success": False, "message": "不支持的服务类型"}
                
        except Exception as e:
            logger.error(f"测试配置失败: {e}")
            return {"success": False, "message": f"测试失败: {str(e)}"}
    
    def _update_config_fields(self, config: NotificationConfig, data: Dict[str, Any]):
        """更新配置字段"""
        # 基础字段
        if 'name' in data:
            config.name = data['name']
        if 'service_type' in data:
            config.service_type = NotificationServiceType(data['service_type'])
        if 'is_enabled' in data:
            config.is_enabled = data['is_enabled']
        if 'is_default' in data:
            config.is_default = data['is_default']
        if 'description' in data:
            config.description = data['description']
        
        # 根据服务类型更新相应字段
        if config.service_type == NotificationServiceType.EMAIL:
            self._update_email_fields(config, data)
        elif config.service_type == NotificationServiceType.WECHAT:
            self._update_wechat_fields(config, data)
        elif config.service_type == NotificationServiceType.FEISHU:
            self._update_feishu_fields(config, data)
    
    def _update_email_fields(self, config: NotificationConfig, data: Dict[str, Any]):
        """更新邮件配置字段"""
        email_fields = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'from_email', 'from_name']
        for field in email_fields:
            if field in data:
                setattr(config, field, data[field])
    
    def _update_wechat_fields(self, config: NotificationConfig, data: Dict[str, Any]):
        """更新微信配置字段"""
        if 'wechat_webhook_url' in data:
            config.wechat_webhook_url = data['wechat_webhook_url']
        if 'wechat_mentioned_list' in data:
            config.wechat_mentioned_list = json.dumps(data['wechat_mentioned_list']) if data['wechat_mentioned_list'] else None
        if 'wechat_mentioned_mobile_list' in data:
            config.wechat_mentioned_mobile_list = json.dumps(data['wechat_mentioned_mobile_list']) if data['wechat_mentioned_mobile_list'] else None
    
    def _update_feishu_fields(self, config: NotificationConfig, data: Dict[str, Any]):
        """更新飞书配置字段"""
        if 'feishu_webhook_url' in data:
            config.feishu_webhook_url = data['feishu_webhook_url']
        if 'feishu_at_all' in data:
            config.feishu_at_all = data['feishu_at_all']
        if 'feishu_at_users' in data:
            config.feishu_at_users = json.dumps(data['feishu_at_users']) if data['feishu_at_users'] else None
    
    def _encrypt_sensitive_fields(self, config: NotificationConfig):
        """加密敏感字段"""
        if config.service_type == NotificationServiceType.EMAIL and config.smtp_password:
            config.smtp_password = self.encryption.encrypt(config.smtp_password)
        elif config.service_type == NotificationServiceType.WECHAT and config.wechat_webhook_url:
            config.wechat_webhook_url = self.encryption.encrypt(config.wechat_webhook_url)
        elif config.service_type == NotificationServiceType.FEISHU:
            if config.feishu_webhook_url:
                config.feishu_webhook_url = self.encryption.encrypt(config.feishu_webhook_url)
            if config.feishu_secret_key:
                config.feishu_secret_key = self.encryption.encrypt(config.feishu_secret_key)
    
    def _decrypt_sensitive_fields(self, config: NotificationConfig):
        """解密敏感字段"""
        if config.service_type == NotificationServiceType.EMAIL and config.smtp_password:
            config.smtp_password = self.encryption.decrypt(config.smtp_password)
        elif config.service_type == NotificationServiceType.WECHAT and config.wechat_webhook_url:
            config.wechat_webhook_url = self.encryption.decrypt(config.wechat_webhook_url)
        elif config.service_type == NotificationServiceType.FEISHU and config.feishu_webhook_url:
            config.feishu_webhook_url = self.encryption.decrypt(config.feishu_webhook_url)
    
    def _unset_default_config(self, service_type: NotificationServiceType):
        """取消指定服务类型的默认配置"""
        self.db.query(NotificationConfig).filter(
            and_(
                NotificationConfig.service_type == service_type,
                NotificationConfig.is_default == True
            )
        ).update({'is_default': False})
    
    def _test_email_config(self, config: NotificationConfig) -> Dict[str, Any]:
        """测试邮件配置"""
        # 这里可以添加实际的SMTP连接测试
        if not all([config.smtp_host, config.smtp_port, config.smtp_username, config.smtp_password]):
            return {"success": False, "message": "邮件配置不完整"}
        return {"success": True, "message": "邮件配置有效"}
    
    def _test_wechat_config(self, config: NotificationConfig) -> Dict[str, Any]:
        """测试微信配置"""
        if not config.wechat_webhook_url:
            return {"success": False, "message": "微信Webhook URL未配置"}
        return {"success": True, "message": "微信配置有效"}
    
    def _test_feishu_config(self, config: NotificationConfig) -> Dict[str, Any]:
        """测试飞书配置"""
        if not config.feishu_webhook_url:
            return {"success": False, "message": "飞书Webhook URL未配置"}
        return {"success": True, "message": "飞书配置有效"}
