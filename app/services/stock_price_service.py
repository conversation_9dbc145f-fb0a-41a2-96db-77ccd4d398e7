"""
股价数据服务
使用AkShare获取股价走势图数据
"""
from typing import Dict, List, Optional, Tuple
from datetime import datetime, date, timedelta
from loguru import logger
import pandas as pd
import time


class StockPriceService:
    """股价数据服务类"""

    def __init__(self):
        """初始化股价服务"""
        self.cache = {}  # 简单的内存缓存
        self.cache_ttl = 300  # 缓存5分钟
    
    def get_stock_price_data(
        self, 
        stock_code: str, 
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None,
        period: str = "daily"
    ) -> Dict:
        """
        获取股票价格数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            period: 数据周期 (daily, weekly, monthly)
            
        Returns:
            包含价格数据的字典
        """
        try:
            import akshare as ak

            # 设置默认日期范围
            if not end_date:
                end_date = date.today().strftime('%Y%m%d')
            else:
                end_date = end_date.replace('-', '')

            if not start_date:
                # 默认获取半年的数据
                start_date = (date.today() - timedelta(days=180)).strftime('%Y%m%d')
            else:
                start_date = start_date.replace('-', '')

            # 构建缓存键
            cache_key = f"{stock_code}_{start_date}_{end_date}_{period}"

            # 检查缓存
            if cache_key in self.cache:
                cache_data, cache_time = self.cache[cache_key]
                if time.time() - cache_time < self.cache_ttl:
                    logger.debug(f"从缓存获取股价数据: {stock_code}")
                    return cache_data
                else:
                    # 缓存过期，删除
                    del self.cache[cache_key]

            logger.info(f"获取股票 {stock_code} 从 {start_date} 到 {end_date} 的价格数据")
            
            # 根据周期选择不同的接口
            if period == "daily":
                df = ak.stock_zh_a_hist(symbol=stock_code, period="daily", start_date=start_date, end_date=end_date)
            elif period == "weekly":
                df = ak.stock_zh_a_hist(symbol=stock_code, period="weekly", start_date=start_date, end_date=end_date)
            elif period == "monthly":
                df = ak.stock_zh_a_hist(symbol=stock_code, period="monthly", start_date=start_date, end_date=end_date)
            else:
                df = ak.stock_zh_a_hist(symbol=stock_code, period="daily", start_date=start_date, end_date=end_date)
            
            if df is None or df.empty:
                logger.warning(f"未获取到股票 {stock_code} 的价格数据")
                return {"success": False, "message": "未获取到价格数据", "data": []}
            
            # 转换数据格式
            price_data = []
            for _, row in df.iterrows():
                price_data.append({
                    "date": row['日期'].strftime('%Y-%m-%d') if pd.notna(row['日期']) else None,
                    "open": float(row['开盘']) if pd.notna(row['开盘']) else None,
                    "high": float(row['最高']) if pd.notna(row['最高']) else None,
                    "low": float(row['最低']) if pd.notna(row['最低']) else None,
                    "close": float(row['收盘']) if pd.notna(row['收盘']) else None,
                    "volume": int(row['成交量']) if pd.notna(row['成交量']) else None,
                    "amount": float(row['成交额']) if pd.notna(row['成交额']) else None,
                    "change_pct": float(row['涨跌幅']) if pd.notna(row['涨跌幅']) else None,
                    "change_amount": float(row['涨跌额']) if pd.notna(row['涨跌额']) else None,
                    "turnover_rate": float(row['换手率']) if pd.notna(row['换手率']) else None,
                })
            
            logger.info(f"成功获取 {len(price_data)} 条价格数据")

            result = {
                "success": True,
                "data": price_data,
                "stock_code": stock_code,
                "period": period,
                "start_date": start_date,
                "end_date": end_date,
                "total_records": len(price_data)
            }

            # 缓存结果
            self.cache[cache_key] = (result, time.time())

            return result
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 价格数据失败: {e}")
            return {"success": False, "message": str(e), "data": []}
    
    def get_stock_price_with_holdings(
        self, 
        stock_code: str, 
        holding_changes: List[Dict],
        days_before: int = 30,
        days_after: int = 30
    ) -> Dict:
        """
        获取股票价格数据并标注增减持事件
        
        Args:
            stock_code: 股票代码
            holding_changes: 增减持变动记录列表
            days_before: 事件前天数
            days_after: 事件后天数
            
        Returns:
            包含价格数据和增减持标注的字典
        """
        try:
            if not holding_changes:
                logger.warning("没有增减持数据")
                return {"success": False, "message": "没有增减持数据", "data": []}
            
            # 计算日期范围
            dates = []
            for change in holding_changes:
                if change.get('announcement_date'):
                    if isinstance(change['announcement_date'], str):
                        dates.append(datetime.strptime(change['announcement_date'], '%Y-%m-%d').date())
                    elif isinstance(change['announcement_date'], date):
                        dates.append(change['announcement_date'])
            
            if not dates:
                logger.warning("增减持数据中没有有效日期")
                return {"success": False, "message": "增减持数据中没有有效日期", "data": []}
            
            min_date = min(dates) - timedelta(days=days_before)
            max_date = max(dates) + timedelta(days=days_after)
            
            # 获取价格数据
            price_result = self.get_stock_price_data(
                stock_code=stock_code,
                start_date=min_date.strftime('%Y-%m-%d'),
                end_date=max_date.strftime('%Y-%m-%d')
            )
            
            if not price_result["success"]:
                return price_result
            
            # 为价格数据添加增减持标注
            price_data = price_result["data"]
            holding_events = {}
            
            # 整理增减持事件
            for change in holding_changes:
                change_date = change.get('announcement_date')
                if change_date:
                    if isinstance(change_date, str):
                        date_key = change_date
                    elif isinstance(change_date, date):
                        date_key = change_date.strftime('%Y-%m-%d')
                    else:
                        continue
                    
                    if date_key not in holding_events:
                        holding_events[date_key] = []
                    
                    holding_events[date_key].append({
                        "holder_name": change.get('holder_name', ''),
                        "direction": change.get('direction', ''),
                        "change_shares": change.get('change_shares', 0),
                        "change_amount": change.get('change_amount', 0),
                        "change_reason": change.get('change_reason', ''),
                        "holder_type": change.get('holder_type', '')
                    })
            
            # 为每个价格数据点添加增减持标注
            for price_point in price_data:
                price_date = price_point["date"]
                if price_date in holding_events:
                    price_point["holding_changes"] = holding_events[price_date]
                else:
                    price_point["holding_changes"] = []
            
            return {
                "success": True,
                "data": price_data,
                "stock_code": stock_code,
                "holding_events_count": len(holding_events),
                "date_range": {
                    "start": min_date.strftime('%Y-%m-%d'),
                    "end": max_date.strftime('%Y-%m-%d')
                },
                "total_records": len(price_data)
            }
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 带增减持标注的价格数据失败: {e}")
            return {"success": False, "message": str(e), "data": []}
    
    def get_stock_basic_info(self, stock_code: str) -> Dict:
        """
        获取股票基本信息
        
        Args:
            stock_code: 股票代码
            
        Returns:
            股票基本信息
        """
        try:
            import akshare as ak
            
            # 获取股票基本信息
            df = ak.stock_individual_info_em(symbol=stock_code)
            
            if df is None or df.empty:
                logger.warning(f"未获取到股票 {stock_code} 的基本信息")
                return {"success": False, "message": "未获取到股票基本信息"}
            
            # 转换为字典格式
            info_dict = {}
            for _, row in df.iterrows():
                key = row['item']
                value = row['value']
                info_dict[key] = value
            
            return {
                "success": True,
                "data": info_dict,
                "stock_code": stock_code
            }
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 基本信息失败: {e}")
            return {"success": False, "message": str(e)}
    
    def get_realtime_price(self, stock_code: str) -> Dict:
        """
        获取股票实时价格
        
        Args:
            stock_code: 股票代码
            
        Returns:
            实时价格信息
        """
        try:
            import akshare as ak
            
            # 获取实时行情
            df = ak.stock_zh_a_spot_em()
            
            if df is None or df.empty:
                logger.warning("未获取到实时行情数据")
                return {"success": False, "message": "未获取到实时行情数据"}
            
            # 查找指定股票
            stock_data = df[df['代码'] == stock_code]
            
            if stock_data.empty:
                logger.warning(f"未找到股票 {stock_code} 的实时数据")
                return {"success": False, "message": f"未找到股票 {stock_code} 的实时数据"}
            
            row = stock_data.iloc[0]
            
            return {
                "success": True,
                "data": {
                    "stock_code": stock_code,
                    "stock_name": row['名称'],
                    "current_price": float(row['最新价']),
                    "change_amount": float(row['涨跌额']),
                    "change_pct": float(row['涨跌幅']),
                    "open_price": float(row['今开']),
                    "high_price": float(row['最高']),
                    "low_price": float(row['最低']),
                    "volume": int(row['成交量']),
                    "amount": float(row['成交额']),
                    "turnover_rate": float(row['换手率']),
                    "pe_ratio": float(row['市盈率-动态']) if pd.notna(row['市盈率-动态']) else None,
                    "pb_ratio": float(row['市净率']) if pd.notna(row['市净率']) else None,
                    "market_cap": float(row['总市值']) if pd.notna(row['总市值']) else None,
                    "update_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            }
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 实时价格失败: {e}")
            return {"success": False, "message": str(e)}
