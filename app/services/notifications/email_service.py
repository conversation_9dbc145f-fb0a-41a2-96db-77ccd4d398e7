"""
邮件通知服务
"""
import aiosmtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import Dict, Any, List, Optional
from pathlib import Path
from jinja2 import Template
from loguru import logger

from app.schemas.notifications import (
    NotificationContentBase,
    NotificationPriority,
    EmailNotificationConfig
)
from .base import BaseNotificationService, NotificationResult


class EmailNotificationService(BaseNotificationService):
    """邮件通知服务"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.smtp_host = config.get('smtp_host', 'smtp.qq.com')
        self.smtp_port = config.get('smtp_port', 587)
        self.smtp_username = config.get('smtp_username', '')
        self.smtp_password = config.get('smtp_password', '')
        self.from_email = config.get('from_email', '')
        self.from_name = config.get('from_name', '增减持数据分析平台')
        
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证邮件配置"""
        required_fields = ['to_emails', 'subject']
        return all(field in config for field in required_fields)
    
    async def send_notification(
        self,
        content: NotificationContentBase,
        config: Dict[str, Any],
        priority: NotificationPriority = NotificationPriority.NORMAL
    ) -> NotificationResult:
        """发送邮件通知"""
        try:
            if not self.validate_config(config):
                return NotificationResult(
                    success=False,
                    message="邮件配置验证失败"
                )
            
            # 创建邮件消息
            message = await self._create_email_message(content, config)
            
            # 发送邮件
            await self._send_email(message, config['to_emails'])
            
            return NotificationResult(
                success=True,
                message="邮件发送成功",
                data={'recipients': config['to_emails']}
            )
            
        except Exception as e:
            logger.error(f"邮件发送失败: {str(e)}")
            return NotificationResult(
                success=False,
                message=f"邮件发送失败: {str(e)}",
                error=e
            )
    
    async def _create_email_message(
        self,
        content: NotificationContentBase,
        config: Dict[str, Any]
    ) -> MIMEMultipart:
        """创建邮件消息"""
        message = MIMEMultipart('alternative')
        
        # 设置邮件头
        # QQ邮箱要求From头部格式严格，使用简单格式
        message['From'] = self.from_email  # 只使用邮箱地址，不使用显示名称
        message['To'] = ', '.join(config['to_emails'])
        message['Subject'] = config['subject']
        
        # 设置抄送和密送
        if config.get('cc_emails'):
            message['Cc'] = ', '.join(config['cc_emails'])
        if config.get('bcc_emails'):
            message['Bcc'] = ', '.join(config['bcc_emails'])
        
        # 添加文本内容
        text_part = MIMEText(content.content, 'plain', 'utf-8')
        message.attach(text_part)
        
        # 添加HTML内容
        html_content = config.get('html_content')
        if not html_content and content.data:
            # 如果没有提供HTML内容，但有数据，则生成HTML
            html_content = self._generate_html_content(content)
        
        if html_content:
            html_part = MIMEText(html_content, 'html', 'utf-8')
            message.attach(html_part)
        
        # 添加附件
        attachments = config.get('attachments', [])
        for attachment_path in attachments:
            await self._add_attachment(message, attachment_path)
        
        return message
    
    async def _send_email(self, message: MIMEMultipart, recipients: List[str]):
        """发送邮件"""
        # 获取所有收件人（包括抄送和密送）
        all_recipients = recipients.copy()

        if message.get('Cc'):
            all_recipients.extend(message['Cc'].split(', '))
        if message.get('Bcc'):
            all_recipients.extend(message['Bcc'].split(', '))

        # 尝试不同的连接方式
        smtp_configs = []

        if self.smtp_port == 465:
            # 465端口通常使用SSL
            smtp_configs = [
                {'use_tls': True, 'start_tls': False},
                {'use_tls': False, 'start_tls': True}
            ]
        else:
            # 587端口通常使用STARTTLS
            smtp_configs = [
                {'use_tls': False, 'start_tls': True},
                {'use_tls': True, 'start_tls': False},
                {'use_tls': False, 'start_tls': False}  # 最后尝试不加密
            ]

        last_error = None
        for config in smtp_configs:
            try:
                async with aiosmtplib.SMTP(
                    hostname=self.smtp_host,
                    port=self.smtp_port,
                    **config
                ) as smtp:
                    await smtp.login(self.smtp_username, self.smtp_password)
                    await smtp.send_message(
                        message,
                        sender=self.from_email,
                        recipients=all_recipients
                    )
                    logger.info(f"邮件发送成功，使用配置: {config}")
                    return  # 成功发送，退出
            except Exception as e:
                last_error = e
                logger.warning(f"SMTP配置 {config} 失败: {str(e)}")
                continue

        # 如果所有配置都失败，抛出最后一个错误
        if last_error:
            raise last_error
    
    async def _add_attachment(self, message: MIMEMultipart, file_path: str):
        """添加附件"""
        try:
            path = Path(file_path)
            if not path.exists():
                logger.warning(f"附件文件不存在: {file_path}")
                return
            
            with open(path, 'rb') as f:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(f.read())
                encoders.encode_base64(part)
                part.add_header(
                    'Content-Disposition',
                    f'attachment; filename= {path.name}'
                )
                message.attach(part)
                
        except Exception as e:
            logger.error(f"添加附件失败 {file_path}: {str(e)}")
    
    def _generate_html_content(self, content: NotificationContentBase) -> str:
        """生成HTML邮件内容"""
        if not content.data:
            return f"<html><body><p>{content.content}</p></body></html>"
        
        # 如果是增减持数据，使用专门的模板
        if 'total_count' in content.data:
            return self._generate_holding_changes_html(content.data)
        
        # 默认HTML模板
        html_template = """
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .header { background-color: #f4f4f4; padding: 20px; text-align: center; }
                .content { padding: 20px; }
                .footer { background-color: #f4f4f4; padding: 10px; text-align: center; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h2>{{ title }}</h2>
            </div>
            <div class="content">
                <p>{{ content }}</p>
                {% if summary %}
                <p><strong>摘要:</strong> {{ summary }}</p>
                {% endif %}
            </div>
            <div class="footer">
                <p>此邮件由增减持数据分析平台自动发送</p>
            </div>
        </body>
        </html>
        """
        
        template = Template(html_template)
        return template.render(
            title=content.title,
            content=content.content,
            summary=content.summary
        )
    
    def _generate_holding_changes_html(self, holding_data: Dict[str, Any]) -> str:
        """生成增减持数据的HTML邮件"""
        html_template = """
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                .container { max-width: 800px; margin: 0 auto; background-color: #fff; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
                .header h1 { margin: 0; font-size: 28px; }
                .stats { display: flex; justify-content: space-around; padding: 30px; background-color: #f8f9fa; }
                .stat-item { text-align: center; }
                .stat-number { font-size: 32px; font-weight: bold; color: #667eea; }
                .stat-label { font-size: 14px; color: #666; margin-top: 5px; }
                .content { padding: 30px; }
                .summary { background-color: #e3f2fd; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
                .changes-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                .changes-table th, .changes-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
                .changes-table th { background-color: #f5f5f5; font-weight: bold; }
                .increase { color: #d32f2f; }
                .decrease { color: #388e3c; }
                .footer { background-color: #f4f4f4; padding: 20px; text-align: center; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>📊 {{ date }} 增减持数据报告</h1>
                </div>
                
                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-number">{{ total_count }}</div>
                        <div class="stat-label">总记录数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number increase">{{ increase_count }}</div>
                        <div class="stat-label">增持记录</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number decrease">{{ decrease_count }}</div>
                        <div class="stat-label">减持记录</div>
                    </div>
                </div>
                
                <div class="content">
                    {% if summary %}
                    <div class="summary">
                        <h3>💡 数据摘要</h3>
                        <p>{{ summary }}</p>
                    </div>
                    {% endif %}
                    
                    {% if top_changes %}
                    <h3>🔥 重要变动</h3>
                    <table class="changes-table">
                        <thead>
                            <tr>
                                <th>股票名称</th>
                                <th>变动人</th>
                                <th>方向</th>
                                <th>变动股数(万股)</th>
                                <th>变动金额(万元)</th>
                                <th>变动比例(%)</th>
                                <th>成交均价(元)</th>
                                <th>变动原因</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for change in top_changes[:10] %}
                            <tr>
                                <td>{{ change.stock_name or '未知' }}</td>
                                <td>{{ change.holder_name or '未知' }}</td>
                                <td class="{{ 'increase' if change.direction == 'increase' else 'decrease' }}">
                                    {{ '🔺增持' if change.direction == 'increase' else '🔻减持' }}
                                </td>
                                <td>{{ "%.2f"|format(change.change_shares or 0) }}</td>
                                <td>{{ "%.2f"|format(change.change_amount or 0) }}</td>
                                <td>{{ "%.2f"|format(change.change_ratio or 0) }}</td>
                                <td>{{ "%.2f"|format(change.price_avg or 0) if change.price_avg and change.price_avg > 0 else '-' }}</td>
                                <td>{{ change.change_reason or '未说明' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% endif %}
                </div>
                
                <div class="footer">
                    <p>此邮件由增减持数据分析平台自动发送 | 发送时间: {{ current_time }}</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        from datetime import datetime
        template = Template(html_template)
        return template.render(
            date=holding_data.get('date', '今日'),
            total_count=holding_data.get('total_count', 0),
            increase_count=holding_data.get('increase_count', 0),
            decrease_count=holding_data.get('decrease_count', 0),
            summary=holding_data.get('summary'),
            top_changes=holding_data.get('top_changes', []),
            current_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
