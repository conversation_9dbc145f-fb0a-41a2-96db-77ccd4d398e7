"""
企业微信机器人通知服务
"""
import aiohttp
import json
from typing import Dict, Any, List, Optional
from loguru import logger

from app.schemas.notifications import (
    NotificationContentBase,
    NotificationPriority
)
from .base import BaseNotificationService, NotificationResult


class WechatNotificationService(BaseNotificationService):
    """企业微信机器人通知服务"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.default_webhook_url = config.get('webhook_url', '')
        
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证微信配置"""
        webhook_url = config.get('webhook_url', self.default_webhook_url)
        return bool(webhook_url and webhook_url.startswith('https://qyapi.weixin.qq.com/'))
    
    async def send_notification(
        self,
        content: NotificationContentBase,
        config: Dict[str, Any],
        priority: NotificationPriority = NotificationPriority.NORMAL
    ) -> NotificationResult:
        """发送微信通知"""
        try:
            if not self.validate_config(config):
                return NotificationResult(
                    success=False,
                    message="微信配置验证失败"
                )
            
            webhook_url = config.get('webhook_url', self.default_webhook_url)
            
            # 根据内容类型选择消息格式
            if content.data and 'total_count' in content.data:
                # 增减持数据使用Markdown格式
                payload = self._create_markdown_message(content, config)
            else:
                # 普通消息使用文本格式
                payload = self._create_text_message(content, config)
            
            # 发送请求
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook_url,
                    json=payload,
                    headers={'Content-Type': 'application/json'}
                ) as response:
                    result_data = await response.json()
                    
                    if result_data.get('errcode') == 0:
                        return NotificationResult(
                            success=True,
                            message="微信消息发送成功",
                            data=result_data
                        )
                    else:
                        return NotificationResult(
                            success=False,
                            message=f"微信消息发送失败: {result_data.get('errmsg', '未知错误')}",
                            data=result_data
                        )
                        
        except Exception as e:
            logger.error(f"微信消息发送异常: {str(e)}")
            return NotificationResult(
                success=False,
                message=f"微信消息发送异常: {str(e)}",
                error=e
            )
    
    def _create_text_message(
        self,
        content: NotificationContentBase,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建文本消息"""
        message_content = f"{content.title}\n\n{content.content}"
        
        if content.summary:
            message_content += f"\n\n💡 {content.summary}"
        
        payload = {
            "msgtype": "text",
            "text": {
                "content": message_content
            }
        }
        
        # 添加@用户
        mentioned_list = config.get('mentioned_list', [])
        mentioned_mobile_list = config.get('mentioned_mobile_list', [])
        
        if mentioned_list or mentioned_mobile_list:
            payload["text"]["mentioned_list"] = mentioned_list
            payload["text"]["mentioned_mobile_list"] = mentioned_mobile_list
        
        return payload
    
    def _create_markdown_message(
        self,
        content: NotificationContentBase,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建Markdown消息（用于增减持数据）"""
        holding_data = content.data
        markdown_content = self._format_holding_changes_markdown(holding_data)
        
        payload = {
            "msgtype": "markdown",
            "markdown": {
                "content": markdown_content
            }
        }
        
        return payload
    
    def _format_holding_changes_markdown(self, holding_data: Dict[str, Any]) -> str:
        """格式化增减持数据为Markdown"""
        date = holding_data.get('date', '今日')
        total = holding_data.get('total_count', 0)
        increase = holding_data.get('increase_count', 0)
        decrease = holding_data.get('decrease_count', 0)
        
        # 构建Markdown内容
        content = f"# 📊 {date} 增减持数据报告\n\n"
        
        # 数据概览
        content += "## 📈 数据概览\n\n"
        content += f"> **总记录数**: <font color=\"info\">{total}</font>\n"
        content += f"> **增持记录**: <font color=\"warning\">{increase}</font>\n"
        content += f"> **减持记录**: <font color=\"comment\">{decrease}</font>\n\n"
        
        # 数据摘要
        if holding_data.get('summary'):
            content += f"## 💡 数据摘要\n\n{holding_data['summary']}\n\n"
        
        # 重要变动
        top_changes = holding_data.get('top_changes', [])
        if top_changes:
            content += "## 🔥 重要变动\n\n"
            
            for i, change in enumerate(top_changes[:8], 1):  # 微信限制，最多显示8条
                stock_name = change.get('stock_name', '未知')
                holder_name = change.get('holder_name', '未知')
                direction = change.get('direction', '')
                shares = change.get('change_shares', 0)
                amount = change.get('change_amount', 0)
                
                if direction == 'increase':
                    direction_text = "<font color=\"warning\">🔺增持</font>"
                else:
                    direction_text = "<font color=\"comment\">🔻减持</font>"
                
                content += f"**{i}.** {stock_name}\n"
                content += f"   变动人: {holder_name}\n"
                content += f"   方向: {direction_text}\n"
                content += f"   股数: {shares:.2f}万股"
                
                if amount > 0:
                    content += f" | 金额: {amount:.2f}万元"
                
                content += "\n\n"
        
        # 添加时间戳
        from datetime import datetime
        content += f"---\n<font color=\"comment\">发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</font>"
        
        return content
    
    def _create_news_message(
        self,
        content: NotificationContentBase,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建图文消息"""
        articles = []
        
        # 主文章
        main_article = {
            "title": content.title,
            "description": content.summary or content.content[:100],
            "url": config.get('detail_url', ''),
            "picurl": config.get('pic_url', '')
        }
        articles.append(main_article)
        
        # 如果有增减持数据，添加子文章
        if content.data and 'top_changes' in content.data:
            top_changes = content.data['top_changes'][:3]  # 最多3个子文章
            for change in top_changes:
                article = {
                    "title": f"{change.get('stock_name', '未知')} - {change.get('holder_name', '未知')}",
                    "description": f"{'增持' if change.get('direction') == 'increase' else '减持'} {change.get('change_shares', 0):.2f}万股",
                    "url": config.get('detail_url', ''),
                    "picurl": ""
                }
                articles.append(article)
        
        payload = {
            "msgtype": "news",
            "news": {
                "articles": articles
            }
        }
        
        return payload
    
    async def send_file_message(
        self,
        file_path: str,
        webhook_url: str,
        filename: Optional[str] = None
    ) -> NotificationResult:
        """发送文件消息"""
        try:
            # 企业微信机器人需要先上传文件获取media_id
            # 这里简化处理，实际使用时需要实现文件上传逻辑
            return NotificationResult(
                success=False,
                message="企业微信机器人暂不支持直接发送文件，请使用其他消息类型"
            )
            
        except Exception as e:
            logger.error(f"微信文件发送异常: {str(e)}")
            return NotificationResult(
                success=False,
                message=f"微信文件发送异常: {str(e)}",
                error=e
            )
    
    def get_message_types(self) -> List[str]:
        """获取支持的消息类型"""
        return ["text", "markdown", "news"]
    
    def format_at_users(self, user_list: List[str], mobile_list: List[str] = None) -> str:
        """格式化@用户"""
        at_text = ""
        
        if user_list:
            for user in user_list:
                at_text += f"<@{user}> "
        
        if mobile_list:
            for mobile in mobile_list:
                at_text += f"<@{mobile}> "
        
        return at_text.strip()
