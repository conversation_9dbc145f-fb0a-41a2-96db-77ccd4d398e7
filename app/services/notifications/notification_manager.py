"""
通知管理器
"""
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, date, timedelta
from loguru import logger
from sqlalchemy.orm import Session

from app.core.config import settings
from app.schemas.notifications import (
    NotificationType,
    NotificationPriority,
    NotificationContentBase,
    BatchNotificationRequest,
    HoldingChangeNotificationData,
    NotificationStats
)
from .base import BaseNotificationService, NotificationResult
from .email_service import EmailNotificationService
from .wechat_service import WechatNotificationService
from .feishu_service import FeishuNotificationService
from app.services.notification_config_service import NotificationConfigService
from app.models.notification_config import NotificationServiceType


class NotificationManager:
    """通知管理器"""
    
    def __init__(self):
        """初始化通知管理器"""
        self.services: Dict[NotificationType, BaseNotificationService] = {}
        self._initialize_services()

    def reload_services(self, db: Session):
        """重新加载通知服务配置"""
        self.services.clear()
        self._initialize_services_from_db(db)
        
    def _initialize_services(self):
        """初始化通知服务"""
        # 邮件服务
        if settings.ENABLE_EMAIL_NOTIFICATION:
            email_config = {
                'enabled': True,
                'smtp_host': settings.SMTP_HOST,
                'smtp_port': settings.SMTP_PORT,
                'smtp_username': settings.SMTP_USERNAME,
                'smtp_password': settings.SMTP_PASSWORD,
                'from_email': settings.SMTP_FROM_EMAIL,
                'from_name': settings.SMTP_FROM_NAME
            }
            self.services[NotificationType.EMAIL] = EmailNotificationService(email_config)
        
        # 微信服务
        if settings.ENABLE_WECHAT_NOTIFICATION:
            wechat_config = {
                'enabled': True,
                'webhook_url': settings.WECHAT_WEBHOOK_URL
            }
            self.services[NotificationType.WECHAT] = WechatNotificationService(wechat_config)
        
        # 飞书服务
        if settings.ENABLE_FEISHU_NOTIFICATION:
            feishu_config = {
                'enabled': True,
                'webhook_url': settings.FEISHU_WEBHOOK_URL,
                'secret_key': settings.FEISHU_SECRET_KEY
            }
            self.services[NotificationType.FEISHU] = FeishuNotificationService(feishu_config)
        
        logger.info(f"通知管理器初始化完成，已启用服务: {list(self.services.keys())}")

    def _initialize_services_from_db(self, db: Session):
        """从数据库配置初始化通知服务（简化版，结合环境变量）"""
        try:
            from app.services.simple_notification_config_service import SimpleNotificationConfigService
            from app.core.config import settings

            # 使用简化的配置服务
            config_service = SimpleNotificationConfigService(db)
            enabled_configs = config_service.get_enabled_configs()

            # 清空现有服务
            self.services.clear()

            for config in enabled_configs:
                if config.service_type.value == 'email':
                    # 邮件服务：从数据库读取SMTP配置
                    from app.services.system_config_service import SystemConfigManager
                    config_manager = SystemConfigManager(db)

                    # 获取SMTP配置
                    smtp_host = config_manager.get_config_value('smtp_host', 'smtp.qq.com')
                    smtp_port = int(config_manager.get_config_value('smtp_port', '465'))
                    smtp_username = config_manager.get_config_value('smtp_username', settings.SMTP_USERNAME)
                    smtp_password = config_manager.get_config_value('smtp_password', settings.SMTP_PASSWORD)
                    from_email = config_manager.get_config_value('smtp_from_email', settings.SMTP_FROM_EMAIL)

                    email_config = {
                        'enabled': True,
                        'smtp_host': smtp_host,
                        'smtp_port': smtp_port,
                        'smtp_username': smtp_username,
                        'smtp_password': smtp_password,
                        'from_email': from_email,
                        'from_name': settings.SMTP_FROM_NAME
                    }
                    self.services[NotificationType.EMAIL] = EmailNotificationService(email_config)

                elif config.service_type.value == 'wechat':
                    # 微信服务：使用环境变量中的配置
                    wechat_config = {
                        'enabled': True,
                        'webhook_url': settings.WECHAT_WEBHOOK_URL
                    }
                    self.services[NotificationType.WECHAT] = WechatNotificationService(wechat_config)

                elif config.service_type.value == 'feishu':
                    # 飞书服务：使用环境变量中的配置
                    feishu_config = {
                        'enabled': True,
                        'webhook_url': settings.FEISHU_WEBHOOK_URL,
                        'secret_key': settings.FEISHU_SECRET_KEY
                    }
                    self.services[NotificationType.FEISHU] = FeishuNotificationService(feishu_config)

            logger.info(f"从数据库加载通知服务配置完成，已启用服务: {list(self.services.keys())}")

        except Exception as e:
            logger.error(f"从数据库加载通知配置失败: {e}")
            # 回退到环境变量配置
            self._initialize_services()
    
    async def send_notification(
        self,
        notification_type: NotificationType,
        content: NotificationContentBase,
        config: Dict[str, Any],
        priority: NotificationPriority = NotificationPriority.NORMAL,
        max_retries: int = 3
    ) -> NotificationResult:
        """发送单个通知"""
        service = self.services.get(notification_type)
        if not service:
            return NotificationResult(
                success=False,
                message=f"通知服务 {notification_type} 未启用或不存在"
            )
        
        if not service.is_enabled:
            return NotificationResult(
                success=False,
                message=f"通知服务 {notification_type} 已禁用"
            )
        
        return await service.send_with_retry(
            content=content,
            config=config,
            priority=priority,
            max_retries=max_retries
        )
    
    async def send_batch_notification(
        self,
        request: BatchNotificationRequest
    ) -> Dict[NotificationType, NotificationResult]:
        """批量发送通知"""
        results = {}

        # 并发发送所有通知
        tasks = []
        for notification_type in request.notification_types:
            if notification_type in self.services:
                # 为不同通知类型生成相应的配置
                config = self._get_notification_config(notification_type)
                task = self._send_single_notification_task(
                    notification_type,
                    request.content,
                    config,
                    request.priority
                )
                tasks.append((notification_type, task))
        
        # 等待所有任务完成
        for notification_type, task in tasks:
            try:
                result = await task
                results[notification_type] = result
            except Exception as e:
                logger.error(f"批量通知发送异常 {notification_type}: {str(e)}")
                results[notification_type] = NotificationResult(
                    success=False,
                    message=f"发送异常: {str(e)}",
                    error=e
                )
        
        return results

    def _get_notification_config(self, notification_type: NotificationType) -> Dict[str, Any]:
        """获取通知类型对应的配置"""
        if notification_type == NotificationType.EMAIL:
            # 邮件配置需要收件人和主题
            recipients = []

            # 从数据库配置中获取收件人
            try:
                from app.core.database import SessionLocal
                from app.models.notification_config import NotificationConfig

                db = SessionLocal()
                try:
                    email_config = db.query(NotificationConfig).filter(
                        NotificationConfig.service_type == 'EMAIL',
                        NotificationConfig.is_enabled == True
                    ).first()

                    if email_config and email_config.email_recipients:
                        try:
                            import json
                            recipients = json.loads(email_config.email_recipients) if isinstance(email_config.email_recipients, str) else email_config.email_recipients
                        except (json.JSONDecodeError, TypeError):
                            recipients = email_config.email_recipients if isinstance(email_config.email_recipients, list) else []
                finally:
                    db.close()
            except Exception as e:
                logger.error(f"获取邮件收件人配置失败: {e}")

            # 如果没有配置收件人，使用默认邮箱
            if not recipients:
                recipients = ['<EMAIL>']  # 使用配置的默认邮箱

            return {
                'to_emails': recipients,
                'subject': '📊 每日增持数据通知 - 增减持数据分析平台'
            }

        elif notification_type == NotificationType.WECHAT:
            # 微信配置
            config = {}
            try:
                from app.core.database import SessionLocal
                from app.models.notification_config import NotificationConfig

                db = SessionLocal()
                try:
                    wechat_config = db.query(NotificationConfig).filter(
                        NotificationConfig.service_type == 'WECHAT',
                        NotificationConfig.is_enabled == True
                    ).first()

                    if wechat_config and wechat_config.wechat_mentioned_users:
                        try:
                            import json
                            mentioned_users = json.loads(wechat_config.wechat_mentioned_users)
                            config['mentioned_list'] = mentioned_users
                        except (json.JSONDecodeError, TypeError):
                            pass
                finally:
                    db.close()
            except Exception as e:
                logger.error(f"获取微信配置失败: {e}")
            return config

        elif notification_type == NotificationType.FEISHU:
            # 飞书配置
            config = {}
            try:
                from app.core.database import SessionLocal
                from app.models.notification_config import NotificationConfig

                db = SessionLocal()
                try:
                    feishu_config = db.query(NotificationConfig).filter(
                        NotificationConfig.service_type == 'FEISHU',
                        NotificationConfig.is_enabled == True
                    ).first()

                    if feishu_config:
                        if feishu_config.feishu_at_all:
                            config['at_all'] = feishu_config.feishu_at_all
                        if feishu_config.feishu_at_users:
                            try:
                                import json
                                at_users = json.loads(feishu_config.feishu_at_users)
                                config['at_users'] = at_users
                            except (json.JSONDecodeError, TypeError):
                                pass
                finally:
                    db.close()
            except Exception as e:
                logger.error(f"获取飞书配置失败: {e}")
            return config

        # 默认返回空配置
        return {}

    async def _send_single_notification_task(
        self,
        notification_type: NotificationType,
        content: NotificationContentBase,
        config: Dict[str, Any],
        priority: NotificationPriority
    ) -> NotificationResult:
        """单个通知发送任务"""
        return await self.send_notification(
            notification_type=notification_type,
            content=content,
            config=config,
            priority=priority
        )
    
    async def send_daily_holding_changes_notification(
        self,
        db: Session,
        target_date: Optional[date] = None
    ) -> Dict[NotificationType, NotificationResult]:
        """发送每日增减持数据通知（近7天增持信息）"""
        if target_date is None:
            target_date = date.today()  # 使用今天作为结束日期

        try:
            # 重新加载数据库配置
            self._initialize_services_from_db(db)

            # 获取近7天的增持数据
            holding_data = await self._get_weekly_increase_holding_data(db, target_date)

            if holding_data['total_count'] == 0:
                logger.info(f"近7天无增持数据，跳过通知")
                return {}

            # 构建通知内容
            content = self._build_weekly_increase_content(holding_data)

            # 发送到所有启用的通知渠道
            notification_types = list(self.services.keys())

            if not notification_types:
                logger.info("没有启用的通知服务")
                return {}

            batch_request = BatchNotificationRequest(
                notification_types=notification_types,
                content=content,
                priority=NotificationPriority.NORMAL
            )

            results = await self.send_batch_notification(batch_request)

            # 记录发送结果
            success_count = sum(1 for result in results.values() if result.success)
            total_count = len(results)

            logger.info(f"每日增持通知发送完成: {success_count}/{total_count} 成功")

            return results

        except Exception as e:
            logger.error(f"发送每日增持通知失败: {str(e)}")
            return {}

    async def send_weekly_increase_email_notification(
        self,
        db: Session,
        target_date: Optional[date] = None
    ) -> Optional[NotificationResult]:
        """发送每周增持数据邮件通知（只发送邮件）"""
        if target_date is None:
            target_date = date.today()

        try:
            # 重新加载数据库配置
            self._initialize_services_from_db(db)

            # 检查邮件服务是否启用
            if NotificationType.EMAIL not in self.services:
                logger.info("邮件服务未启用，跳过邮件通知")
                return None

            # 获取近7天的增持数据
            holding_data = await self._get_weekly_increase_holding_data(db, target_date)

            if holding_data['total_count'] == 0:
                logger.info(f"近7天无增持数据，跳过邮件通知")
                return None

            # 构建通知内容
            content = self._build_weekly_increase_content(holding_data)

            # 只发送邮件通知
            result = await self.send_notification(
                notification_type=NotificationType.EMAIL,
                content=content,
                config={
                    'to_emails': ['<EMAIL>'],  # 可以从配置中读取
                    'subject': f'📊 近7天增持数据报告 - {holding_data["date"]}'
                },
                priority=NotificationPriority.NORMAL
            )

            if result.success:
                logger.info(f"每周增持邮件通知发送成功")
            else:
                logger.error(f"每周增持邮件通知发送失败: {result.message}")

            return result

        except Exception as e:
            logger.error(f"发送每周增持邮件通知失败: {str(e)}")
            return None
    
    async def _get_weekly_increase_holding_data(
        self,
        db: Session,
        end_date: date
    ) -> Dict[str, Any]:
        """获取近7天的增持数据"""
        from app.models.holdings import HoldingChange, Stock
        from sqlalchemy import func, and_

        # 计算开始日期（7天前）
        start_date = end_date - timedelta(days=7)

        # 查询近7天的增持记录（只查询增持，不包括减持）
        query = db.query(HoldingChange).join(Stock).filter(
            and_(
                HoldingChange.announcement_date >= start_date,
                HoldingChange.announcement_date <= end_date,
                HoldingChange.direction == 'increase'  # 只查询增持
            )
        )

        all_increases = query.all()
        total_count = len(all_increases)

        # 计算总金额
        total_amount = sum(change.change_amount or 0 for change in all_increases)

        # 获取重要增持（按变动金额排序）
        top_changes = []
        for change in sorted(all_increases, key=lambda x: x.change_amount or 0, reverse=True)[:15]:
            top_changes.append({
                'stock_name': change.stock.name,
                'stock_code': change.stock.code,
                'holder_name': change.holder_name,
                'direction': change.direction,
                'change_shares': change.change_shares,
                'change_amount': change.change_amount,
                'change_ratio': change.holding_ratio_after,  # 变动比例
                'change_reason': change.change_reason,  # 变动原因
                'announcement_date': change.announcement_date.strftime('%Y-%m-%d'),
                'price_avg': change.price_avg
            })

        # 生成摘要
        summary = self._generate_weekly_increase_summary(total_count, total_amount, top_changes, start_date, end_date)

        return {
            'date': f"{start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}",
            'start_date': start_date,
            'end_date': end_date,
            'total_count': total_count,
            'increase_count': total_count,  # 全部都是增持
            'decrease_count': 0,  # 不包括减持
            'total_amount': total_amount,
            'top_changes': top_changes,
            'summary': summary,
            'stats': {
                'avg_amount': total_amount / total_count if total_count > 0 else 0,
                'stock_count': len(set(change['stock_name'] for change in top_changes)),
                'days': 7
            }
        }

    async def _get_daily_holding_changes_data(
        self,
        db: Session,
        target_date: date
    ) -> Dict[str, Any]:
        """获取每日增减持数据"""
        from app.models.holdings import HoldingChange, Stock
        from sqlalchemy import func, and_

        # 查询当日的增减持记录
        query = db.query(HoldingChange).join(Stock).filter(
            HoldingChange.announcement_date == target_date
        )

        all_changes = query.all()
        total_count = len(all_changes)

        # 统计增持和减持数量
        increase_count = sum(1 for change in all_changes if change.direction == 'increase')
        decrease_count = sum(1 for change in all_changes if change.direction == 'decrease')

        # 获取重要变动（按变动金额排序）
        top_changes = []
        for change in sorted(all_changes, key=lambda x: x.change_amount or 0, reverse=True)[:10]:
            top_changes.append({
                'stock_name': change.stock.name,
                'stock_code': change.stock.code,
                'holder_name': change.holder_name,
                'direction': change.direction,
                'change_shares': change.change_shares,
                'change_amount': change.change_amount,
                'change_ratio': change.holding_ratio_after  # 变动比例
            })

        # 生成摘要
        summary = self._generate_data_summary(total_count, increase_count, decrease_count, top_changes)

        return {
            'date': target_date,
            'total_count': total_count,
            'increase_count': increase_count,
            'decrease_count': decrease_count,
            'top_changes': top_changes,
            'summary': summary
        }
    
    def _generate_weekly_increase_summary(
        self,
        total_count: int,
        total_amount: float,
        top_changes: List[Dict[str, Any]],
        start_date: date,
        end_date: date
    ) -> str:
        """生成近7天增持数据摘要"""
        if total_count == 0:
            return "近7天无增持数据"

        summary = f"近7天共有 {total_count} 条增持记录，"
        summary += f"总金额 {total_amount/10000:.2f} 万元，"
        summary += f"平均每笔 {total_amount/total_count/10000:.2f} 万元"

        # 添加重要增持信息
        if top_changes:
            top_change = top_changes[0]
            if top_change['change_amount'] and top_change['change_amount'] > 1000:  # 超过1000万元
                summary += f"。其中 {top_change['stock_name']} 的增持金额最大，达到 {top_change['change_amount']/10000:.2f} 万元"
                if top_change['change_ratio']:
                    summary += f"，变动比例 {top_change['change_ratio']:.2f}%"

        # 统计涉及股票数量
        stock_count = len(set(change['stock_name'] for change in top_changes))
        if stock_count > 0:
            summary += f"，涉及 {stock_count} 只股票"

        return summary

    def _generate_data_summary(
        self,
        total_count: int,
        increase_count: int,
        decrease_count: int,
        top_changes: List[Dict[str, Any]]
    ) -> str:
        """生成数据摘要"""
        if total_count == 0:
            return "今日无增减持数据"

        summary = f"今日共有 {total_count} 条增减持记录，"

        if increase_count > decrease_count:
            summary += f"增持活动较为活跃（{increase_count} 条增持 vs {decrease_count} 条减持）"
        elif decrease_count > increase_count:
            summary += f"减持活动较为活跃（{decrease_count} 条减持 vs {increase_count} 条增持）"
        else:
            summary += f"增持和减持活动相当（各 {increase_count} 条）"

        # 添加重要变动信息
        if top_changes:
            top_change = top_changes[0]
            if top_change['change_amount'] and top_change['change_amount'] > 1000:  # 超过1000万元
                direction_text = "增持" if top_change['direction'] == 'increase' else "减持"
                summary += f"。其中 {top_change['stock_name']} 的 {direction_text} 金额最大，达到 {top_change['change_amount']:.2f} 万元"

        return summary
    
    def _build_weekly_increase_content(self, holding_data: Dict[str, Any]) -> NotificationContentBase:
        """构建近7天增持通知内容"""
        title = f"📈 近7天增持数据报告"

        # 构建简要内容
        content = f"📅 统计时间：{holding_data['date']}\n"
        content += f"📊 增持记录：{holding_data['total_count']} 条\n"
        content += f"💰 总金额：{holding_data['total_amount']/10000:.2f} 万元\n"
        content += f"📈 涉及股票：{holding_data['stats']['stock_count']} 只\n"
        content += f"📋 平均金额：{holding_data['stats']['avg_amount']/10000:.2f} 万元/笔"

        # 添加重要增持信息
        if holding_data['top_changes']:
            content += f"\n\n🔥 重要增持（前5名）：\n"
            for i, change in enumerate(holding_data['top_changes'][:5], 1):
                amount_text = f"{change['change_amount']/10000:.2f}万元" if change['change_amount'] else "未知"
                ratio_text = f"，变动比例{change['change_ratio']:.2f}%" if change['change_ratio'] else ""
                content += f"{i}. {change['stock_name']}({change['stock_code']}) - {change['holder_name']}\n"
                content += f"   增持{change['change_shares']:.2f}万股，金额{amount_text}{ratio_text}\n"

        return NotificationContentBase(
            title=title,
            content=content,
            summary=holding_data['summary'],
            data=holding_data
        )

    def _build_holding_changes_content(self, holding_data: Dict[str, Any]) -> NotificationContentBase:
        """构建增减持通知内容"""
        date_str = holding_data['date'].strftime('%Y年%m月%d日')
        title = f"📊 {date_str} 增减持数据报告"

        # 构建简要内容
        content = f"{date_str} 增减持数据摘要：\n"
        content += f"总记录数：{holding_data['total_count']}\n"
        content += f"增持记录：{holding_data['increase_count']}\n"
        content += f"减持记录：{holding_data['decrease_count']}"

        return NotificationContentBase(
            title=title,
            content=content,
            summary=holding_data['summary'],
            data=holding_data
        )
    
    def get_enabled_services(self) -> List[NotificationType]:
        """获取已启用的通知服务列表"""
        return [service_type for service_type, service in self.services.items() if service.is_enabled]
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取所有服务状态"""
        status = {}
        for service_type, service in self.services.items():
            status[service_type.value] = {
                'enabled': service.is_enabled,
                'service_name': service.service_name,
                'config_valid': bool(service.config)
            }
        
        return {
            'total_services': len(self.services),
            'enabled_services': len([s for s in self.services.values() if s.is_enabled]),
            'services': status
        }
    
    async def test_all_services(self) -> Dict[NotificationType, NotificationResult]:
        """测试所有通知服务"""
        test_content = NotificationContentBase(
            title="🧪 通知服务测试",
            content="这是一条测试消息，用于验证通知服务是否正常工作。",
            summary="通知服务连通性测试"
        )
        
        results = {}
        for notification_type, service in self.services.items():
            if service.is_enabled:
                try:
                    # 使用默认测试配置
                    test_config = self._get_test_config(notification_type)
                    result = await service.send_notification(
                        content=test_content,
                        config=test_config,
                        priority=NotificationPriority.LOW
                    )
                    results[notification_type] = result
                except Exception as e:
                    results[notification_type] = NotificationResult(
                        success=False,
                        message=f"测试失败: {str(e)}",
                        error=e
                    )
            else:
                results[notification_type] = NotificationResult(
                    success=False,
                    message="服务未启用"
                )
        
        return results
    
    def _get_test_config(self, notification_type: NotificationType) -> Dict[str, Any]:
        """获取测试配置"""
        if notification_type == NotificationType.EMAIL:
            return {
                'to_emails': [settings.SMTP_FROM_EMAIL],  # 发送给自己
                'subject': '通知服务测试邮件'
            }
        elif notification_type == NotificationType.WECHAT:
            return {
                'webhook_url': settings.WECHAT_WEBHOOK_URL
            }
        elif notification_type == NotificationType.FEISHU:
            return {
                'webhook_url': settings.FEISHU_WEBHOOK_URL
            }
        else:
            return {}


# 创建全局通知管理器实例
notification_manager = NotificationManager()
