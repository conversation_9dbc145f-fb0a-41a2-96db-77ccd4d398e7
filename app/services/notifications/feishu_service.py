"""
飞书机器人通知服务
"""
import aiohttp
import json
import time
import hmac
import hashlib
import base64
from typing import Dict, Any, List, Optional
from loguru import logger

from app.schemas.notifications import (
    NotificationContentBase,
    NotificationPriority
)
from .base import BaseNotificationService, NotificationResult


class FeishuNotificationService(BaseNotificationService):
    """飞书机器人通知服务"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.default_webhook_url = config.get('webhook_url', '')
        self.secret_key = config.get('secret_key', '')
        
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证飞书配置"""
        webhook_url = config.get('webhook_url', self.default_webhook_url)
        return bool(webhook_url and 'open.feishu.cn' in webhook_url)
    
    async def send_notification(
        self,
        content: NotificationContentBase,
        config: Dict[str, Any],
        priority: NotificationPriority = NotificationPriority.NORMAL
    ) -> NotificationResult:
        """发送飞书通知"""
        try:
            if not self.validate_config(config):
                return NotificationResult(
                    success=False,
                    message="飞书配置验证失败"
                )
            
            webhook_url = config.get('webhook_url', self.default_webhook_url)
            
            # 根据内容类型选择消息格式
            if content.data and 'total_count' in content.data:
                # 增减持数据使用富文本格式
                payload = self._create_rich_text_message(content, config)
            else:
                # 普通消息使用文本格式
                payload = self._create_text_message(content, config)

            # 如果配置了签名密钥，在消息体中添加签名验证字段
            if self.secret_key:
                timestamp = str(int(time.time()))
                sign = self._generate_sign(timestamp, self.secret_key)
                # 在消息体中添加时间戳和签名
                payload['timestamp'] = timestamp
                payload['sign'] = sign

            # 准备请求头
            headers = {'Content-Type': 'application/json'}

            # 发送请求
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook_url,
                    json=payload,
                    headers=headers
                ) as response:
                    result_data = await response.json()
                    
                    if result_data.get('code') == 0:
                        return NotificationResult(
                            success=True,
                            message="飞书消息发送成功",
                            data=result_data
                        )
                    else:
                        return NotificationResult(
                            success=False,
                            message=f"飞书消息发送失败: {result_data.get('msg', '未知错误')}",
                            data=result_data
                        )
                        
        except Exception as e:
            logger.error(f"飞书消息发送异常: {str(e)}")
            return NotificationResult(
                success=False,
                message=f"飞书消息发送异常: {str(e)}",
                error=e
            )
    
    def _create_text_message(
        self,
        content: NotificationContentBase,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建文本消息"""
        # 直接使用content.content，因为它已经包含了标题
        message_content = content.content
        
        if content.summary:
            message_content += f"\n\n💡 {content.summary}"
        
        # 添加@用户
        at_users = config.get('at_users', [])
        at_all = config.get('at_all', False)
        
        if at_all:
            message_content += "\n<at user_id=\"all\">所有人</at>"
        elif at_users:
            for user_id in at_users:
                message_content += f"\n<at user_id=\"{user_id}\"></at>"
        
        payload = {
            "msg_type": "text",
            "content": {
                "text": message_content
            }
        }
        
        return payload
    
    def _create_rich_text_message(
        self,
        content: NotificationContentBase,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建富文本消息（用于增减持数据）"""
        holding_data = content.data
        rich_content = self._build_rich_text_content(holding_data)
        
        payload = {
            "msg_type": "post",
            "content": {
                "post": {
                    "zh_cn": {
                        "title": content.title,
                        "content": rich_content
                    }
                }
            }
        }
        
        return payload
    
    def _build_rich_text_content(self, holding_data: Dict[str, Any]) -> List[List[Dict[str, Any]]]:
        """构建富文本内容"""
        content = []
        
        # 标题行
        date = holding_data.get('date', '今日')
        content.append([
            {"tag": "text", "text": "📊 "},
            {"tag": "text", "text": f"{date} 增减持数据报告", "style": ["bold"]}
        ])
        
        # 空行
        content.append([{"tag": "text", "text": ""}])
        
        # 数据概览
        total = holding_data.get('total_count', 0)
        increase = holding_data.get('increase_count', 0)
        decrease = holding_data.get('decrease_count', 0)
        
        content.append([
            {"tag": "text", "text": "📈 数据概览", "style": ["bold"]}
        ])
        
        content.append([
            {"tag": "text", "text": f"总记录数: "},
            {"tag": "text", "text": str(total), "style": ["bold"], "color": "blue"}
        ])
        
        content.append([
            {"tag": "text", "text": f"增持记录: "},
            {"tag": "text", "text": str(increase), "style": ["bold"], "color": "red"}
        ])
        
        content.append([
            {"tag": "text", "text": f"减持记录: "},
            {"tag": "text", "text": str(decrease), "style": ["bold"], "color": "green"}
        ])
        
        # 数据摘要
        if holding_data.get('summary'):
            content.append([{"tag": "text", "text": ""}])
            content.append([
                {"tag": "text", "text": "💡 数据摘要", "style": ["bold"]}
            ])
            content.append([
                {"tag": "text", "text": holding_data['summary']}
            ])
        
        # 重要变动
        top_changes = holding_data.get('top_changes', [])
        if top_changes:
            content.append([{"tag": "text", "text": ""}])
            content.append([
                {"tag": "text", "text": "🔥 重要变动", "style": ["bold"]}
            ])
            
            for i, change in enumerate(top_changes[:10], 1):
                stock_name = change.get('stock_name', '未知')
                holder_name = change.get('holder_name', '未知')
                direction = change.get('direction', '')
                shares = change.get('change_shares', 0)
                amount = change.get('change_amount', 0)
                ratio_after = change.get('holding_ratio_after', None)

                # 方向标识和颜色
                if direction == 'increase':
                    direction_text = "🔺增持"
                    direction_color = "red"
                else:
                    direction_text = "🔻减持"
                    direction_color = "green"

                # 构建变动信息行
                change_line = [
                    {"tag": "text", "text": f"{i}. "},
                    {"tag": "text", "text": stock_name, "style": ["bold"]},
                    {"tag": "text", "text": f" - {holder_name} "},
                    {"tag": "text", "text": direction_text, "color": direction_color},
                    {"tag": "text", "text": f" {shares:,.0f}股"}
                ]

                # 添加变动比例
                if ratio_after is not None:
                    change_line.append({"tag": "text", "text": f" (比例:{ratio_after:.2f}%)"})

                # 添加金额
                if amount > 0:
                    change_line.append({"tag": "text", "text": f" {amount:,.0f}万元"})

                content.append(change_line)
        
        # 添加时间戳
        from datetime import datetime
        content.append([{"tag": "text", "text": ""}])
        content.append([
            {"tag": "text", "text": f"发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", "style": ["italic"]}
        ])
        
        return content
    
    def _create_card_message(
        self,
        content: NotificationContentBase,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建卡片消息"""
        holding_data = content.data
        
        # 构建卡片元素
        elements = []
        
        # 标题
        elements.append({
            "tag": "div",
            "text": {
                "tag": "lark_md",
                "content": f"**📊 {content.title}**"
            }
        })
        
        # 分割线
        elements.append({"tag": "hr"})
        
        # 数据概览
        if holding_data and 'total_count' in holding_data:
            total = holding_data.get('total_count', 0)
            increase = holding_data.get('increase_count', 0)
            decrease = holding_data.get('decrease_count', 0)
            
            elements.append({
                "tag": "div",
                "fields": [
                    {
                        "is_short": True,
                        "text": {
                            "tag": "lark_md",
                            "content": f"**总记录数**\n{total}"
                        }
                    },
                    {
                        "is_short": True,
                        "text": {
                            "tag": "lark_md",
                            "content": f"**增持记录**\n<font color='red'>{increase}</font>"
                        }
                    },
                    {
                        "is_short": True,
                        "text": {
                            "tag": "lark_md",
                            "content": f"**减持记录**\n<font color='green'>{decrease}</font>"
                        }
                    }
                ]
            })
        
        # 摘要
        if content.summary:
            elements.append({"tag": "hr"})
            elements.append({
                "tag": "div",
                "text": {
                    "tag": "lark_md",
                    "content": f"**💡 摘要**\n{content.summary}"
                }
            })
        
        # 重要变动
        if holding_data and 'top_changes' in holding_data:
            top_changes = holding_data['top_changes'][:5]  # 卡片中最多显示5条
            if top_changes:
                elements.append({"tag": "hr"})
                elements.append({
                    "tag": "div",
                    "text": {
                        "tag": "lark_md",
                        "content": "**🔥 重要变动**"
                    }
                })
                
                for i, change in enumerate(top_changes, 1):
                    stock_name = change.get('stock_name', '未知')
                    holder_name = change.get('holder_name', '未知')
                    direction = "增持" if change.get('direction') == 'increase' else "减持"
                    shares = change.get('change_shares', 0)
                    
                    elements.append({
                        "tag": "div",
                        "text": {
                            "tag": "lark_md",
                            "content": f"{i}. **{stock_name}** - {holder_name} {direction} {shares:.2f}万股"
                        }
                    })
        
        payload = {
            "msg_type": "interactive",
            "card": {
                "elements": elements,
                "header": {
                    "title": {
                        "tag": "plain_text",
                        "content": content.title
                    },
                    "template": "blue"
                }
            }
        }
        
        return payload
    
    def get_message_types(self) -> List[str]:
        """获取支持的消息类型"""
        return ["text", "post", "interactive", "image", "file"]
    
    async def send_image_message(
        self,
        image_key: str,
        webhook_url: str
    ) -> NotificationResult:
        """发送图片消息"""
        try:
            payload = {
                "msg_type": "image",
                "content": {
                    "image_key": image_key
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook_url,
                    json=payload,
                    headers={'Content-Type': 'application/json'}
                ) as response:
                    result_data = await response.json()
                    
                    if result_data.get('code') == 0:
                        return NotificationResult(
                            success=True,
                            message="飞书图片发送成功",
                            data=result_data
                        )
                    else:
                        return NotificationResult(
                            success=False,
                            message=f"飞书图片发送失败: {result_data.get('msg', '未知错误')}",
                            data=result_data
                        )
                        
        except Exception as e:
            logger.error(f"飞书图片发送异常: {str(e)}")
            return NotificationResult(
                success=False,
                message=f"飞书图片发送异常: {str(e)}",
                error=e
            )

    def _generate_sign(self, timestamp: str, secret: str) -> str:
        """
        生成飞书签名

        根据飞书官方文档：
        1. 把timestamp + "\n" + 密钥当做签名的字符串
        2. 使用HmacSHA256算法计算签名，再进行Base64 encode，得到最终的签名

        Args:
            timestamp: 时间戳
            secret: 签名密钥

        Returns:
            str: 签名字符串
        """
        # 拼接timestamp和secret
        string_to_sign = '{}\n{}'.format(timestamp, secret)

        # 使用拼接字符串作为HMAC的key（这是飞书的特殊要求）
        hmac_code = hmac.new(
            string_to_sign.encode("utf-8"),
            digestmod=hashlib.sha256
        ).digest()

        # 对结果进行base64编码
        sign = base64.b64encode(hmac_code).decode('utf-8')

        return sign

    def _verify_sign(self, timestamp: str, nonce: str, signature: str, secret: str) -> bool:
        """
        验证飞书签名

        Args:
            timestamp: 时间戳
            nonce: 随机字符串
            signature: 签名
            secret: 签名密钥

        Returns:
            bool: 验证结果
        """
        try:
            # 检查时间戳是否在有效期内（5分钟）
            current_time = int(time.time())
            request_time = int(timestamp)
            if abs(current_time - request_time) > 300:  # 5分钟
                logger.warning(f"飞书签名验证失败: 时间戳超出有效期")
                return False

            # 生成期望的签名
            expected_sign = self._generate_sign(timestamp, secret)

            # 比较签名
            return hmac.compare_digest(signature, expected_sign)

        except Exception as e:
            logger.error(f"飞书签名验证异常: {e}")
            return False
