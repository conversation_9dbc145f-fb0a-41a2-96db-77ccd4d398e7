"""
通知服务基类
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from datetime import datetime
import asyncio
from loguru import logger

from app.schemas.notifications import (
    NotificationContentBase,
    NotificationStatus,
    NotificationPriority
)


class NotificationResult:
    """通知发送结果"""
    
    def __init__(
        self,
        success: bool,
        message: str,
        data: Optional[Dict[str, Any]] = None,
        error: Optional[Exception] = None
    ):
        self.success = success
        self.message = message
        self.data = data or {}
        self.error = error
        self.sent_time = datetime.now()
    
    def __str__(self):
        return f"NotificationResult(success={self.success}, message='{self.message}')"


class BaseNotificationService(ABC):
    """通知服务基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化通知服务
        
        Args:
            config: 通知服务配置
        """
        self.config = config
        self.service_name = self.__class__.__name__
        self.is_enabled = config.get('enabled', False)
        
    @abstractmethod
    async def send_notification(
        self,
        content: NotificationContentBase,
        config: Dict[str, Any],
        priority: NotificationPriority = NotificationPriority.NORMAL
    ) -> NotificationResult:
        """
        发送通知
        
        Args:
            content: 通知内容
            config: 发送配置
            priority: 优先级
            
        Returns:
            NotificationResult: 发送结果
        """
        pass
    
    @abstractmethod
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        验证配置是否有效
        
        Args:
            config: 配置字典
            
        Returns:
            bool: 配置是否有效
        """
        pass
    
    async def send_with_retry(
        self,
        content: NotificationContentBase,
        config: Dict[str, Any],
        priority: NotificationPriority = NotificationPriority.NORMAL,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ) -> NotificationResult:
        """
        带重试的发送通知
        
        Args:
            content: 通知内容
            config: 发送配置
            priority: 优先级
            max_retries: 最大重试次数
            retry_delay: 重试延迟(秒)
            
        Returns:
            NotificationResult: 发送结果
        """
        last_result = None
        
        for attempt in range(max_retries + 1):
            try:
                logger.info(f"{self.service_name} 发送通知 (尝试 {attempt + 1}/{max_retries + 1})")
                
                result = await self.send_notification(content, config, priority)
                
                if result.success:
                    if attempt > 0:
                        logger.info(f"{self.service_name} 重试成功，尝试次数: {attempt + 1}")
                    return result
                else:
                    last_result = result
                    logger.warning(f"{self.service_name} 发送失败: {result.message}")
                    
            except Exception as e:
                logger.error(f"{self.service_name} 发送异常: {str(e)}")
                last_result = NotificationResult(
                    success=False,
                    message=f"发送异常: {str(e)}",
                    error=e
                )
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < max_retries:
                await asyncio.sleep(retry_delay * (2 ** attempt))  # 指数退避
        
        # 所有重试都失败
        logger.error(f"{self.service_name} 发送失败，已达到最大重试次数")
        return last_result or NotificationResult(
            success=False,
            message="发送失败，已达到最大重试次数"
        )
    
    def get_service_info(self) -> Dict[str, Any]:
        """
        获取服务信息
        
        Returns:
            Dict[str, Any]: 服务信息
        """
        return {
            'service_name': self.service_name,
            'is_enabled': self.is_enabled,
            'config_keys': list(self.config.keys())
        }
    
    def format_holding_changes_content(
        self,
        holding_data: Dict[str, Any],
        template_type: str = "simple"
    ) -> str:
        """
        格式化增减持数据内容
        
        Args:
            holding_data: 增减持数据
            template_type: 模板类型 (simple, detailed, markdown)
            
        Returns:
            str: 格式化后的内容
        """
        if template_type == "markdown":
            return self._format_markdown_content(holding_data)
        elif template_type == "detailed":
            return self._format_detailed_content(holding_data)
        else:
            return self._format_simple_content(holding_data)
    
    def _format_simple_content(self, holding_data: Dict[str, Any]) -> str:
        """格式化简单内容"""
        date = holding_data.get('date', '今日')
        total = holding_data.get('total_count', 0)
        increase = holding_data.get('increase_count', 0)
        decrease = holding_data.get('decrease_count', 0)
        
        content = f"📊 {date} 增减持数据摘要\n\n"
        content += f"📈 总记录数: {total}\n"
        content += f"🔺 增持记录: {increase}\n"
        content += f"🔻 减持记录: {decrease}\n"
        
        if holding_data.get('summary'):
            content += f"\n💡 {holding_data['summary']}"
            
        return content
    
    def _format_detailed_content(self, holding_data: Dict[str, Any]) -> str:
        """格式化详细内容"""
        content = self._format_simple_content(holding_data)
        
        top_changes = holding_data.get('top_changes', [])
        if top_changes:
            content += "\n\n🔥 重要变动:\n"
            for i, change in enumerate(top_changes[:5], 1):
                stock_name = change.get('stock_name', '未知')
                holder_name = change.get('holder_name', '未知')
                direction = "增持" if change.get('direction') == 'increase' else "减持"
                shares = change.get('change_shares', 0)
                content += f"{i}. {stock_name} - {holder_name} {direction} {shares:.2f}万股\n"
        
        return content
    
    def _format_markdown_content(self, holding_data: Dict[str, Any]) -> str:
        """格式化Markdown内容"""
        date = holding_data.get('date', '今日')
        total = holding_data.get('total_count', 0)
        increase = holding_data.get('increase_count', 0)
        decrease = holding_data.get('decrease_count', 0)
        
        content = f"# 📊 {date} 增减持数据报告\n\n"
        content += f"## 📈 数据概览\n\n"
        content += f"- **总记录数**: {total}\n"
        content += f"- **增持记录**: {increase}\n"
        content += f"- **减持记录**: {decrease}\n\n"
        
        if holding_data.get('summary'):
            content += f"## 💡 数据摘要\n\n{holding_data['summary']}\n\n"
        
        top_changes = holding_data.get('top_changes', [])
        if top_changes:
            content += "## 🔥 重要变动\n\n"
            content += "| 序号 | 股票名称 | 变动人 | 方向 | 变动股数(万股) |\n"
            content += "|------|----------|--------|------|----------------|\n"
            for i, change in enumerate(top_changes[:10], 1):
                stock_name = change.get('stock_name', '未知')
                holder_name = change.get('holder_name', '未知')
                direction = "🔺增持" if change.get('direction') == 'increase' else "🔻减持"
                shares = change.get('change_shares', 0)
                content += f"| {i} | {stock_name} | {holder_name} | {direction} | {shares:.2f} |\n"
        
        return content
