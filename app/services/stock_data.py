"""
股价数据服务
"""
import akshare as ak
import pandas as pd
from datetime import datetime, date, timedelta
from typing import Optional, Dict, List
from loguru import logger


class StockDataService:
    """股价数据服务类"""
    
    def __init__(self):
        self.cache = {}  # 简单的内存缓存
    
    def get_stock_price_data(
        self, 
        stock_code: str, 
        start_date: date, 
        end_date: date,
        adjust: str = "qfq"
    ) -> Optional[pd.DataFrame]:
        """
        获取股票价格数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            adjust: 复权类型 ("qfq": 前复权, "hfq": 后复权, "": 不复权)
            
        Returns:
            股价数据DataFrame
        """
        try:
            # 构建缓存键
            cache_key = f"{stock_code}_{start_date}_{end_date}_{adjust}"
            
            # 检查缓存
            if cache_key in self.cache:
                logger.debug(f"从缓存获取股价数据: {stock_code}")
                return self.cache[cache_key]
            
            # 从AkShare获取数据
            logger.info(f"获取股价数据: {stock_code} ({start_date} ~ {end_date})")
            
            # 直接使用股票代码，不添加市场前缀
            # AkShare 的 stock_zh_a_hist 函数可以直接使用6位数字代码
            symbol = stock_code
            
            # 获取股票历史数据
            df = ak.stock_zh_a_hist(
                symbol=symbol,
                period="daily",
                start_date=start_date.strftime("%Y%m%d"),
                end_date=end_date.strftime("%Y%m%d"),
                adjust=adjust
            )
            
            if df is not None and not df.empty:
                # 标准化列名
                df = self._standardize_columns(df)
                
                # 缓存数据
                self.cache[cache_key] = df
                
                logger.info(f"成功获取 {len(df)} 条股价数据")
                return df
            else:
                logger.warning(f"未获取到股价数据: {stock_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取股价数据失败 {stock_code}: {e}")
            return None
    
    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化DataFrame列名"""
        column_mapping = {
            '日期': 'date',
            '开盘': 'open',
            '收盘': 'close', 
            '最高': 'high',
            '最低': 'low',
            '成交量': 'volume',
            '成交额': 'amount',
            '振幅': 'amplitude',
            '涨跌幅': 'pct_change',
            '涨跌额': 'change',
            '换手率': 'turnover'
        }
        
        df = df.rename(columns=column_mapping)
        
        # 确保日期列为datetime类型
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
            df = df.set_index('date')
        
        return df
    
    def calculate_price_impact(
        self, 
        stock_code: str, 
        event_date: date,
        before_days: int = 5,
        after_days: int = 20
    ) -> Dict:
        """
        计算增减持事件对股价的影响
        
        Args:
            stock_code: 股票代码
            event_date: 事件日期
            before_days: 事件前天数
            after_days: 事件后天数
            
        Returns:
            影响分析结果
        """
        try:
            # 计算数据获取范围
            start_date = event_date - timedelta(days=before_days + 10)  # 多获取一些数据
            end_date = event_date + timedelta(days=after_days + 10)
            
            # 获取股价数据
            df = self.get_stock_price_data(stock_code, start_date, end_date)
            if df is None or df.empty:
                return {}
            
            # 找到事件日期对应的交易日
            event_idx = self._find_nearest_trading_day(df, event_date)
            if event_idx is None:
                return {}
            
            # 计算各时间段的收益率
            result = {
                'stock_code': stock_code,
                'event_date': event_date,
                'event_price': float(df.iloc[event_idx]['close']),
            }
            
            # 计算事件前后的价格变化
            for days in [1, 3, 5, 10, 20]:
                # 事件后N日收益率
                after_idx = event_idx + days
                if after_idx < len(df):
                    after_price = float(df.iloc[after_idx]['close'])
                    after_return = (after_price - result['event_price']) / result['event_price'] * 100
                    result[f'return_after_{days}d'] = round(after_return, 2)
                
                # 事件前N日收益率（用于对比）
                before_idx = event_idx - days
                if before_idx >= 0:
                    before_price = float(df.iloc[before_idx]['close'])
                    before_return = (result['event_price'] - before_price) / before_price * 100
                    result[f'return_before_{days}d'] = round(before_return, 2)
            
            # 计算最大回撤和最大涨幅
            after_prices = df.iloc[event_idx:event_idx+after_days+1]['close']
            if len(after_prices) > 1:
                max_price = after_prices.max()
                min_price = after_prices.min()
                
                result['max_gain'] = round((max_price - result['event_price']) / result['event_price'] * 100, 2)
                result['max_drawdown'] = round((min_price - result['event_price']) / result['event_price'] * 100, 2)
            
            return result
            
        except Exception as e:
            logger.error(f"计算价格影响失败 {stock_code}: {e}")
            return {}
    
    def _find_nearest_trading_day(self, df: pd.DataFrame, target_date: date) -> Optional[int]:
        """找到最接近目标日期的交易日索引"""
        try:
            target_datetime = pd.to_datetime(target_date)
            
            # 如果目标日期就是交易日
            if target_datetime in df.index:
                return df.index.get_loc(target_datetime)
            
            # 找到最接近的交易日
            diff = abs(df.index - target_datetime)
            nearest_idx = diff.argmin()
            
            # 确保不超过5天的差距
            if diff.iloc[nearest_idx].days <= 5:
                return nearest_idx
            
            return None
            
        except Exception as e:
            logger.error(f"查找交易日失败: {e}")
            return None
    
    def get_market_index_data(
        self, 
        index_code: str = "000001",  # 上证指数
        start_date: date = None,
        end_date: date = None
    ) -> Optional[pd.DataFrame]:
        """
        获取市场指数数据
        
        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            指数数据DataFrame
        """
        try:
            if start_date is None:
                start_date = date.today() - timedelta(days=365)
            if end_date is None:
                end_date = date.today()
            
            # 获取指数数据
            df = ak.stock_zh_index_daily(
                symbol=f"sh{index_code}"
            )
            
            if df is not None and not df.empty:
                # 过滤日期范围
                df['date'] = pd.to_datetime(df['date'])
                df = df[(df['date'] >= pd.to_datetime(start_date)) & 
                       (df['date'] <= pd.to_datetime(end_date))]
                df = df.set_index('date')
                
                return df
            
            return None
            
        except Exception as e:
            logger.error(f"获取指数数据失败 {index_code}: {e}")
            return None
    
    def calculate_relative_performance(
        self,
        stock_code: str,
        event_date: date,
        days: int = 20,
        benchmark: str = "000001"
    ) -> Dict:
        """
        计算相对市场的表现
        
        Args:
            stock_code: 股票代码
            event_date: 事件日期
            days: 计算天数
            benchmark: 基准指数代码
            
        Returns:
            相对表现数据
        """
        try:
            start_date = event_date - timedelta(days=10)
            end_date = event_date + timedelta(days=days+10)
            
            # 获取股票和指数数据
            stock_df = self.get_stock_price_data(stock_code, start_date, end_date)
            index_df = self.get_market_index_data(benchmark, start_date, end_date)
            
            if stock_df is None or index_df is None:
                return {}
            
            # 找到事件日期
            event_idx_stock = self._find_nearest_trading_day(stock_df, event_date)
            event_idx_index = self._find_nearest_trading_day(index_df, event_date)
            
            if event_idx_stock is None or event_idx_index is None:
                return {}
            
            result = {}
            
            # 计算相对表现
            for d in [1, 3, 5, 10, 20]:
                if (event_idx_stock + d < len(stock_df) and 
                    event_idx_index + d < len(index_df)):
                    
                    # 股票收益率
                    stock_return = (
                        stock_df.iloc[event_idx_stock + d]['close'] - 
                        stock_df.iloc[event_idx_stock]['close']
                    ) / stock_df.iloc[event_idx_stock]['close'] * 100
                    
                    # 指数收益率
                    index_return = (
                        index_df.iloc[event_idx_index + d]['close'] - 
                        index_df.iloc[event_idx_index]['close']
                    ) / index_df.iloc[event_idx_index]['close'] * 100
                    
                    # 相对表现
                    relative_return = stock_return - index_return
                    result[f'relative_return_{d}d'] = round(relative_return, 2)
            
            return result
            
        except Exception as e:
            logger.error(f"计算相对表现失败: {e}")
            return {}
