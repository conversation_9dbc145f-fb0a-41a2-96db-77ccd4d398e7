"""
增减持相关数据模型
"""
from datetime import datetime, date
from sqlalchemy import Column, Integer, String, Float, Date, DateTime, Text, ForeignKey, Enum, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid
import enum

from .base import BaseModel


class ChangeDirection(enum.Enum):
    """增减持方向枚举"""
    INCREASE = "increase"  # 增持
    DECREASE = "decrease"  # 减持


class HolderType(enum.Enum):
    """持有人类型枚举"""
    EXECUTIVE = "executive"  # 董监高
    INSTITUTION = "institution"  # 机构
    MAJOR_SHAREHOLDER = "major_shareholder"  # 大股东
    OTHER = "other"  # 其他


class Stock(BaseModel):
    """股票基础信息表"""
    __tablename__ = "stocks"
    
    # 股票代码（如：000001）
    code = Column(String(10), unique=True, index=True, nullable=False)
    # 股票名称
    name = Column(String(100), nullable=False)
    # 交易所（SZ/SH）
    exchange = Column(String(10), nullable=False)
    # 行业
    industry = Column(String(100))
    # 是否活跃
    is_active = Column(Boolean, default=True)
    
    # 关联的增减持记录
    holding_changes = relationship("HoldingChange", back_populates="stock")


class HoldingChange(BaseModel):
    """增减持变动记录表"""
    __tablename__ = "holding_changes"
    
    # 关联股票ID
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False)
    
    # 公告日期
    announcement_date = Column(Date, nullable=False, index=True)
    # 变动日期
    change_date = Column(Date, nullable=False, index=True)
    
    # 变动人姓名/机构名称
    holder_name = Column(String(200), nullable=False)
    # 持有人类型
    holder_type = Column(String(50), nullable=False, index=True)

    # 增减持方向
    direction = Column(String(20), nullable=False, index=True)
    
    # 变动股份数量（万股）
    change_shares = Column(Float, nullable=False)
    # 变动后持股数量（万股）
    total_shares_after = Column(Float)
    # 变动后持股比例（%）
    holding_ratio_after = Column(Float)
    
    # 变动价格区间
    price_min = Column(Float)  # 最低价格
    price_max = Column(Float)  # 最高价格
    price_avg = Column(Float)  # 平均价格
    
    # 变动金额（万元）
    change_amount = Column(Float)
    
    # 变动原因
    change_reason = Column(Text)
    
    # 数据来源URL
    source_url = Column(String(500))
    
    # 影响力评分（后续计算）
    impact_score = Column(Float, default=0.0)
    
    # 关联的股票信息
    stock = relationship("Stock", back_populates="holding_changes")
    
    def __repr__(self):
        return f"<HoldingChange({self.stock.code}-{self.holder_name}-{self.direction.value})>"
