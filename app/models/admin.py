"""
管理员用户模型
"""
from sqlalchemy import Column, String, Boolean, DateTime, Text
from sqlalchemy.sql import func
from app.models.base import BaseModel


class Admin(BaseModel):
    """管理员用户表"""
    __tablename__ = "admins"
    
    # 用户名（唯一）
    username = Column(String(50), unique=True, index=True, nullable=False)
    # 邮箱（唯一）
    email = Column(String(100), unique=True, index=True, nullable=False)
    # 密码哈希
    password_hash = Column(String(255), nullable=False)
    # 显示名称
    display_name = Column(String(100), nullable=False)
    # 是否激活
    is_active = Column(Boolean, default=True, nullable=False)
    # 是否超级管理员
    is_superuser = Column(Boolean, default=False, nullable=False)
    # 最后登录时间
    last_login = Column(DateTime(timezone=True), nullable=True)
    # 备注
    notes = Column(Text, nullable=True)
    
    def __repr__(self):
        return f"<Admin(username='{self.username}', email='{self.email}')>"


class SystemConfig(BaseModel):
    """系统配置表"""
    __tablename__ = "system_configs"
    
    # 配置键（唯一）
    config_key = Column(String(100), unique=True, index=True, nullable=False)
    # 配置值
    config_value = Column(Text, nullable=False)
    # 配置描述
    description = Column(String(255), nullable=True)
    # 配置类型（string, int, float, bool, json）
    config_type = Column(String(20), default="string", nullable=False)
    # 是否可编辑
    is_editable = Column(Boolean, default=True, nullable=False)
    # 配置分组
    config_group = Column(String(50), default="general", nullable=False)
    
    def __repr__(self):
        return f"<SystemConfig(key='{self.config_key}', value='{self.config_value}')>"
