"""
通知配置数据模型
"""
from sqlalchemy import Column, Integer, String, Boolean, Text, DateTime, Enum as SQLEnum
from sqlalchemy.sql import func
from enum import Enum

from app.models.base import BaseModel


class NotificationServiceType(str, Enum):
    """通知服务类型"""
    EMAIL = "email"
    WECHAT = "wechat"
    FEISHU = "feishu"


class NotificationConfig(BaseModel):
    """通知配置表 - 简化版，只管理启用状态和基本设置"""
    __tablename__ = "notification_configs"

    # 移除继承的id字段，使用service_type作为主键
    id = None

    # 服务类型
    service_type = Column(
        SQLEnum(NotificationServiceType),
        nullable=False,
        primary_key=True,
        comment="通知服务类型"
    )

    # 是否启用
    is_enabled = Column(Boolean, default=False, nullable=False, comment="是否启用")

    # 配置描述
    description = Column(String(500), comment="配置描述")

    # 通知时间配置
    notification_time = Column(String(10), default="09:00", comment="每日通知时间")

    # 邮件特定配置
    email_recipients = Column(Text, comment="邮件收件人列表(JSON)")

    # 微信特定配置
    wechat_mentioned_users = Column(Text, comment="微信@用户列表(JSON)")
    wechat_mentioned_mobiles = Column(Text, comment="微信@手机号列表(JSON)")

    # 飞书特定配置
    feishu_at_all = Column(Boolean, default=False, comment="飞书是否@所有人")
    feishu_at_users = Column(Text, comment="飞书@用户ID列表(JSON)")

    # 创建和更新时间
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<NotificationConfig(service_type='{self.service_type}', is_enabled={self.is_enabled})>"

    def to_dict(self):
        """转换为字典"""
        import json

        data = {
            'service_type': self.service_type.value if self.service_type else None,
            'is_enabled': self.is_enabled,
            'description': self.description,
            'notification_time': self.notification_time,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

        # 根据服务类型添加相应字段
        if self.service_type == NotificationServiceType.EMAIL:
            try:
                data['email_recipients'] = json.loads(self.email_recipients) if self.email_recipients else []
            except (json.JSONDecodeError, TypeError):
                data['email_recipients'] = []

        elif self.service_type == NotificationServiceType.WECHAT:
            try:
                data['wechat_mentioned_users'] = json.loads(self.wechat_mentioned_users) if self.wechat_mentioned_users else []
                data['wechat_mentioned_mobiles'] = json.loads(self.wechat_mentioned_mobiles) if self.wechat_mentioned_mobiles else []
            except (json.JSONDecodeError, TypeError):
                data['wechat_mentioned_users'] = []
                data['wechat_mentioned_mobiles'] = []

        elif self.service_type == NotificationServiceType.FEISHU:
            data['feishu_at_all'] = self.feishu_at_all
            try:
                data['feishu_at_users'] = json.loads(self.feishu_at_users) if self.feishu_at_users else []
            except (json.JSONDecodeError, TypeError):
                data['feishu_at_users'] = []

        return data
    
    def get_notification_config(self):
        """获取通知配置，结合环境变量中的敏感信息"""
        from app.core.config import settings
        import json

        base_config = {
            'enabled': self.is_enabled,
            'notification_time': self.notification_time
        }

        if self.service_type == NotificationServiceType.EMAIL:
            base_config.update({
                'smtp_host': settings.SMTP_HOST,
                'smtp_port': settings.SMTP_PORT,
                'smtp_username': settings.SMTP_USERNAME,
                'smtp_password': settings.SMTP_PASSWORD,
                'from_email': settings.SMTP_FROM_EMAIL,
                'from_name': settings.SMTP_FROM_NAME
            })

            # 添加收件人列表
            try:
                if self.email_recipients:
                    base_config['recipients'] = json.loads(self.email_recipients)
            except (json.JSONDecodeError, TypeError):
                base_config['recipients'] = []

        elif self.service_type == NotificationServiceType.WECHAT:
            base_config.update({
                'webhook_url': settings.WECHAT_WEBHOOK_URL
            })

            # 添加@用户配置
            try:
                if self.wechat_mentioned_users:
                    base_config['mentioned_list'] = json.loads(self.wechat_mentioned_users)
                if self.wechat_mentioned_mobiles:
                    base_config['mentioned_mobile_list'] = json.loads(self.wechat_mentioned_mobiles)
            except (json.JSONDecodeError, TypeError):
                pass

        elif self.service_type == NotificationServiceType.FEISHU:
            base_config.update({
                'webhook_url': settings.FEISHU_WEBHOOK_URL,
                'secret_key': settings.FEISHU_SECRET_KEY,
                'at_all': self.feishu_at_all
            })

            # 添加@用户配置
            try:
                if self.feishu_at_users:
                    base_config['at_users'] = json.loads(self.feishu_at_users)
            except (json.JSONDecodeError, TypeError):
                pass

        return base_config
