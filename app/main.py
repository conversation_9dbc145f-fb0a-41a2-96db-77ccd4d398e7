"""
FastAPI主应用
"""
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from loguru import logger
import time

from app.core.config import settings
from app.api.v1.api import api_router


# 创建FastAPI应用
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.VERSION,
    description="专业的股票增减持数据分析平台",
    openapi_url=f"{settings.API_PREFIX}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
    redirect_slashes=False  # 禁用自动重定向，避免307状态码
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    
    # 记录请求信息
    logger.info(f"请求开始: {request.method} {request.url}")
    
    # 处理请求
    response = await call_next(request)
    
    # 计算处理时间
    process_time = time.time() - start_time
    
    # 记录响应信息
    logger.info(
        f"请求完成: {request.method} {request.url} - "
        f"状态码: {response.status_code} - "
        f"耗时: {process_time:.3f}s"
    )
    
    # 添加处理时间到响应头
    response.headers["X-Process-Time"] = str(process_time)
    
    return response


# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"全局异常: {request.method} {request.url} - {str(exc)}")
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "message": "服务暂时不可用，请稍后重试",
            "detail": str(exc) if settings.DEBUG else None
        }
    )


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "app_name": settings.APP_NAME,
        "version": settings.VERSION,
        "timestamp": time.time()
    }


# 根路径
@app.get("/")
async def root():
    """根路径信息"""
    return {
        "message": f"欢迎使用{settings.APP_NAME}",
        "version": settings.VERSION,
        "docs_url": "/docs",
        "api_prefix": settings.API_PREFIX
    }


# 注册API路由
app.include_router(api_router, prefix=settings.API_PREFIX)


# 应用启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时执行"""
    from app.utils.logger import setup_logger
    from app.utils.scheduler import scheduler_manager

    # 初始化日志
    setup_logger()

    logger.info(f"{settings.APP_NAME} v{settings.VERSION} 启动成功")
    logger.info(f"API文档地址: http://{settings.API_HOST}:{settings.API_PORT}/docs")

    # 启动定时任务调度器
    try:
        scheduler_manager.start()
        logger.info("定时任务调度器启动成功")
    except Exception as e:
        logger.error(f"启动定时任务调度器失败: {e}")


# 应用关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时执行"""
    from app.utils.scheduler import scheduler_manager

    logger.info(f"{settings.APP_NAME} 正在关闭...")

    # 停止定时任务调度器
    try:
        scheduler_manager.stop()
        logger.info("定时任务调度器已停止")
    except Exception as e:
        logger.error(f"停止定时任务调度器失败: {e}")


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
