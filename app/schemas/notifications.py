"""
通知相关的Pydantic模式
"""
from datetime import datetime
from datetime import date as Date
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, EmailStr
from enum import Enum


class NotificationType(str, Enum):
    """通知类型"""
    EMAIL = "email"
    WECHAT = "wechat"
    FEISHU = "feishu"


class NotificationStatus(str, Enum):
    """通知状态"""
    PENDING = "pending"
    SENDING = "sending"
    SUCCESS = "success"
    FAILED = "failed"
    RETRY = "retry"


class NotificationPriority(str, Enum):
    """通知优先级"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


# 通知内容基础模式
class NotificationContentBase(BaseModel):
    """通知内容基础模式"""
    title: str = Field(..., description="通知标题")
    content: str = Field(..., description="通知内容")
    summary: Optional[str] = Field(None, description="通知摘要")
    data: Optional[Dict[str, Any]] = Field(None, description="附加数据")


# 邮件通知模式
class EmailNotificationConfig(BaseModel):
    """邮件通知配置"""
    to_emails: List[EmailStr] = Field(..., description="收件人邮箱列表")
    cc_emails: Optional[List[EmailStr]] = Field(None, description="抄送邮箱列表")
    bcc_emails: Optional[List[EmailStr]] = Field(None, description="密送邮箱列表")
    subject: str = Field(..., description="邮件主题")
    html_content: Optional[str] = Field(None, description="HTML格式内容")
    attachments: Optional[List[str]] = Field(None, description="附件路径列表")


class EmailNotificationRequest(NotificationContentBase):
    """邮件通知请求"""
    config: EmailNotificationConfig


# 微信通知模式
class WechatNotificationConfig(BaseModel):
    """微信通知配置"""
    webhook_url: str = Field(..., description="微信机器人Webhook URL")
    mentioned_list: Optional[List[str]] = Field(None, description="@用户列表")
    mentioned_mobile_list: Optional[List[str]] = Field(None, description="@手机号列表")


class WechatNotificationRequest(NotificationContentBase):
    """微信通知请求"""
    config: WechatNotificationConfig


# 飞书通知模式
class FeishuNotificationConfig(BaseModel):
    """飞书通知配置"""
    webhook_url: str = Field(..., description="飞书机器人Webhook URL")
    at_all: bool = Field(False, description="是否@所有人")
    at_users: Optional[List[str]] = Field(None, description="@用户ID列表")


class FeishuNotificationRequest(NotificationContentBase):
    """飞书通知请求"""
    config: FeishuNotificationConfig


# 通用通知请求
class NotificationRequest(BaseModel):
    """通用通知请求"""
    notification_type: NotificationType = Field(..., description="通知类型")
    priority: NotificationPriority = Field(NotificationPriority.NORMAL, description="优先级")
    content: NotificationContentBase = Field(..., description="通知内容")
    config: Dict[str, Any] = Field(..., description="通知配置")
    scheduled_time: Optional[datetime] = Field(None, description="定时发送时间")


# 通知记录模式
class NotificationRecord(BaseModel):
    """通知记录"""
    id: int
    notification_type: NotificationType
    priority: NotificationPriority
    status: NotificationStatus
    title: str
    content: str
    config: Dict[str, Any]
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    scheduled_time: Optional[datetime] = None
    sent_time: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 增减持通知数据模式
class HoldingChangeNotificationData(BaseModel):
    """增减持通知数据"""
    date: Date = Field(..., description="统计日期")
    total_count: int = Field(..., description="总记录数")
    increase_count: int = Field(..., description="增持记录数")
    decrease_count: int = Field(..., description="减持记录数")
    top_changes: List[Dict[str, Any]] = Field(..., description="重要变动列表")
    summary: str = Field(..., description="数据摘要")


# 批量通知请求
class BatchNotificationRequest(BaseModel):
    """批量通知请求"""
    notification_types: List[NotificationType] = Field(..., description="通知类型列表")
    content: NotificationContentBase = Field(..., description="通知内容")
    priority: NotificationPriority = Field(NotificationPriority.NORMAL, description="优先级")
    scheduled_time: Optional[datetime] = Field(None, description="定时发送时间")


# 通知统计模式
class NotificationStats(BaseModel):
    """通知统计"""
    total_sent: int = Field(..., description="总发送数")
    success_count: int = Field(..., description="成功数")
    failed_count: int = Field(..., description="失败数")
    pending_count: int = Field(..., description="待发送数")
    success_rate: float = Field(..., description="成功率")
    last_sent_time: Optional[datetime] = Field(None, description="最后发送时间")


# API响应模式
class NotificationResponse(BaseModel):
    """通知响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    notification_id: Optional[int] = Field(None, description="通知记录ID")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
