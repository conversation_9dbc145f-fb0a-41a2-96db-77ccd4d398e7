"""
管理员相关的Pydantic模式
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr, Field


class AdminBase(BaseModel):
    """管理员基础模式"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    display_name: str = Field(..., min_length=1, max_length=100, description="显示名称")
    is_active: bool = Field(True, description="是否激活")
    is_superuser: bool = Field(False, description="是否超级管理员")
    notes: Optional[str] = Field(None, description="备注")


class AdminCreate(AdminBase):
    """创建管理员模式"""
    password: str = Field(..., min_length=6, max_length=50, description="密码")


class AdminUpdate(BaseModel):
    """更新管理员模式"""
    email: Optional[EmailStr] = Field(None, description="邮箱地址")
    display_name: Optional[str] = Field(None, min_length=1, max_length=100, description="显示名称")
    is_active: Optional[bool] = Field(None, description="是否激活")
    is_superuser: Optional[bool] = Field(None, description="是否超级管理员")
    notes: Optional[str] = Field(None, description="备注")


class AdminPasswordUpdate(BaseModel):
    """更新密码模式"""
    old_password: str = Field(..., description="旧密码")
    new_password: str = Field(..., min_length=6, max_length=50, description="新密码")


class Admin(AdminBase):
    """管理员响应模式"""
    id: int
    last_login: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AdminLogin(BaseModel):
    """管理员登录模式"""
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


class AdminToken(BaseModel):
    """管理员令牌响应模式"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field("bearer", description="令牌类型")
    admin: Admin = Field(..., description="管理员信息")


class SystemConfigBase(BaseModel):
    """系统配置基础模式"""
    config_key: str = Field(..., max_length=100, description="配置键")
    config_value: str = Field(..., description="配置值")
    description: Optional[str] = Field(None, max_length=255, description="配置描述")
    config_type: str = Field("string", description="配置类型")
    is_editable: bool = Field(True, description="是否可编辑")
    config_group: str = Field("general", max_length=50, description="配置分组")


class SystemConfigCreate(SystemConfigBase):
    """创建系统配置模式"""
    pass


class SystemConfigUpdate(BaseModel):
    """更新系统配置模式"""
    config_value: Optional[str] = Field(None, description="配置值")
    description: Optional[str] = Field(None, max_length=255, description="配置描述")
    is_editable: Optional[bool] = Field(None, description="是否可编辑")


class SystemConfig(SystemConfigBase):
    """系统配置响应模式"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
