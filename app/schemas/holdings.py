"""
增减持相关的Pydantic模式
"""
from datetime import date, datetime
from typing import Optional, List
from pydantic import BaseModel, Field
from enum import Enum


class ChangeDirection(str, Enum):
    """增减持方向"""
    INCREASE = "increase"
    DECREASE = "decrease"


class HolderType(str, Enum):
    """持有人类型"""
    EXECUTIVE = "executive"
    INSTITUTION = "institution"
    MAJOR_SHAREHOLDER = "major_shareholder"
    OTHER = "other"


# Stock相关模式
class StockBase(BaseModel):
    """股票基础模式"""
    code: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")
    exchange: str = Field(..., description="交易所")
    industry: Optional[str] = Field(None, description="行业")
    is_active: bool = Field(True, description="是否活跃")


class StockCreate(StockBase):
    """创建股票模式"""
    pass


class StockUpdate(BaseModel):
    """更新股票模式"""
    name: Optional[str] = None
    industry: Optional[str] = None
    is_active: Optional[bool] = None


class Stock(StockBase):
    """股票响应模式"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# HoldingChange相关模式
class HoldingChangeBase(BaseModel):
    """增减持基础模式"""
    announcement_date: date = Field(..., description="公告日期")
    change_date: date = Field(..., description="变动日期")
    holder_name: str = Field(..., description="变动人姓名")
    holder_type: HolderType = Field(..., description="持有人类型")
    direction: ChangeDirection = Field(..., description="增减持方向")
    change_shares: float = Field(..., description="变动股份数量(万股)")
    total_shares_after: Optional[float] = Field(None, description="变动后持股数量(万股)")
    holding_ratio_after: Optional[float] = Field(None, description="变动后持股比例(%)")
    price_min: Optional[float] = Field(None, description="最低价格")
    price_max: Optional[float] = Field(None, description="最高价格")
    price_avg: Optional[float] = Field(None, description="平均价格")
    change_amount: Optional[float] = Field(None, description="变动金额(万元)")
    change_reason: Optional[str] = Field(None, description="变动原因")


class HoldingChangeCreate(HoldingChangeBase):
    """创建增减持记录模式"""
    stock_id: int = Field(..., description="股票ID")


class HoldingChangeUpdate(BaseModel):
    """更新增减持记录模式"""
    holder_name: Optional[str] = None
    holder_type: Optional[HolderType] = None
    direction: Optional[ChangeDirection] = None
    change_shares: Optional[float] = None
    total_shares_after: Optional[float] = None
    holding_ratio_after: Optional[float] = None
    price_min: Optional[float] = None
    price_max: Optional[float] = None
    price_avg: Optional[float] = None
    change_amount: Optional[float] = None
    change_reason: Optional[str] = None
    impact_score: Optional[float] = None


class HoldingChange(HoldingChangeBase):
    """增减持响应模式"""
    id: int
    stock_id: int
    impact_score: Optional[float] = Field(None, description="影响力评分")
    created_at: datetime
    updated_at: datetime
    stock: Stock

    class Config:
        from_attributes = True


# 查询和筛选模式
class HoldingChangeFilter(BaseModel):
    """增减持筛选条件"""
    stock_code: Optional[str] = Field(None, description="股票代码")
    stock_name: Optional[str] = Field(None, description="股票名称")
    holder_name: Optional[str] = Field(None, description="变动人姓名")
    holder_type: Optional[HolderType] = Field(None, description="持有人类型")
    direction: Optional[ChangeDirection] = Field(None, description="增减持方向")
    change_reason: Optional[str] = Field(None, description="变动原因")
    date_from: Optional[date] = Field(None, description="开始日期")
    date_to: Optional[date] = Field(None, description="结束日期")
    change_amount_min: Optional[float] = Field(None, description="最小变动金额")
    change_amount_max: Optional[float] = Field(None, description="最大变动金额")
    holding_ratio_min: Optional[float] = Field(None, description="最小持股比例")
    holding_ratio_max: Optional[float] = Field(None, description="最大持股比例")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")


class HoldingChangeResponse(BaseModel):
    """增减持查询响应"""
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    items: List[HoldingChange] = Field(..., description="数据列表")
