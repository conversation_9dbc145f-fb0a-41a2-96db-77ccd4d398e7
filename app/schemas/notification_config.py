"""
通知配置相关的Pydantic模式
"""
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, EmailStr, validator
from enum import Enum

from app.models.notification_config import NotificationServiceType


# 基础模式
class NotificationConfigBase(BaseModel):
    """通知配置基础模式"""
    name: str = Field(..., description="配置名称", max_length=100)
    service_type: NotificationServiceType = Field(..., description="通知服务类型")
    is_enabled: bool = Field(True, description="是否启用")
    is_default: bool = Field(False, description="是否为默认配置")
    description: Optional[str] = Field(None, description="配置描述", max_length=500)


# 邮件配置模式
class EmailConfigData(BaseModel):
    """邮件配置数据"""
    smtp_host: str = Field(..., description="SMTP服务器地址")
    smtp_port: int = Field(..., description="SMTP端口", ge=1, le=65535)
    smtp_username: str = Field(..., description="SMTP用户名")
    smtp_password: str = Field(..., description="SMTP密码")
    from_email: EmailStr = Field(..., description="发件人邮箱")
    from_name: str = Field(..., description="发件人名称")


# 微信配置模式
class WechatConfigData(BaseModel):
    """微信配置数据"""
    wechat_webhook_url: str = Field(..., description="企业微信Webhook URL")
    wechat_mentioned_list: Optional[List[str]] = Field(None, description="默认@用户列表")
    wechat_mentioned_mobile_list: Optional[List[str]] = Field(None, description="默认@手机号列表")


# 飞书配置模式
class FeishuConfigData(BaseModel):
    """飞书配置数据"""
    feishu_webhook_url: str = Field(..., description="飞书Webhook URL")
    feishu_at_all: bool = Field(False, description="是否@所有人")
    feishu_at_users: Optional[List[str]] = Field(None, description="默认@用户ID列表")


# 创建配置请求
class NotificationConfigCreate(NotificationConfigBase):
    """创建通知配置请求"""
    config_data: Union[EmailConfigData, WechatConfigData, FeishuConfigData] = Field(..., description="配置数据")
    
    @validator('config_data')
    def validate_config_data(cls, v, values):
        service_type = values.get('service_type')
        if service_type == NotificationServiceType.EMAIL and not isinstance(v, EmailConfigData):
            raise ValueError('邮件服务需要提供邮件配置数据')
        elif service_type == NotificationServiceType.WECHAT and not isinstance(v, WechatConfigData):
            raise ValueError('微信服务需要提供微信配置数据')
        elif service_type == NotificationServiceType.FEISHU and not isinstance(v, FeishuConfigData):
            raise ValueError('飞书服务需要提供飞书配置数据')
        return v


# 更新配置请求
class NotificationConfigUpdate(BaseModel):
    """更新通知配置请求"""
    name: Optional[str] = Field(None, description="配置名称", max_length=100)
    is_enabled: Optional[bool] = Field(None, description="是否启用")
    is_default: Optional[bool] = Field(None, description="是否为默认配置")
    description: Optional[str] = Field(None, description="配置描述", max_length=500)
    config_data: Optional[Union[EmailConfigData, WechatConfigData, FeishuConfigData]] = Field(None, description="配置数据")


# 响应模式
class NotificationConfigResponse(NotificationConfigBase):
    """通知配置响应"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    # 邮件配置字段（遮掩敏感信息）
    smtp_host: Optional[str] = None
    smtp_port: Optional[int] = None
    smtp_username: Optional[str] = None
    smtp_password: Optional[str] = None  # 遮掩后的密码
    from_email: Optional[str] = None
    from_name: Optional[str] = None
    
    # 微信配置字段（遮掩敏感信息）
    wechat_webhook_url: Optional[str] = None  # 遮掩后的URL
    wechat_mentioned_list: Optional[List[str]] = None
    wechat_mentioned_mobile_list: Optional[List[str]] = None
    
    # 飞书配置字段（遮掩敏感信息）
    feishu_webhook_url: Optional[str] = None  # 遮掩后的URL
    feishu_at_all: Optional[bool] = None
    feishu_at_users: Optional[List[str]] = None
    
    class Config:
        from_attributes = True


# 配置详情响应（包含敏感信息，仅用于编辑）
class NotificationConfigDetail(NotificationConfigResponse):
    """通知配置详情响应（包含敏感信息）"""
    pass  # 继承所有字段，但敏感字段不遮掩


# 配置列表响应
class NotificationConfigListResponse(BaseModel):
    """通知配置列表响应"""
    total: int = Field(..., description="总数")
    items: List[NotificationConfigResponse] = Field(..., description="配置列表")


# 配置测试请求
class NotificationConfigTest(BaseModel):
    """通知配置测试请求"""
    config_id: int = Field(..., description="配置ID")
    test_message: Optional[str] = Field("这是一条测试消息", description="测试消息内容")


# 配置测试响应
class NotificationConfigTestResponse(BaseModel):
    """通知配置测试响应"""
    success: bool = Field(..., description="测试是否成功")
    message: str = Field(..., description="测试结果消息")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")


# 配置状态响应
class NotificationConfigStatus(BaseModel):
    """通知配置状态响应"""
    service_type: NotificationServiceType = Field(..., description="服务类型")
    total_configs: int = Field(..., description="总配置数")
    enabled_configs: int = Field(..., description="启用配置数")
    has_default: bool = Field(..., description="是否有默认配置")
    default_config_id: Optional[int] = Field(None, description="默认配置ID")


# 批量操作请求
class NotificationConfigBatchOperation(BaseModel):
    """批量操作请求"""
    config_ids: List[int] = Field(..., description="配置ID列表")
    operation: str = Field(..., description="操作类型", pattern="^(enable|disable|delete)$")


# 批量操作响应
class NotificationConfigBatchResponse(BaseModel):
    """批量操作响应"""
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    failed_items: List[Dict[str, Any]] = Field(..., description="失败项目详情")


# 配置导入导出
class NotificationConfigExport(BaseModel):
    """配置导出格式"""
    configs: List[Dict[str, Any]] = Field(..., description="配置列表")
    export_time: datetime = Field(..., description="导出时间")
    version: str = Field("1.0", description="格式版本")


class NotificationConfigImport(BaseModel):
    """配置导入请求"""
    configs: List[Dict[str, Any]] = Field(..., description="配置列表")
    overwrite_existing: bool = Field(False, description="是否覆盖现有配置")


# 配置统计
class NotificationConfigStats(BaseModel):
    """通知配置统计"""
    total_configs: int = Field(..., description="总配置数")
    enabled_configs: int = Field(..., description="启用配置数")
    by_service_type: Dict[str, int] = Field(..., description="按服务类型统计")
    recent_activity: List[Dict[str, Any]] = Field(..., description="最近活动")


# 配置验证
class NotificationConfigValidation(BaseModel):
    """配置验证结果"""
    is_valid: bool = Field(..., description="是否有效")
    errors: List[str] = Field(..., description="错误列表")
    warnings: List[str] = Field(..., description="警告列表")
    suggestions: List[str] = Field(..., description="建议列表")
