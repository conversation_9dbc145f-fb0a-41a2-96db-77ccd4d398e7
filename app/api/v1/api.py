"""
API v1 路由汇总
"""
from fastapi import APIRouter

from app.api.v1.endpoints import stocks, holdings, analysis, scraping, stock_price, admin, system, data_management, notifications, feishu_webhook, simple_notifications, manual_notifications

api_router = APIRouter()

# 注册各模块路由
api_router.include_router(stocks.router, prefix="/stocks", tags=["stocks"])
api_router.include_router(holdings.router, prefix="/holdings", tags=["holdings"])
api_router.include_router(analysis.router, prefix="/analysis", tags=["analysis"])
api_router.include_router(scraping.router, prefix="/scraping", tags=["scraping"])
api_router.include_router(stock_price.router, prefix="/stock-price", tags=["stock-price"])
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])
api_router.include_router(system.router, prefix="/system", tags=["system"])
api_router.include_router(data_management.router, prefix="/data", tags=["data-management"])
api_router.include_router(notifications.router, prefix="/notifications", tags=["notifications"])
api_router.include_router(simple_notifications.router, prefix="/simple-notifications", tags=["simple-notifications"])
api_router.include_router(manual_notifications.router, prefix="/manual-notifications", tags=["manual-notifications"])
api_router.include_router(feishu_webhook.router, prefix="/feishu", tags=["feishu-webhook"])
