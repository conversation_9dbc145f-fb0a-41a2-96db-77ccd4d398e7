"""
数据管理API端点（仅管理员可访问）
"""
from datetime import datetime, date, timedelta
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.dependencies import admin_required
from app.models.admin import Admin
from app.models.holdings import HoldingChange, Stock
from app.services.scraper import TongHuaShunScraper
from app.services.holding_service import HoldingChangeService
from app.utils.scheduler import scheduler_manager

router = APIRouter()


class DataStats(BaseModel):
    """数据统计模式"""
    total_stocks: int = Field(..., description="股票总数")
    total_holdings: int = Field(..., description="增减持记录总数")
    recent_holdings: int = Field(..., description="最近30天记录数")
    last_update: Optional[datetime] = Field(None, description="最后更新时间")


class ScrapingTask(BaseModel):
    """抓取任务模式"""
    page: int = Field(1, ge=1, description="起始页码")
    max_pages: int = Field(5, ge=1, le=20, description="最大抓取页数")
    force_update: bool = Field(False, description="是否强制更新已存在的记录")


class DataCleanup(BaseModel):
    """数据清理模式"""
    days_to_keep: int = Field(730, ge=30, le=3650, description="保留天数")
    dry_run: bool = Field(True, description="是否为试运行")


@router.get("/stats", response_model=DataStats)
def get_data_stats(
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """获取数据统计信息"""
    # 统计股票数量
    total_stocks = db.query(Stock).count()
    
    # 统计增减持记录数量
    total_holdings = db.query(HoldingChange).count()
    
    # 统计最近30天的记录
    thirty_days_ago = date.today() - timedelta(days=30)
    recent_holdings = db.query(HoldingChange).filter(
        HoldingChange.announcement_date >= thirty_days_ago
    ).count()
    
    # 获取最后更新时间
    last_record = db.query(HoldingChange).order_by(desc(HoldingChange.created_at)).first()
    last_update = last_record.created_at if last_record else None
    
    return DataStats(
        total_stocks=total_stocks,
        total_holdings=total_holdings,
        recent_holdings=recent_holdings,
        last_update=last_update
    )


@router.get("/holdings/recent")
def get_recent_holdings(
    days: int = Query(7, ge=1, le=90, description="最近天数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """获取最近的增减持记录"""
    cutoff_date = date.today() - timedelta(days=days)
    
    holdings = db.query(HoldingChange).join(Stock).filter(
        HoldingChange.announcement_date >= cutoff_date
    ).order_by(desc(HoldingChange.announcement_date)).limit(limit).all()
    
    result = []
    for holding in holdings:
        result.append({
            "id": holding.id,
            "stock_code": holding.stock.code,
            "stock_name": holding.stock.name,
            "holder_name": holding.holder_name,
            "change_type": holding.change_type,
            "change_shares": holding.change_shares,
            "change_ratio": holding.change_ratio,
            "announcement_date": holding.announcement_date,
            "created_at": holding.created_at
        })
    
    return {
        "total": len(result),
        "days": days,
        "data": result
    }


@router.post("/scraping/start")
async def start_manual_scraping(
    task: ScrapingTask,
    background_tasks: BackgroundTasks,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """启动手动数据抓取任务"""
    try:
        # 添加后台任务
        background_tasks.add_task(
            _manual_scrape_task,
            db_session=db,
            page=task.page,
            max_pages=task.max_pages,
            force_update=task.force_update,
            admin_id=current_admin.id
        )
        
        return {
            "message": "手动数据抓取任务已启动",
            "status": "started",
            "parameters": {
                "start_page": task.page,
                "max_pages": task.max_pages,
                "force_update": task.force_update
            },
            "started_by": current_admin.username
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启动抓取任务失败: {str(e)}"
        )


@router.post("/cleanup/preview")
def preview_data_cleanup(
    cleanup: DataCleanup,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """预览数据清理结果"""
    cutoff_date = date.today() - timedelta(days=cleanup.days_to_keep)
    
    # 统计将要删除的记录
    records_to_delete = db.query(HoldingChange).filter(
        HoldingChange.announcement_date < cutoff_date
    ).count()
    
    # 按股票统计
    stock_stats = db.query(
        Stock.code,
        Stock.name,
        func.count(HoldingChange.id).label('count')
    ).join(HoldingChange).filter(
        HoldingChange.announcement_date < cutoff_date
    ).group_by(Stock.code, Stock.name).order_by(desc('count')).limit(10).all()
    
    return {
        "cutoff_date": cutoff_date,
        "records_to_delete": records_to_delete,
        "top_affected_stocks": [
            {
                "code": stat.code,
                "name": stat.name,
                "records_count": stat.count
            }
            for stat in stock_stats
        ]
    }


@router.post("/cleanup/execute")
def execute_data_cleanup(
    cleanup: DataCleanup,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """执行数据清理"""
    if cleanup.dry_run:
        return preview_data_cleanup(cleanup, current_admin, db)
    
    cutoff_date = date.today() - timedelta(days=cleanup.days_to_keep)
    
    try:
        # 删除过期记录
        deleted_count = db.query(HoldingChange).filter(
            HoldingChange.announcement_date < cutoff_date
        ).delete()
        
        db.commit()
        
        return {
            "message": "数据清理完成",
            "cutoff_date": cutoff_date,
            "deleted_records": deleted_count,
            "executed_by": current_admin.username
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"数据清理失败: {str(e)}"
        )


@router.get("/duplicates/check")
def check_duplicates(
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """检查重复记录"""
    # 查找可能的重复记录
    duplicates = db.query(
        HoldingChange.stock_id,
        HoldingChange.holder_name,
        HoldingChange.announcement_date,
        func.count(HoldingChange.id).label('count')
    ).group_by(
        HoldingChange.stock_id,
        HoldingChange.holder_name,
        HoldingChange.announcement_date
    ).having(func.count(HoldingChange.id) > 1).all()
    
    duplicate_groups = []
    for dup in duplicates:
        # 获取重复记录的详细信息
        records = db.query(HoldingChange).filter(
            HoldingChange.stock_id == dup.stock_id,
            HoldingChange.holder_name == dup.holder_name,
            HoldingChange.announcement_date == dup.announcement_date
        ).all()
        
        duplicate_groups.append({
            "stock_id": dup.stock_id,
            "holder_name": dup.holder_name,
            "announcement_date": dup.announcement_date,
            "count": dup.count,
            "records": [
                {
                    "id": record.id,
                    "change_shares": record.change_shares,
                    "change_ratio": record.change_ratio,
                    "created_at": record.created_at
                }
                for record in records
            ]
        })
    
    return {
        "total_duplicate_groups": len(duplicate_groups),
        "duplicates": duplicate_groups
    }


@router.delete("/duplicates/remove")
def remove_duplicates(
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """移除重复记录（保留最新的）"""
    try:
        # 查找重复记录组
        duplicates = db.query(
            HoldingChange.stock_id,
            HoldingChange.holder_name,
            HoldingChange.announcement_date,
            func.count(HoldingChange.id).label('count')
        ).group_by(
            HoldingChange.stock_id,
            HoldingChange.holder_name,
            HoldingChange.announcement_date
        ).having(func.count(HoldingChange.id) > 1).all()
        
        removed_count = 0
        
        for dup in duplicates:
            # 获取该组的所有记录，按创建时间排序
            records = db.query(HoldingChange).filter(
                HoldingChange.stock_id == dup.stock_id,
                HoldingChange.holder_name == dup.holder_name,
                HoldingChange.announcement_date == dup.announcement_date
            ).order_by(desc(HoldingChange.created_at)).all()
            
            # 保留最新的记录，删除其他的
            for record in records[1:]:  # 跳过第一个（最新的）
                db.delete(record)
                removed_count += 1
        
        db.commit()
        
        return {
            "message": "重复记录清理完成",
            "removed_records": removed_count,
            "executed_by": current_admin.username
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理重复记录失败: {str(e)}"
        )


async def _manual_scrape_task(
    db_session: Session,
    page: int,
    max_pages: int,
    force_update: bool,
    admin_id: int
):
    """手动抓取任务的后台执行函数"""
    try:
        scraper = TongHuaShunScraper()
        
        # 抓取数据
        scraped_data = await scraper.scrape_holdings_data(
            page=page,
            max_pages=max_pages
        )
        
        if scraped_data:
            # 保存到数据库
            holding_service = HoldingChangeService(db_session)
            created_changes = holding_service.batch_create_holding_changes(scraped_data)
            
            print(f"手动抓取任务完成，保存了 {len(created_changes)} 条记录")
        else:
            print("手动抓取任务完成，但未获取到新数据")
            
    except Exception as e:
        print(f"手动抓取任务失败: {e}")
    finally:
        db_session.close()
