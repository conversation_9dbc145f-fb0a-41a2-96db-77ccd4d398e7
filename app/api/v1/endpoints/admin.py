"""
管理员API端点
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.dependencies import admin_required, superuser_required, get_current_active_admin
from app.core.auth import create_admin_token
from app.models.admin import Admin
from app.schemas.admin import (
    Admin as AdminSchema,
    AdminCreate,
    AdminUpdate,
    AdminPasswordUpdate,
    AdminLogin,
    AdminToken,
    SystemConfig as SystemConfigSchema,
    SystemConfigCreate,
    SystemConfigUpdate
)
from app.services.admin_service import AdminService, SystemConfigService

router = APIRouter()


@router.post("/login", response_model=AdminToken)
def admin_login(
    login_data: AdminLogin,
    db: Session = Depends(get_db)
):
    """管理员登录"""
    admin_service = AdminService(db)
    admin = admin_service.authenticate_admin(login_data.username, login_data.password)
    
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 创建访问令牌
    access_token = create_admin_token(admin.id, admin.username)
    
    return AdminToken(
        access_token=access_token,
        token_type="bearer",
        admin=AdminSchema.from_orm(admin)
    )


@router.get("/me", response_model=AdminSchema)
def get_current_admin_info(
    current_admin: Admin = Depends(get_current_active_admin)
):
    """获取当前管理员信息"""
    return AdminSchema.from_orm(current_admin)


@router.put("/me", response_model=AdminSchema)
def update_current_admin(
    admin_data: AdminUpdate,
    current_admin: Admin = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """更新当前管理员信息"""
    admin_service = AdminService(db)
    
    # 非超级管理员不能修改自己的超级管理员状态
    if not current_admin.is_superuser and admin_data.is_superuser is not None:
        admin_data.is_superuser = None
    
    updated_admin = admin_service.update_admin(current_admin.id, admin_data)
    if not updated_admin:
        raise HTTPException(status_code=404, detail="管理员不存在")
    
    return AdminSchema.from_orm(updated_admin)


@router.put("/me/password")
def update_current_admin_password(
    password_data: AdminPasswordUpdate,
    current_admin: Admin = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """更新当前管理员密码"""
    admin_service = AdminService(db)
    success = admin_service.update_admin_password(current_admin.id, password_data)
    
    if not success:
        raise HTTPException(status_code=404, detail="管理员不存在")
    
    return {"message": "密码更新成功"}


@router.get("/", response_model=List[AdminSchema])
def get_admins(
    skip: int = 0,
    limit: int = 100,
    current_admin: Admin = Depends(superuser_required),
    db: Session = Depends(get_db)
):
    """获取管理员列表（仅超级管理员）"""
    admin_service = AdminService(db)
    admins = admin_service.get_admins(skip=skip, limit=limit)
    return [AdminSchema.from_orm(admin) for admin in admins]


@router.post("/", response_model=AdminSchema)
def create_admin(
    admin_data: AdminCreate,
    current_admin: Admin = Depends(superuser_required),
    db: Session = Depends(get_db)
):
    """创建管理员（仅超级管理员）"""
    admin_service = AdminService(db)
    admin = admin_service.create_admin(admin_data)
    return AdminSchema.from_orm(admin)


@router.get("/{admin_id}", response_model=AdminSchema)
def get_admin(
    admin_id: int,
    current_admin: Admin = Depends(superuser_required),
    db: Session = Depends(get_db)
):
    """获取指定管理员信息（仅超级管理员）"""
    admin_service = AdminService(db)
    admin = admin_service.get_admin_by_id(admin_id)
    
    if not admin:
        raise HTTPException(status_code=404, detail="管理员不存在")
    
    return AdminSchema.from_orm(admin)


@router.put("/{admin_id}", response_model=AdminSchema)
def update_admin(
    admin_id: int,
    admin_data: AdminUpdate,
    current_admin: Admin = Depends(superuser_required),
    db: Session = Depends(get_db)
):
    """更新指定管理员信息（仅超级管理员）"""
    admin_service = AdminService(db)
    updated_admin = admin_service.update_admin(admin_id, admin_data)
    
    if not updated_admin:
        raise HTTPException(status_code=404, detail="管理员不存在")
    
    return AdminSchema.from_orm(updated_admin)


@router.delete("/{admin_id}")
def delete_admin(
    admin_id: int,
    current_admin: Admin = Depends(superuser_required),
    db: Session = Depends(get_db)
):
    """删除指定管理员（仅超级管理员）"""
    if admin_id == current_admin.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己的账户"
        )
    
    admin_service = AdminService(db)
    success = admin_service.delete_admin(admin_id)
    
    if not success:
        raise HTTPException(status_code=404, detail="管理员不存在")
    
    return {"message": "管理员删除成功"}


# 系统配置相关端点
@router.get("/config/", response_model=List[SystemConfigSchema])
def get_system_configs(
    group: str = None,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """获取系统配置列表"""
    config_service = SystemConfigService(db)
    
    if group:
        configs = config_service.get_configs_by_group(group)
    else:
        configs = config_service.get_all_configs()
    
    return [SystemConfigSchema.from_orm(config) for config in configs]


@router.post("/config/", response_model=SystemConfigSchema)
def create_system_config(
    config_data: SystemConfigCreate,
    current_admin: Admin = Depends(superuser_required),
    db: Session = Depends(get_db)
):
    """创建系统配置（仅超级管理员）"""
    config_service = SystemConfigService(db)
    config = config_service.create_config(config_data)
    return SystemConfigSchema.from_orm(config)


@router.get("/config/{config_key}", response_model=SystemConfigSchema)
def get_system_config(
    config_key: str,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """获取指定系统配置"""
    config_service = SystemConfigService(db)
    config = config_service.get_config_by_key(config_key)
    
    if not config:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    return SystemConfigSchema.from_orm(config)


@router.put("/config/{config_key}", response_model=SystemConfigSchema)
def update_system_config(
    config_key: str,
    config_data: SystemConfigUpdate,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """更新系统配置"""
    config_service = SystemConfigService(db)
    updated_config = config_service.update_config(config_key, config_data)
    
    if not updated_config:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    return SystemConfigSchema.from_orm(updated_config)


@router.delete("/config/{config_key}")
def delete_system_config(
    config_key: str,
    current_admin: Admin = Depends(superuser_required),
    db: Session = Depends(get_db)
):
    """删除系统配置（仅超级管理员）"""
    config_service = SystemConfigService(db)
    success = config_service.delete_config(config_key)
    
    if not success:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    return {"message": "配置删除成功"}
