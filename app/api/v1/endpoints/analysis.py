"""
数据分析相关API端点
"""
from typing import Dict, List
from datetime import date, datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.stock_data import StockDataService
from app.services.holding_service import HoldingChangeService

router = APIRouter()


@router.get("/price-impact/{stock_code}")
def get_price_impact_analysis(
    stock_code: str,
    event_date: str = Query(..., description="事件日期 (YYYY-MM-DD)"),
    before_days: int = Query(5, ge=1, le=30, description="事件前天数"),
    after_days: int = Query(20, ge=1, le=60, description="事件后天数"),
):
    """分析增减持事件对股价的影响"""
    try:
        # 解析日期
        parsed_date = datetime.strptime(event_date, "%Y-%m-%d").date()
        
        # 获取股价影响分析
        stock_data_service = StockDataService()
        impact_data = stock_data_service.calculate_price_impact(
            stock_code=stock_code,
            event_date=parsed_date,
            before_days=before_days,
            after_days=after_days
        )
        
        if not impact_data:
            raise HTTPException(status_code=404, detail="无法获取股价数据")
        
        return impact_data
        
    except ValueError:
        raise HTTPException(status_code=400, detail="日期格式错误，请使用 YYYY-MM-DD 格式")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")


@router.get("/relative-performance/{stock_code}")
def get_relative_performance(
    stock_code: str,
    event_date: str = Query(..., description="事件日期 (YYYY-MM-DD)"),
    days: int = Query(20, ge=1, le=60, description="分析天数"),
    benchmark: str = Query("000001", description="基准指数代码"),
):
    """分析股票相对市场的表现"""
    try:
        # 解析日期
        parsed_date = datetime.strptime(event_date, "%Y-%m-%d").date()
        
        # 获取相对表现分析
        stock_data_service = StockDataService()
        performance_data = stock_data_service.calculate_relative_performance(
            stock_code=stock_code,
            event_date=parsed_date,
            days=days,
            benchmark=benchmark
        )
        
        if not performance_data:
            raise HTTPException(status_code=404, detail="无法获取相对表现数据")
        
        return performance_data
        
    except ValueError:
        raise HTTPException(status_code=400, detail="日期格式错误，请使用 YYYY-MM-DD 格式")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")


@router.get("/stock-chart/{stock_code}")
def get_stock_chart_data(
    stock_code: str,
    start_date: str = Query(..., description="开始日期 (YYYY-MM-DD)"),
    end_date: str = Query(..., description="结束日期 (YYYY-MM-DD)"),
    adjust: str = Query("qfq", description="复权类型 (qfq/hfq/none)"),
    db: Session = Depends(get_db)
):
    """获取股票图表数据（包含增减持事件标注）"""
    try:
        # 解析日期
        start_dt = datetime.strptime(start_date, "%Y-%m-%d").date()
        end_dt = datetime.strptime(end_date, "%Y-%m-%d").date()
        
        # 获取股价数据
        stock_data_service = StockDataService()
        price_df = stock_data_service.get_stock_price_data(
            stock_code=stock_code,
            start_date=start_dt,
            end_date=end_dt,
            adjust=adjust
        )
        
        if price_df is None or price_df.empty:
            raise HTTPException(status_code=404, detail="无法获取股价数据")
        
        # 获取增减持事件
        holding_service = HoldingChangeService(db)
        holding_changes = holding_service.get_stock_holding_changes(stock_code, limit=100)
        
        # 过滤日期范围内的事件
        events = []
        for change in holding_changes:
            # 使用变动日期进行过滤，如果变动日期为空则使用公告日期
            event_date = change.change_date or change.announcement_date
            if start_dt <= event_date <= end_dt:
                events.append({
                    'date': event_date.isoformat(),
                    'holder_name': change.holder_name,
                    'direction': change.direction.value if hasattr(change.direction, 'value') else change.direction,
                    'change_amount': change.change_amount or 0,
                    'change_shares': change.change_shares or 0,
                    'holding_ratio_after': change.holding_ratio_after,
                    'impact_score': change.impact_score,
                    'announcement_date': change.announcement_date.isoformat(),
                    'change_date': change.change_date.isoformat() if change.change_date else None
                })
        
        # 转换价格数据为图表格式
        chart_data = []
        for idx, row in price_df.iterrows():
            chart_data.append({
                'date': idx.strftime('%Y-%m-%d'),
                'open': float(row['open']),
                'close': float(row['close']),
                'high': float(row['high']),
                'low': float(row['low']),
                'volume': float(row['volume']) if 'volume' in row else 0
            })
        
        return {
            'stock_code': stock_code,
            'price_data': chart_data,
            'events': events,
            'period': {
                'start_date': start_date,
                'end_date': end_date
            }
        }
        
    except ValueError:
        raise HTTPException(status_code=400, detail="日期格式错误，请使用 YYYY-MM-DD 格式")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图表数据失败: {str(e)}")


@router.get("/market-overview")
def get_market_overview(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db)
):
    """获取市场增减持概览"""
    try:
        holding_service = HoldingChangeService(db)
        
        # 获取基础统计
        stats = holding_service.get_statistics()
        
        # 获取最近的热门变动
        recent_changes = holding_service.get_recent_changes(days=days, limit=10)
        top_changes = holding_service.get_top_changes_by_amount(limit=10)
        
        # 构建概览数据
        overview = {
            'statistics': stats,
            'recent_highlights': [
                {
                    'stock_code': change.stock.code,
                    'stock_name': change.stock.name,
                    'holder_name': change.holder_name,
                    'direction': change.direction.value if hasattr(change.direction, 'value') else change.direction,
                    'change_amount': change.change_amount,
                    'announcement_date': change.announcement_date.isoformat(),
                    'impact_score': change.impact_score
                }
                for change in recent_changes[:5]
            ],
            'top_changes_by_amount': [
                {
                    'stock_code': change.stock.code,
                    'stock_name': change.stock.name,
                    'holder_name': change.holder_name,
                    'direction': change.direction.value if hasattr(change.direction, 'value') else change.direction,
                    'change_amount': change.change_amount,
                    'announcement_date': change.announcement_date.isoformat(),
                    'impact_score': change.impact_score
                }
                for change in top_changes[:5]
            ]
        }
        
        return overview
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取市场概览失败: {str(e)}")


@router.get("/trend-analysis")
def get_trend_analysis(
    days: int = Query(90, ge=30, le=365, description="分析天数"),
    db: Session = Depends(get_db)
):
    """获取增减持趋势分析"""
    try:
        from datetime import timedelta
        from sqlalchemy import func
        from app.models.holdings import HoldingChange, ChangeDirection
        
        # 计算日期范围
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        # 按日期统计增减持数量
        daily_stats = db.query(
            HoldingChange.announcement_date,
            HoldingChange.direction,
            func.count(HoldingChange.id).label('count'),
            func.sum(HoldingChange.change_amount).label('total_amount')
        ).filter(
            HoldingChange.announcement_date >= start_date,
            HoldingChange.announcement_date <= end_date
        ).group_by(
            HoldingChange.announcement_date,
            HoldingChange.direction
        ).all()
        
        # 整理数据
        trend_data = {}
        for stat in daily_stats:
            date_str = stat.announcement_date.isoformat()
            if date_str not in trend_data:
                trend_data[date_str] = {
                    'date': date_str,
                    'increase_count': 0,
                    'decrease_count': 0,
                    'increase_amount': 0,
                    'decrease_amount': 0
                }
            
            if stat.direction == ChangeDirection.INCREASE:
                trend_data[date_str]['increase_count'] = stat.count
                trend_data[date_str]['increase_amount'] = float(stat.total_amount or 0)
            else:
                trend_data[date_str]['decrease_count'] = stat.count
                trend_data[date_str]['decrease_amount'] = float(stat.total_amount or 0)
        
        # 转换为列表并排序
        trend_list = sorted(trend_data.values(), key=lambda x: x['date'])
        
        return {
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': days
            },
            'trend_data': trend_list
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取趋势分析失败: {str(e)}")
