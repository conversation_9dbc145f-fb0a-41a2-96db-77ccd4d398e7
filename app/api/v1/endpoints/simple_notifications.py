"""
简化的通知配置API - 不使用加密
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from loguru import logger

from app.core.database import get_db
from app.core.dependencies import get_current_admin
from app.models.admin import Admin
from app.models.notification_config import NotificationServiceType
from app.services.simple_notification_config_service import SimpleNotificationConfigService

router = APIRouter()


@router.get("/configs")
async def get_notification_configs(
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取通知配置列表"""
    try:
        service = SimpleNotificationConfigService(db)
        configs = service.get_all_configs()
        
        return {
            "success": True,
            "data": [config.to_dict() for config in configs]
        }
    except Exception as e:
        logger.error(f"获取通知配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取通知配置失败"
        )


@router.get("/configs/{service_type}")
async def get_notification_config(
    service_type: NotificationServiceType,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取指定服务类型的配置"""
    try:
        service = SimpleNotificationConfigService(db)
        config = service.get_config_by_service_type(service_type)
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="配置不存在"
            )
        
        return {
            "success": True,
            "data": config.to_dict()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取通知配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取通知配置失败"
        )


@router.put("/configs/{service_type}")
async def update_notification_config(
    service_type: NotificationServiceType,
    config_data: Dict[str, Any],
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """更新通知配置"""
    try:
        service = SimpleNotificationConfigService(db)
        config = service.create_or_update_config(service_type, config_data)
        
        return {
            "success": True,
            "message": "配置更新成功",
            "data": config.to_dict()
        }
    except Exception as e:
        logger.error(f"更新通知配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新配置失败"
        )


@router.post("/configs/{service_type}/test")
async def test_notification_config(
    service_type: NotificationServiceType,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """测试通知配置 - 发送真实测试消息"""
    try:
        service = SimpleNotificationConfigService(db)
        result = await service.test_config(service_type)

        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"测试通知配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="测试配置失败"
        )


@router.delete("/configs/{service_type}")
async def delete_notification_config(
    service_type: NotificationServiceType,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """删除通知配置"""
    try:
        service = SimpleNotificationConfigService(db)
        success = service.delete_config(service_type)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="配置不存在"
            )
        
        return {
            "success": True,
            "message": "配置删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除通知配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除配置失败"
        )


@router.post("/configs/initialize")
async def initialize_default_configs(
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """初始化默认配置"""
    try:
        service = SimpleNotificationConfigService(db)
        service.initialize_default_configs()
        
        return {
            "success": True,
            "message": "默认配置初始化成功"
        }
    except Exception as e:
        logger.error(f"初始化默认配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="初始化配置失败"
        )


@router.get("/configs/enabled/list")
async def get_enabled_configs(
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取所有启用的配置"""
    try:
        service = SimpleNotificationConfigService(db)
        configs = service.get_enabled_configs()
        
        return {
            "success": True,
            "data": [config.to_dict() for config in configs]
        }
    except Exception as e:
        logger.error(f"获取启用配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取启用配置失败"
        )


@router.get("/service-types")
async def get_service_types():
    """获取所有支持的服务类型"""
    return {
        "success": True,
        "data": [
            {
                "value": "EMAIL",
                "name": "邮件通知",
                "description": "通过SMTP发送邮件通知"
            },
            {
                "value": "WECHAT", 
                "name": "企业微信",
                "description": "通过企业微信机器人发送通知"
            },
            {
                "value": "FEISHU",
                "name": "飞书",
                "description": "通过飞书机器人发送通知"
            }
        ]
    }
