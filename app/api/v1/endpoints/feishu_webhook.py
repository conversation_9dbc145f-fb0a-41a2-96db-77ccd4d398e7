"""
飞书Webhook接收接口（用于接收飞书回调和验证签名）
"""
import json
from typing import Dict, Any
from fastapi import APIRouter, Request, HTTPException, Header
from loguru import logger

from app.core.config import settings
from app.services.notifications.feishu_service import FeishuNotificationService

router = APIRouter()


@router.post("/webhook")
async def feishu_webhook(
    request: Request,
    x_lark_request_timestamp: str = Header(None, alias="X-Lark-Request-Timestamp"),
    x_lark_request_nonce: str = Header(None, alias="X-Lark-Request-Nonce"),
    x_lark_signature: str = Header(None, alias="X-Lark-Signature")
):
    """
    接收飞书Webhook回调
    
    用于验证飞书签名和处理回调事件
    """
    try:
        # 获取请求体
        body = await request.body()
        body_str = body.decode('utf-8')
        
        # 验证签名（如果配置了签名密钥）
        if settings.FEISHU_SECRET_KEY:
            if not all([x_lark_request_timestamp, x_lark_request_nonce, x_lark_signature]):
                raise HTTPException(status_code=400, detail="缺少必要的签名头")
            
            # 创建飞书服务实例进行签名验证
            feishu_service = FeishuNotificationService({
                'webhook_url': settings.FEISHU_WEBHOOK_URL,
                'secret_key': settings.FEISHU_SECRET_KEY
            })
            
            # 验证签名
            is_valid = feishu_service._verify_sign(
                x_lark_request_timestamp,
                x_lark_request_nonce,
                x_lark_signature,
                settings.FEISHU_SECRET_KEY
            )
            
            if not is_valid:
                logger.warning("飞书Webhook签名验证失败")
                raise HTTPException(status_code=401, detail="签名验证失败")
        
        # 解析请求数据
        try:
            data = json.loads(body_str)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="无效的JSON数据")
        
        # 处理不同类型的事件
        event_type = data.get('type')
        
        if event_type == 'url_verification':
            # URL验证事件
            challenge = data.get('challenge', '')
            logger.info(f"飞书URL验证: {challenge}")
            return {"challenge": challenge}
        
        elif event_type == 'event_callback':
            # 事件回调
            event = data.get('event', {})
            event_type = event.get('type')
            
            logger.info(f"收到飞书事件: {event_type}")
            
            # 这里可以处理各种飞书事件
            # 例如：消息事件、用户操作事件等
            
            return {"code": 0, "msg": "success"}
        
        else:
            logger.warning(f"未知的飞书事件类型: {event_type}")
            return {"code": 0, "msg": "success"}
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理飞书Webhook失败: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.get("/test-signature")
async def test_feishu_signature():
    """
    测试飞书签名生成
    
    用于调试和验证签名算法
    """
    if not settings.FEISHU_SECRET_KEY:
        raise HTTPException(status_code=400, detail="未配置飞书签名密钥")
    
    try:
        import time
        
        # 创建飞书服务实例
        feishu_service = FeishuNotificationService({
            'webhook_url': settings.FEISHU_WEBHOOK_URL,
            'secret_key': settings.FEISHU_SECRET_KEY
        })
        
        # 生成测试签名
        timestamp = str(int(time.time()))
        signature = feishu_service._generate_sign(timestamp, settings.FEISHU_SECRET_KEY)
        
        return {
            "timestamp": timestamp,
            "secret_key_configured": bool(settings.FEISHU_SECRET_KEY),
            "signature": signature,
            "webhook_url_configured": bool(settings.FEISHU_WEBHOOK_URL)
        }
        
    except Exception as e:
        logger.error(f"测试飞书签名失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试失败: {str(e)}")


@router.post("/send-test")
async def send_test_message():
    """
    发送测试消息到飞书
    
    用于验证配置和签名是否正确
    """
    if not settings.FEISHU_WEBHOOK_URL:
        raise HTTPException(status_code=400, detail="未配置飞书Webhook URL")
    
    try:
        from app.schemas.notifications import NotificationContentBase, NotificationPriority
        
        # 创建飞书服务实例
        feishu_service = FeishuNotificationService({
            'webhook_url': settings.FEISHU_WEBHOOK_URL,
            'secret_key': settings.FEISHU_SECRET_KEY,
            'enabled': True
        })
        
        # 创建测试消息
        test_content = NotificationContentBase(
            title="🧪 飞书签名验证测试",
            content="这是一条测试消息，用于验证飞书机器人配置和签名验证是否正常工作。",
            summary="飞书通知服务测试"
        )
        
        # 发送测试消息
        result = await feishu_service.send_notification(
            content=test_content,
            config={'webhook_url': settings.FEISHU_WEBHOOK_URL},
            priority=NotificationPriority.LOW
        )
        
        return {
            "success": result.success,
            "message": result.message,
            "signature_enabled": bool(settings.FEISHU_SECRET_KEY),
            "data": result.data
        }
        
    except Exception as e:
        logger.error(f"发送飞书测试消息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发送失败: {str(e)}")
