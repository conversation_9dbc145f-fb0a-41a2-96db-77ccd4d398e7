"""
数据抓取相关API端点
"""
from typing import Dict
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.dependencies import admin_required
from app.models.admin import Admin
from app.services.scraper import TongHuaShunScraper
from app.services.holding_service import HoldingChangeService

router = APIRouter()

# 全局爬虫实例
scraper = TongHuaShunScraper()


@router.post("/start")
async def start_scraping(
    background_tasks: BackgroundTasks,
    page: int = Query(1, ge=1, description="起始页码"),
    max_pages: int = Query(5, ge=1, le=20, description="最大抓取页数"),
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """启动数据抓取任务"""
    try:
        # 添加后台任务
        background_tasks.add_task(
            scrape_and_save_data,
            db=db,
            page=page,
            max_pages=max_pages
        )
        
        return {
            "message": "数据抓取任务已启动",
            "status": "started",
            "parameters": {
                "start_page": page,
                "max_pages": max_pages
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动抓取任务失败: {str(e)}")


@router.get("/status")
def get_scraping_status():
    """获取抓取状态"""
    # 这里可以实现更复杂的状态跟踪
    return {
        "status": "ready",
        "message": "爬虫服务正常运行",
        "last_update": None
    }


async def scrape_and_save_data(db: Session, page: int = 1, max_pages: int = 5):
    """
    抓取并保存数据的后台任务
    """
    try:
        from loguru import logger
        
        logger.info(f"开始抓取数据: 页码 {page}-{page+max_pages-1}")
        
        # 抓取数据
        scraped_data = await scraper.scrape_holdings_data(page=page, max_pages=max_pages)
        
        if not scraped_data:
            logger.warning("未抓取到任何数据")
            return
        
        # 保存到数据库
        holding_service = HoldingChangeService(db)
        created_changes = holding_service.batch_create_holding_changes(scraped_data)
        
        logger.info(f"成功保存 {len(created_changes)} 条增减持记录")
        
    except Exception as e:
        from loguru import logger
        logger.error(f"抓取和保存数据失败: {e}")


@router.post("/test")
async def test_scraping(
    max_pages: int = Query(1, ge=1, le=3, description="测试抓取页数"),
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """测试数据抓取功能"""
    try:
        # 测试抓取少量数据
        scraped_data = await scraper.scrape_holdings_data(page=1, max_pages=max_pages)

        # 保存到数据库
        if scraped_data:
            holding_service = HoldingChangeService(db)
            created_changes = holding_service.batch_create_holding_changes(scraped_data)
            saved_count = len(created_changes)
        else:
            saved_count = 0

        return {
            "message": "测试抓取完成",
            "data_count": len(scraped_data),
            "saved_count": saved_count,
            "sample_data": scraped_data[:3] if scraped_data else []
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试抓取失败: {str(e)}")


@router.get("/scheduler/status")
def get_scheduler_status():
    """获取定时任务状态"""
    from app.utils.scheduler import scheduler_manager
    return scheduler_manager.get_job_status()


@router.post("/scheduler/trigger/{job_id}")
def trigger_scheduled_job(
    job_id: str,
    current_admin: Admin = Depends(admin_required)
):
    """手动触发定时任务"""
    from app.utils.scheduler import scheduler_manager

    success = scheduler_manager.trigger_job(job_id)
    if success:
        return {"message": f"任务 {job_id} 已触发", "status": "success"}
    else:
        raise HTTPException(status_code=404, detail=f"任务 {job_id} 不存在")


@router.get("/data-sources")
def get_data_sources():
    """获取数据源信息"""
    return {
        "sources": [
            {
                "name": "专业金融数据源",
                "description": "提供A股上市公司增减持数据",
                "update_frequency": "实时更新",
                "data_fields": [
                    "股票代码和名称",
                    "公告日期",
                    "变动人",
                    "变动情况",
                    "变动股份",
                    "变动比例",
                    "价格区间",
                    "变动金额"
                ]
            },
            {
                "name": "股价数据源",
                "description": "提供A股历史价格数据",
                "update_frequency": "每日更新",
                "data_fields": [
                    "开盘价",
                    "收盘价",
                    "最高价",
                    "最低价",
                    "成交量",
                    "成交额"
                ]
            }
        ]
    }


@router.post("/scheduler/restart")
async def restart_scheduler(
    current_admin: Admin = Depends(admin_required)
):
    """重新启动调度器以应用新配置"""
    try:
        from app.utils.scheduler import scheduler_manager

        # 如果调度器正在运行，先停止
        if scheduler_manager.scheduler.running:
            scheduler_manager.stop()

        # 重新启动调度器
        scheduler_manager.start()
        return {"message": "调度器重启成功"}
    except Exception as e:
        from loguru import logger
        logger.error(f"重启调度器失败: {e}")
        raise HTTPException(status_code=500, detail=f"重启调度器失败: {str(e)}")
