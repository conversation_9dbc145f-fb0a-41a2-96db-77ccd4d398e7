"""
系统设置API端点
"""
from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.dependencies import admin_required, superuser_required
from app.models.admin import Admin
from app.services.system_config_service import SystemConfigManager, SchedulerConfigManager
from app.utils.scheduler import scheduler_manager

router = APIRouter()


class SchedulerConfigUpdate(BaseModel):
    """调度器配置更新模式"""
    scraping_enabled: bool = Field(None, description="是否启用数据抓取")
    scraping_interval_minutes: int = Field(None, ge=1, le=1440, description="抓取间隔（分钟）")
    scraping_max_pages: int = Field(None, ge=1, le=50, description="最大抓取页数")
    data_cleanup_enabled: bool = Field(None, description="是否启用数据清理")
    data_retention_days: int = Field(None, ge=30, le=3650, description="数据保留天数")
    impact_score_update_enabled: bool = Field(None, description="是否启用影响力评分更新")
    health_check_enabled: bool = Field(None, description="是否启用健康检查")


class JobControl(BaseModel):
    """任务控制模式"""
    action: str = Field(..., description="操作类型: trigger, pause, resume")


@router.get("/config/scheduler")
def get_scheduler_config(
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """获取调度器配置"""
    config_manager = SystemConfigManager(db)
    return config_manager.get_scheduler_config()


@router.put("/config/scheduler")
def update_scheduler_config(
    config_update: SchedulerConfigUpdate,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """更新调度器配置"""
    config_manager = SystemConfigManager(db)
    
    # 准备更新数据
    updates = {}
    update_data = config_update.dict(exclude_unset=True)
    
    for key, value in update_data.items():
        if value is not None:
            updates[key] = value
    
    if not updates:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="没有提供要更新的配置"
        )
    
    # 更新配置
    success = config_manager.update_scheduler_config(updates)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新配置失败"
        )
    
    # 重新加载调度器任务
    if scheduler_manager.is_running:
        reload_success = scheduler_manager.reload_jobs()
        if not reload_success:
            return {
                "message": "配置更新成功，但重新加载任务失败",
                "config_updated": True,
                "jobs_reloaded": False
            }
    
    return {
        "message": "调度器配置更新成功",
        "config_updated": True,
        "jobs_reloaded": scheduler_manager.is_running
    }


@router.get("/config/groups/{group_name}")
def get_config_group(
    group_name: str,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """获取指定分组的配置"""
    config_manager = SystemConfigManager(db)
    return config_manager.get_configs_by_group(group_name)


@router.get("/scheduler/status")
def get_scheduler_status(
    current_admin: Admin = Depends(admin_required)
):
    """获取调度器状态"""
    return scheduler_manager.get_job_status()


@router.post("/scheduler/reload")
def reload_scheduler(
    current_admin: Admin = Depends(admin_required)
):
    """重新加载调度器配置"""
    if not scheduler_manager.is_running:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="调度器未运行"
        )
    
    success = scheduler_manager.reload_jobs()
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重新加载调度器失败"
        )
    
    return {"message": "调度器配置重新加载成功"}


@router.post("/scheduler/jobs/{job_id}/control")
def control_job(
    job_id: str,
    control: JobControl,
    current_admin: Admin = Depends(admin_required)
):
    """控制指定任务"""
    if not scheduler_manager.is_running:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="调度器未运行"
        )
    
    success = False
    message = ""
    
    if control.action == "trigger":
        success = scheduler_manager.trigger_job(job_id)
        message = f"任务 {job_id} 已手动触发" if success else f"触发任务 {job_id} 失败"
    elif control.action == "pause":
        success = scheduler_manager.pause_job(job_id)
        message = f"任务 {job_id} 已暂停" if success else f"暂停任务 {job_id} 失败"
    elif control.action == "resume":
        success = scheduler_manager.resume_job(job_id)
        message = f"任务 {job_id} 已恢复" if success else f"恢复任务 {job_id} 失败"
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的操作类型，支持: trigger, pause, resume"
        )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=message
        )
    
    return {"message": message, "success": True}


@router.get("/scheduler/jobs")
def get_scheduler_jobs(
    current_admin: Admin = Depends(admin_required)
):
    """获取所有调度任务信息"""
    status_info = scheduler_manager.get_job_status()
    
    # 添加任务描述信息
    job_descriptions = {
        "scrape_holdings_data": {
            "description": "抓取增减持数据",
            "category": "数据抓取",
            "controllable": True
        },
        "cleanup_old_data": {
            "description": "清理过期数据",
            "category": "数据维护",
            "controllable": True
        },
        "update_impact_scores": {
            "description": "更新影响力评分",
            "category": "数据分析",
            "controllable": True
        },
        "health_check": {
            "description": "系统健康检查",
            "category": "系统监控",
            "controllable": True
        },
        "daily_notification": {
            "description": "每日增减持数据通知",
            "category": "通知服务",
            "controllable": True
        }
    }
    
    # 增强任务信息
    for job in status_info.get("jobs", []):
        job_id = job.get("id")
        if job_id in job_descriptions:
            job.update(job_descriptions[job_id])
    
    return status_info


@router.get("/config/all")
def get_all_configs(
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """获取所有系统配置（按分组）"""
    from app.services.admin_service import SystemConfigService
    
    config_service = SystemConfigService(db)
    all_configs = config_service.get_all_configs()
    
    # 按分组组织配置
    grouped_configs = {}
    for config in all_configs:
        group = config.config_group
        if group not in grouped_configs:
            grouped_configs[group] = []
        
        grouped_configs[group].append({
            "key": config.config_key,
            "value": config.config_value,
            "description": config.description,
            "type": config.config_type,
            "editable": config.is_editable,
            "created_at": config.created_at,
            "updated_at": config.updated_at
        })
    
    return grouped_configs
