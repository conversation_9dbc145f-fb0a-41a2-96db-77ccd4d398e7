"""
增减持相关API端点
"""
from typing import List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.dependencies import admin_required
from app.models.admin import Admin
from app.schemas.holdings import (
    HoldingChange, HoldingChangeCreate, HoldingChangeUpdate,
    HoldingChangeFilter, HoldingChangeResponse, ChangeDirection, HolderType
)
from app.services.holding_service import HoldingChangeService

router = APIRouter()


@router.post("/create", response_model=HoldingChange)
def create_holding_change(
    change_data: HoldingChangeCreate,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """创建增减持记录"""
    holding_service = HoldingChangeService(db)
    change = holding_service.create_holding_change(change_data)
    return change


@router.get("/list", response_model=HoldingChangeResponse)
def get_holding_changes(
    stock_code: str = Query(None, description="股票代码"),
    stock_name: str = Query(None, description="股票名称"),
    holder_name: str = Query(None, description="变动人姓名"),
    holder_type: HolderType = Query(None, description="持有人类型"),
    direction: ChangeDirection = Query(None, description="增减持方向"),
    change_reason: Optional[str] = Query(None, description="变动原因"),
    date_from: str = Query(None, description="开始日期 (YYYY-MM-DD)"),
    date_to: str = Query(None, description="结束日期 (YYYY-MM-DD)"),
    change_amount_min: float = Query(None, description="最小变动金额"),
    change_amount_max: float = Query(None, description="最大变动金额"),
    holding_ratio_min: float = Query(None, description="最小持股比例"),
    holding_ratio_max: float = Query(None, description="最大持股比例"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取增减持记录列表"""
    from datetime import datetime

    # 构建筛选条件
    filters = HoldingChangeFilter(
        stock_code=stock_code,
        stock_name=stock_name,
        holder_name=holder_name,
        holder_type=holder_type,
        direction=direction,
        change_reason=change_reason,
        date_from=datetime.strptime(date_from, "%Y-%m-%d").date() if date_from else None,
        date_to=datetime.strptime(date_to, "%Y-%m-%d").date() if date_to else None,
        change_amount_min=change_amount_min,
        change_amount_max=change_amount_max,
        holding_ratio_min=holding_ratio_min,
        holding_ratio_max=holding_ratio_max,
        page=page,
        page_size=page_size
    )
    
    holding_service = HoldingChangeService(db)
    result = holding_service.get_holding_changes(filters)
    
    return HoldingChangeResponse(**result)


@router.get("/{change_id}", response_model=HoldingChange)
def get_holding_change(
    change_id: int,
    db: Session = Depends(get_db)
):
    """根据ID获取增减持记录"""
    holding_service = HoldingChangeService(db)
    change = holding_service.get_holding_change_by_id(change_id)
    
    if not change:
        raise HTTPException(status_code=404, detail="增减持记录不存在")
    
    return change


@router.put("/{change_id}", response_model=HoldingChange)
def update_holding_change(
    change_id: int,
    change_data: HoldingChangeUpdate,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """更新增减持记录"""
    holding_service = HoldingChangeService(db)
    change = holding_service.update_holding_change(change_id, change_data)
    
    if not change:
        raise HTTPException(status_code=404, detail="增减持记录不存在")
    
    return change


@router.get("/stock/{stock_code}", response_model=List[HoldingChange])
def get_stock_holding_changes(
    stock_code: str,
    limit: int = Query(50, ge=1, le=200, description="返回记录数"),
    db: Session = Depends(get_db)
):
    """获取特定股票的增减持记录"""
    holding_service = HoldingChangeService(db)
    changes = holding_service.get_stock_holding_changes(stock_code, limit)
    return changes


@router.get("/recent/list", response_model=List[HoldingChange])
def get_recent_changes(
    days: int = Query(7, ge=1, le=365, description="天数"),
    limit: int = Query(100, ge=1, le=500, description="返回记录数"),
    db: Session = Depends(get_db)
):
    """获取最近的增减持记录"""
    holding_service = HoldingChangeService(db)
    changes = holding_service.get_recent_changes(days, limit)
    return changes


@router.get("/top/amount", response_model=List[HoldingChange])
def get_top_changes_by_amount(
    limit: int = Query(20, ge=1, le=100, description="返回记录数"),
    db: Session = Depends(get_db)
):
    """获取按变动金额排序的增减持记录"""
    holding_service = HoldingChangeService(db)
    changes = holding_service.get_top_changes_by_amount(limit)
    return changes


@router.get("/statistics/overview")
def get_holdings_statistics(db: Session = Depends(get_db)):
    """获取增减持统计信息"""
    holding_service = HoldingChangeService(db)
    stats = holding_service.get_statistics()
    return stats


@router.get("/chart/{stock_code}", response_model=List[HoldingChange])
def get_stock_chart_data(
    stock_code: str,
    days: int = Query(180, ge=30, le=365, description="查询天数，默认180天"),
    db: Session = Depends(get_db)
):
    """获取指定股票的增减持记录用于图表显示"""
    from datetime import datetime, timedelta

    holding_service = HoldingChangeService(db)

    # 计算日期范围
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days)

    # 获取该股票在指定时间范围内的所有增减持记录
    filters = HoldingChangeFilter(
        stock_code=stock_code,
        date_from=start_date,
        date_to=end_date,
        page=1,
        page_size=100  # 最大100条记录
    )

    result = holding_service.get_holding_changes(filters)
    return result.get('items', [])
