"""
股票相关API端点
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.dependencies import admin_required
from app.models.admin import Admin
from app.schemas.holdings import Stock, StockCreate, StockUpdate
from app.services.stock_service import StockService

router = APIRouter()


@router.get("/list", response_model=List[Stock])
def get_stocks(
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    db: Session = Depends(get_db)
):
    """获取股票列表"""
    stock_service = StockService(db)
    stocks = stock_service.get_all_stocks(skip=skip, limit=limit)
    return stocks


@router.get("/search", response_model=List[Stock])
def search_stocks(
    keyword: str = Query(..., description="搜索关键词（股票代码或名称）"),
    limit: int = Query(20, ge=1, le=100, description="返回记录数"),
    db: Session = Depends(get_db)
):
    """搜索股票"""
    stock_service = StockService(db)
    stocks = stock_service.search_stocks(keyword=keyword, limit=limit)
    return stocks


@router.get("/recent-changes/list", response_model=List[Stock])
def get_stocks_with_recent_changes(
    days: int = Query(30, ge=1, le=365, description="天数"),
    db: Session = Depends(get_db)
):
    """获取最近有增减持变动的股票"""
    stock_service = StockService(db)
    stocks = stock_service.get_stocks_with_recent_changes(days=days)
    return stocks


@router.get("/statistics/overview")
def get_stock_statistics(db: Session = Depends(get_db)):
    """获取股票统计信息"""
    stock_service = StockService(db)
    stats = stock_service.get_stock_statistics()
    return stats


@router.get("/detail/{stock_code}", response_model=Stock)
def get_stock_by_code(
    stock_code: str,
    db: Session = Depends(get_db)
):
    """根据股票代码获取股票信息"""
    stock_service = StockService(db)
    stock = stock_service.get_stock_by_code(stock_code)

    if not stock:
        raise HTTPException(status_code=404, detail="股票不存在")

    return stock


@router.post("/create", response_model=Stock)
def create_stock(
    stock_data: StockCreate,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """创建股票记录"""
    stock_service = StockService(db)
    
    # 检查是否已存在
    existing_stock = stock_service.get_stock_by_code(stock_data.code)
    if existing_stock:
        raise HTTPException(status_code=400, detail="股票代码已存在")
    
    stock = stock_service.create_stock(stock_data)
    return stock


@router.put("/{stock_id}", response_model=Stock)
def update_stock(
    stock_id: int,
    stock_data: StockUpdate,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """更新股票信息"""
    stock_service = StockService(db)
    stock = stock_service.update_stock(stock_id, stock_data)
    
    if not stock:
        raise HTTPException(status_code=404, detail="股票不存在")
    
    return stock


