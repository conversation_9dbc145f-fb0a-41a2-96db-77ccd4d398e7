"""
通知相关API接口
"""
from datetime import date, datetime, timedelta
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.orm import Session

from app.core.dependencies import get_db, admin_required
from app.models.admin import Admin
from app.schemas.notifications import (
    NotificationType,
    NotificationPriority,
    NotificationContentBase,
    NotificationRequest,
    BatchNotificationRequest,
    NotificationResponse,
    NotificationStats,
    EmailNotificationRequest,
    WechatNotificationRequest,
    FeishuNotificationRequest
)
from app.services.notifications.notification_manager import notification_manager
from app.services.simple_notification_config_service import SimpleNotificationConfigService
from app.schemas.notification_config import (
    NotificationConfigCreate,
    NotificationConfigUpdate,
    NotificationConfigResponse,
    NotificationConfigListResponse,
    NotificationConfigTest,
    NotificationConfigTestResponse,
    NotificationConfigBatchOperation,
    NotificationConfigBatchResponse
)
from app.models.notification_config import NotificationServiceType

router = APIRouter()


@router.post("/send", response_model=NotificationResponse)
async def send_notification(
    request: NotificationRequest,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """发送单个通知"""
    try:
        result = await notification_manager.send_notification(
            notification_type=request.notification_type,
            content=request.content,
            config=request.config,
            priority=request.priority
        )
        
        return NotificationResponse(
            success=result.success,
            message=result.message,
            data=result.data
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"发送通知失败: {str(e)}")


@router.post("/send/batch", response_model=Dict[str, NotificationResponse])
async def send_batch_notification(
    request: BatchNotificationRequest,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """批量发送通知"""
    try:
        results = await notification_manager.send_batch_notification(request)
        
        # 转换结果格式
        response = {}
        for notification_type, result in results.items():
            response[notification_type.value] = NotificationResponse(
                success=result.success,
                message=result.message,
                data=result.data
            )
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量发送通知失败: {str(e)}")


@router.post("/send/email", response_model=NotificationResponse)
async def send_email_notification(
    request: EmailNotificationRequest,
    current_admin: Admin = Depends(admin_required)
):
    """发送邮件通知"""
    try:
        result = await notification_manager.send_notification(
            notification_type=NotificationType.EMAIL,
            content=request,
            config=request.config.dict(),
            priority=NotificationPriority.NORMAL
        )
        
        return NotificationResponse(
            success=result.success,
            message=result.message,
            data=result.data
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"发送邮件通知失败: {str(e)}")


@router.post("/send/wechat", response_model=NotificationResponse)
async def send_wechat_notification(
    request: WechatNotificationRequest,
    current_admin: Admin = Depends(admin_required)
):
    """发送微信通知"""
    try:
        result = await notification_manager.send_notification(
            notification_type=NotificationType.WECHAT,
            content=request,
            config=request.config.dict(),
            priority=NotificationPriority.NORMAL
        )
        
        return NotificationResponse(
            success=result.success,
            message=result.message,
            data=result.data
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"发送微信通知失败: {str(e)}")


@router.post("/send/feishu", response_model=NotificationResponse)
async def send_feishu_notification(
    request: FeishuNotificationRequest,
    current_admin: Admin = Depends(admin_required)
):
    """发送飞书通知"""
    try:
        result = await notification_manager.send_notification(
            notification_type=NotificationType.FEISHU,
            content=request,
            config=request.config.dict(),
            priority=NotificationPriority.NORMAL
        )
        
        return NotificationResponse(
            success=result.success,
            message=result.message,
            data=result.data
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"发送飞书通知失败: {str(e)}")


@router.post("/send/daily-holdings", response_model=Dict[str, NotificationResponse])
async def send_daily_holdings_notification(
    target_date: Optional[date] = Body(None, description="目标日期，默认为昨天"),
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """发送每日增减持数据通知"""
    try:
        if target_date is None:
            target_date = date.today() - timedelta(days=1)
        
        results = await notification_manager.send_daily_holding_changes_notification(
            db=db,
            target_date=target_date
        )
        
        # 转换结果格式
        response = {}
        for notification_type, result in results.items():
            response[notification_type.value] = NotificationResponse(
                success=result.success,
                message=result.message,
                data=result.data
            )
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"发送每日增减持通知失败: {str(e)}")


@router.get("/services/status")
def get_services_status(
    current_admin: Admin = Depends(admin_required)
):
    """获取通知服务状态"""
    return notification_manager.get_service_status()


@router.get("/services/enabled")
def get_enabled_services(
    current_admin: Admin = Depends(admin_required)
):
    """获取已启用的通知服务"""
    enabled_services = notification_manager.get_enabled_services()
    return {
        "enabled_services": [service.value for service in enabled_services],
        "count": len(enabled_services)
    }


@router.post("/test", response_model=Dict[str, NotificationResponse])
async def test_notification_services(
    current_admin: Admin = Depends(admin_required)
):
    """测试所有通知服务"""
    try:
        results = await notification_manager.test_all_services()
        
        # 转换结果格式
        response = {}
        for notification_type, result in results.items():
            response[notification_type.value] = NotificationResponse(
                success=result.success,
                message=result.message,
                data=result.data
            )
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试通知服务失败: {str(e)}")


@router.post("/test/{service_type}", response_model=NotificationResponse)
async def test_single_notification_service(
    service_type: NotificationType,
    current_admin: Admin = Depends(admin_required)
):
    """测试单个通知服务"""
    try:
        test_content = NotificationContentBase(
            title=f"🧪 {service_type.value} 通知服务测试",
            content="这是一条测试消息，用于验证通知服务是否正常工作。",
            summary="通知服务连通性测试"
        )
        
        # 获取测试配置
        test_config = notification_manager._get_test_config(service_type)
        
        result = await notification_manager.send_notification(
            notification_type=service_type,
            content=test_content,
            config=test_config,
            priority=NotificationPriority.LOW
        )
        
        return NotificationResponse(
            success=result.success,
            message=result.message,
            data=result.data
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试 {service_type.value} 服务失败: {str(e)}")


@router.get("/templates/holding-changes")
def get_holding_changes_templates(
    current_admin: Admin = Depends(admin_required)
):
    """获取增减持数据通知模板"""
    templates = {
        "email": {
            "subject": "📊 {date} 增减持数据报告",
            "description": "HTML格式的邮件模板，包含数据统计和重要变动表格"
        },
        "wechat": {
            "format": "markdown",
            "description": "Markdown格式的微信消息，支持富文本显示"
        },
        "feishu": {
            "format": "rich_text",
            "description": "飞书富文本格式，支持颜色和样式"
        }
    }
    
    return {
        "templates": templates,
        "sample_data": {
            "date": "2025-06-30",
            "total_count": 25,
            "increase_count": 15,
            "decrease_count": 10,
            "summary": "今日增持活动较为活跃，科技股表现突出"
        }
    }


@router.get("/history/stats")
def get_notification_stats(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    current_admin: Admin = Depends(admin_required)
):
    """获取通知统计信息"""
    # 这里应该从数据库获取实际的统计数据
    # 目前返回模拟数据
    return {
        "period_days": days,
        "total_notifications": 150,
        "success_count": 142,
        "failed_count": 8,
        "success_rate": 94.67,
        "by_service": {
            "email": {"sent": 50, "success": 48, "failed": 2},
            "wechat": {"sent": 50, "success": 47, "failed": 3},
            "feishu": {"sent": 50, "success": 47, "failed": 3}
        },
        "last_sent_time": datetime.now().isoformat()
    }


@router.post("/config/update")
async def update_notification_config(
    config_updates: Dict[str, Any] = Body(..., description="配置更新"),
    current_admin: Admin = Depends(admin_required)
):
    """更新通知配置"""
    try:
        # 这里应该实现配置更新逻辑
        # 目前返回成功响应
        return {
            "success": True,
            "message": "通知配置更新成功",
            "updated_keys": list(config_updates.keys())
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新通知配置失败: {str(e)}")


@router.get("/config/current")
def get_current_notification_config(
    current_admin: Admin = Depends(admin_required)
):
    """获取当前通知配置"""
    from app.core.config import settings
    
    return {
        "email": {
            "enabled": settings.ENABLE_EMAIL_NOTIFICATION,
            "smtp_host": settings.SMTP_HOST,
            "smtp_port": settings.SMTP_PORT,
            "from_name": settings.SMTP_FROM_NAME,
            "has_credentials": bool(settings.SMTP_USERNAME and settings.SMTP_PASSWORD)
        },
        "wechat": {
            "enabled": settings.ENABLE_WECHAT_NOTIFICATION,
            "has_webhook": bool(settings.WECHAT_WEBHOOK_URL)
        },
        "feishu": {
            "enabled": settings.ENABLE_FEISHU_NOTIFICATION,
            "has_webhook": bool(settings.FEISHU_WEBHOOK_URL)
        },
        "schedule": {
            "daily_time": settings.DAILY_NOTIFICATION_TIME,
            "timezone": settings.NOTIFICATION_TIMEZONE
        }
    }


# ==================== 通知配置管理接口 ====================

@router.post("/configs", response_model=NotificationConfigResponse)
async def create_notification_config(
    config_data: NotificationConfigCreate,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """创建通知配置"""
    try:
        config_service = NotificationConfigService(db)

        # 构建配置数据
        create_data = config_data.dict(exclude={'config_data'})
        create_data.update(config_data.config_data.dict())

        config = config_service.create_config(create_data)
        return config.to_dict(include_sensitive=False)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建通知配置失败: {str(e)}")


@router.get("/configs", response_model=NotificationConfigListResponse)
async def get_notification_configs(
    service_type: Optional[NotificationServiceType] = Query(None, description="服务类型筛选"),
    is_enabled: Optional[bool] = Query(None, description="启用状态筛选"),
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """获取通知配置列表"""
    try:
        config_service = NotificationConfigService(db)
        configs = config_service.get_configs(
            service_type=service_type,
            is_enabled=is_enabled,
            include_sensitive=False
        )

        return {
            "total": len(configs),
            "items": [config.to_dict(include_sensitive=False) for config in configs]
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取通知配置失败: {str(e)}")


@router.get("/configs/{config_id}", response_model=NotificationConfigResponse)
async def get_notification_config(
    config_id: int,
    include_sensitive: bool = Query(False, description="是否包含敏感信息"),
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """获取单个通知配置"""
    try:
        config_service = NotificationConfigService(db)
        config = config_service.get_config(config_id, include_sensitive=include_sensitive)

        if not config:
            raise HTTPException(status_code=404, detail="通知配置不存在")

        return config.to_dict(include_sensitive=include_sensitive)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取通知配置失败: {str(e)}")


@router.put("/configs/{config_id}", response_model=NotificationConfigResponse)
async def update_notification_config(
    config_id: int,
    config_data: NotificationConfigUpdate,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """更新通知配置"""
    try:
        config_service = NotificationConfigService(db)

        # 构建更新数据
        update_data = config_data.dict(exclude_unset=True, exclude={'config_data'})
        if config_data.config_data:
            update_data.update(config_data.config_data.dict(exclude_unset=True))

        config = config_service.update_config(config_id, update_data)

        if not config:
            raise HTTPException(status_code=404, detail="通知配置不存在")

        return config.to_dict(include_sensitive=False)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新通知配置失败: {str(e)}")


@router.delete("/configs/{config_id}")
async def delete_notification_config(
    config_id: int,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """删除通知配置"""
    try:
        config_service = NotificationConfigService(db)
        success = config_service.delete_config(config_id)

        if not success:
            raise HTTPException(status_code=404, detail="通知配置不存在")

        return {"message": "通知配置删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除通知配置失败: {str(e)}")


@router.post("/configs/{config_id}/test", response_model=NotificationConfigTestResponse)
async def test_notification_config(
    config_id: int,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """测试通知配置"""
    try:
        config_service = NotificationConfigService(db)
        result = config_service.test_config(config_id)

        return NotificationConfigTestResponse(**result)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试通知配置失败: {str(e)}")


@router.post("/configs/batch", response_model=NotificationConfigBatchResponse)
async def batch_operation_notification_configs(
    operation_data: NotificationConfigBatchOperation,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """批量操作通知配置"""
    try:
        config_service = NotificationConfigService(db)
        success_count = 0
        failed_count = 0
        failed_items = []

        for config_id in operation_data.config_ids:
            try:
                if operation_data.operation == "enable":
                    config_service.update_config(config_id, {"is_enabled": True})
                elif operation_data.operation == "disable":
                    config_service.update_config(config_id, {"is_enabled": False})
                elif operation_data.operation == "delete":
                    config_service.delete_config(config_id)

                success_count += 1

            except Exception as e:
                failed_count += 1
                failed_items.append({
                    "config_id": config_id,
                    "error": str(e)
                })

        return NotificationConfigBatchResponse(
            success_count=success_count,
            failed_count=failed_count,
            failed_items=failed_items
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量操作失败: {str(e)}")


@router.get("/configs/service-types/status")
async def get_service_types_status(
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db)
):
    """获取各服务类型的配置状态"""
    try:
        config_service = NotificationConfigService(db)
        status = {}

        for service_type in NotificationServiceType:
            configs = config_service.get_configs(service_type=service_type)
            enabled_configs = [c for c in configs if c.is_enabled]
            default_config = next((c for c in configs if c.is_default), None)

            status[service_type.value] = {
                "total_configs": len(configs),
                "enabled_configs": len(enabled_configs),
                "has_default": default_config is not None,
                "default_config_id": default_config.id if default_config else None
            }

        return status

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取服务状态失败: {str(e)}")
