"""
股价数据API端点
"""
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.stock_price_service import StockPriceService
from app.services.holding_service import HoldingChangeService

router = APIRouter()


@router.get("/{stock_code}/history")
async def get_stock_price_history(
    stock_code: str,
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    period: str = Query("daily", description="数据周期 (daily, weekly, monthly)")
):
    """获取股票历史价格数据"""
    try:
        price_service = StockPriceService()
        result = price_service.get_stock_price_data(
            stock_code=stock_code,
            start_date=start_date,
            end_date=end_date,
            period=period
        )
        
        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["message"])
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取股价历史数据失败: {str(e)}")


@router.get("/{stock_code}/with-holdings")
async def get_stock_price_with_holdings(
    stock_code: str,
    days_before: int = Query(30, description="事件前天数"),
    days_after: int = Query(30, description="事件后天数"),
    db: Session = Depends(get_db)
):
    """获取股票价格数据并标注增减持事件"""
    try:
        # 获取该股票的增减持记录
        holding_service = HoldingChangeService(db)
        holding_changes = holding_service.get_stock_holding_changes(stock_code, limit=100)
        
        # 转换为字典格式
        changes_data = []
        for change in holding_changes:
            changes_data.append({
                "announcement_date": change.announcement_date,
                "change_date": change.change_date,
                "holder_name": change.holder_name,
                "holder_type": change.holder_type.value if change.holder_type else "",
                "direction": change.direction.value if change.direction else "",
                "change_shares": change.change_shares,
                "change_amount": change.change_amount,
                "change_reason": change.change_reason,
                "holding_ratio_after": change.holding_ratio_after
            })
        
        # 获取带增减持标注的价格数据
        price_service = StockPriceService()
        result = price_service.get_stock_price_with_holdings(
            stock_code=stock_code,
            holding_changes=changes_data,
            days_before=days_before,
            days_after=days_after
        )
        
        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["message"])
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取股价和增减持数据失败: {str(e)}")


@router.get("/{stock_code}/realtime")
async def get_stock_realtime_price(stock_code: str):
    """获取股票实时价格"""
    try:
        price_service = StockPriceService()
        result = price_service.get_realtime_price(stock_code)
        
        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["message"])
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取实时价格失败: {str(e)}")


@router.get("/{stock_code}/info")
async def get_stock_basic_info(stock_code: str):
    """获取股票基本信息"""
    try:
        price_service = StockPriceService()
        result = price_service.get_stock_basic_info(stock_code)
        
        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["message"])
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取股票基本信息失败: {str(e)}")


@router.get("/{stock_code}/chart-data")
async def get_stock_chart_data(
    stock_code: str,
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    include_holdings: bool = Query(True, description="是否包含增减持标注"),
    db: Session = Depends(get_db)
):
    """获取用于图表展示的股价数据"""
    try:
        price_service = StockPriceService()
        
        if include_holdings:
            # 获取增减持记录
            holding_service = HoldingChangeService(db)
            holding_changes = holding_service.get_stock_holding_changes(stock_code, limit=100)
            
            # 转换为字典格式
            changes_data = []
            for change in holding_changes:
                # 安全地获取枚举值
                holder_type_value = ""
                if change.holder_type:
                    if hasattr(change.holder_type, 'value'):
                        holder_type_value = change.holder_type.value
                    else:
                        holder_type_value = str(change.holder_type)

                direction_value = ""
                if change.direction:
                    if hasattr(change.direction, 'value'):
                        direction_value = change.direction.value
                    else:
                        direction_value = str(change.direction)

                changes_data.append({
                    "announcement_date": change.announcement_date,
                    "change_date": change.change_date,
                    "holder_name": change.holder_name,
                    "holder_type": holder_type_value,
                    "direction": direction_value,
                    "change_shares": change.change_shares,
                    "change_amount": change.change_amount,
                    "change_reason": change.change_reason,
                    "holding_ratio_after": change.holding_ratio_after
                })
            
            # 获取带增减持标注的价格数据
            result = price_service.get_stock_price_with_holdings(
                stock_code=stock_code,
                holding_changes=changes_data,
                days_before=60,
                days_after=30
            )
        else:
            # 只获取价格数据
            result = price_service.get_stock_price_data(
                stock_code=stock_code,
                start_date=start_date,
                end_date=end_date
            )
        
        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["message"])
        
        # 转换为图表友好的格式
        chart_data = {
            "success": True,
            "stock_code": stock_code,
            "categories": [],  # 日期数组
            "series": {
                "price": [],  # 价格数据 [开盘, 收盘, 最低, 最高]
                "volume": [],  # 成交量
                "holdings": []  # 增减持事件标注
            },
            "holding_events": []
        }
        
        for item in result["data"]:
            chart_data["categories"].append(item["date"])
            chart_data["series"]["price"].append([
                item["open"],
                item["close"],
                item["low"],
                item["high"],
                item.get("volume", 0),  # 成交量
                item.get("amount", 0),  # 成交额
                item.get("change_pct", 0),  # 涨跌幅
                item.get("change_amount", 0),  # 涨跌额
                item.get("turnover_rate", 0)  # 换手率
            ])
            chart_data["series"]["volume"].append(item.get("volume", 0))
            
            # 处理增减持事件
            if item.get("holding_changes"):
                for event in item["holding_changes"]:
                    chart_data["holding_events"].append({
                        "date": item["date"],
                        "price": item["close"],
                        "event": event
                    })
                chart_data["series"]["holdings"].append(len(item["holding_changes"]))
            else:
                chart_data["series"]["holdings"].append(0)
        
        return chart_data
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图表数据失败: {str(e)}")


@router.get("/market/overview")
async def get_market_overview():
    """获取市场概览数据"""
    try:
        import akshare as ak
        
        # 获取A股市场概览
        df = ak.stock_zh_a_spot_em()
        
        if df is None or df.empty:
            raise HTTPException(status_code=404, detail="未获取到市场数据")
        
        # 计算市场统计
        total_stocks = len(df)
        rising_stocks = len(df[df['涨跌幅'] > 0])
        falling_stocks = len(df[df['涨跌幅'] < 0])
        unchanged_stocks = len(df[df['涨跌幅'] == 0])
        
        # 获取涨跌幅前10
        top_gainers = df.nlargest(10, '涨跌幅')[['代码', '名称', '最新价', '涨跌幅', '涨跌额']].to_dict('records')
        top_losers = df.nsmallest(10, '涨跌幅')[['代码', '名称', '最新价', '涨跌幅', '涨跌额']].to_dict('records')
        
        return {
            "success": True,
            "data": {
                "market_stats": {
                    "total_stocks": total_stocks,
                    "rising_stocks": rising_stocks,
                    "falling_stocks": falling_stocks,
                    "unchanged_stocks": unchanged_stocks,
                    "rising_ratio": round(rising_stocks / total_stocks * 100, 2),
                    "falling_ratio": round(falling_stocks / total_stocks * 100, 2)
                },
                "top_gainers": top_gainers,
                "top_losers": top_losers
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取市场概览失败: {str(e)}")
