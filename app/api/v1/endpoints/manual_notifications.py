"""
手动通知推送API
"""
from typing import Optional
from datetime import datetime, date, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from loguru import logger

from app.core.database import get_db
from app.core.dependencies import get_current_admin
from app.models.admin import Admin
from app.models.holdings import HoldingChange, Stock
from app.services.notifications.notification_manager import notification_manager
from app.schemas.notifications import NotificationContentBase, NotificationType, NotificationPriority

router = APIRouter()


@router.post("/send-holdings")
async def send_holdings_notification(
    days: int = Query(default=1, description="获取最近几天的数据", ge=1, le=30),
    limit: int = Query(default=20, description="最大记录数", ge=1, le=100),
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """手动发送增减持数据通知"""
    try:
        # 计算日期范围
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        # 获取增减持数据
        holdings = db.query(HoldingChange).join(Stock).filter(
            HoldingChange.announcement_date >= start_date,
            HoldingChange.announcement_date <= end_date
        ).order_by(HoldingChange.announcement_date.desc()).limit(limit).all()
        
        if not holdings:
            return {
                "success": False,
                "message": f"未找到最近{days}天的增减持数据",
                "data": {
                    "date_range": f"{start_date} 至 {end_date}",
                    "total_count": 0
                }
            }
        
        logger.info(f"找到 {len(holdings)} 条增减持数据，准备发送通知")
        
        # 构造增减持数据
        holdings_data = []
        for holding in holdings:
            stock = holding.stock
            direction_text = "增持" if holding.direction == 'increase' else "减持"
            direction_emoji = "📈" if holding.direction == 'increase' else "📉"
            
            holdings_data.append({
                'stock_code': stock.code,
                'stock_name': stock.name,
                'holder_name': holding.holder_name,
                'direction': holding.direction,
                'direction_text': direction_text,
                'direction_emoji': direction_emoji,
                'change_shares': holding.change_shares,
                'change_amount': holding.change_amount,
                'announcement_date': holding.announcement_date.strftime('%Y-%m-%d'),
                'holding_ratio_after': holding.holding_ratio_after
            })
        
        # 构造文本内容
        text_content = f"📈 股票增减持信息推送\n\n"
        text_content += f"📊 数据概览：\n"
        text_content += f"• 时间范围：{start_date} 至 {end_date}\n"
        text_content += f"• 总记录数：{len(holdings_data)} 条\n"
        text_content += f"• 涉及股票：{len(set(h['stock_name'] for h in holdings_data))} 只\n"
        text_content += f"• 推送时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        text_content += f"📋 详细信息：\n"
        display_count = min(10, len(holdings_data))  # 最多显示10条
        for i, holding in enumerate(holdings_data[:display_count], 1):
            text_content += f"{i}. {holding['direction_emoji']} {holding['stock_name']}({holding['stock_code']})\n"
            text_content += f"   持有人：{holding['holder_name']}\n"
            text_content += f"   变动：{holding['direction_text']} {holding['change_shares']:,.0f}股\n"

            # 变动比例单独一行
            if holding['holding_ratio_after'] is not None:
                text_content += f"   变动比例：{holding['holding_ratio_after']:.2f}%\n"

            text_content += f"   金额：{holding['change_amount']:,.0f}万元\n"
            text_content += f"   日期：{holding['announcement_date']}\n\n"
        
        if len(holdings_data) > display_count:
            text_content += f"... 还有 {len(holdings_data) - display_count} 条记录\n"
        
        # 构造通知内容
        content = NotificationContentBase(
            title="📈 股票增减持信息推送",
            content=text_content,
            summary=f"最近{days}天共{len(holdings_data)}条增减持变动，涉及{len(set(h['stock_name'] for h in holdings_data))}只股票"
        )
        
        # 发送通知
        result = await notification_manager.send_notification(
            notification_type=NotificationType.FEISHU,
            content=content,
            config={},
            priority=NotificationPriority.NORMAL
        )
        
        if result.success:
            return {
                "success": True,
                "message": "增减持数据推送成功",
                "data": {
                    "date_range": f"{start_date} 至 {end_date}",
                    "total_count": len(holdings_data),
                    "stocks_count": len(set(h['stock_name'] for h in holdings_data)),
                    "sent_time": datetime.now().isoformat()
                }
            }
        else:
            return {
                "success": False,
                "message": f"推送失败: {result.message}",
                "data": {
                    "date_range": f"{start_date} 至 {end_date}",
                    "total_count": len(holdings_data),
                    "error": result.message
                }
            }
            
    except Exception as e:
        logger.error(f"发送增减持通知失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"发送通知失败: {str(e)}"
        )


@router.get("/holdings-preview")
async def preview_holdings_data(
    days: int = Query(default=1, description="获取最近几天的数据", ge=1, le=30),
    limit: int = Query(default=20, description="最大记录数", ge=1, le=100),
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """预览增减持数据（不发送通知）"""
    try:
        # 计算日期范围
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        # 获取增减持数据
        holdings = db.query(HoldingChange).join(Stock).filter(
            HoldingChange.announcement_date >= start_date,
            HoldingChange.announcement_date <= end_date
        ).order_by(HoldingChange.announcement_date.desc()).limit(limit).all()
        
        # 构造数据
        holdings_data = []
        for holding in holdings:
            stock = holding.stock
            holdings_data.append({
                'stock_code': stock.code,
                'stock_name': stock.name,
                'holder_name': holding.holder_name,
                'direction': holding.direction,
                'change_shares': holding.change_shares,
                'change_amount': holding.change_amount,
                'announcement_date': holding.announcement_date.strftime('%Y-%m-%d'),
                'holding_ratio_after': holding.holding_ratio_after
            })
        
        return {
            "success": True,
            "data": {
                "date_range": f"{start_date} 至 {end_date}",
                "total_count": len(holdings_data),
                "stocks_count": len(set(h['stock_name'] for h in holdings_data)),
                "holdings": holdings_data
            }
        }
        
    except Exception as e:
        logger.error(f"预览增减持数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取数据失败: {str(e)}"
        )
