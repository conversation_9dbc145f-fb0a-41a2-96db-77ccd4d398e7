"""
FastAPI依赖项
"""
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import verify_token, AuthenticationError, PermissionError
from app.models.admin import Admin
from app.services.admin_service import AdminService

# HTTP Bearer认证方案
security = HTTPBearer()


def get_current_admin(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> Admin:
    """获取当前登录的管理员"""
    token = credentials.credentials
    payload = verify_token(token)
    
    if payload is None:
        raise AuthenticationError("无效的认证令牌")
    
    # 检查令牌类型
    if payload.get("type") != "admin":
        raise AuthenticationError("无效的令牌类型")
    
    admin_id = payload.get("admin_id")
    if admin_id is None:
        raise AuthenticationError("令牌中缺少管理员ID")
    
    # 获取管理员信息
    admin_service = AdminService(db)
    admin = admin_service.get_admin_by_id(admin_id)
    
    if admin is None:
        raise AuthenticationError("管理员不存在")
    
    if not admin.is_active:
        raise AuthenticationError("管理员账户已被禁用")
    
    return admin


def get_current_active_admin(
    current_admin: Admin = Depends(get_current_admin)
) -> Admin:
    """获取当前活跃的管理员"""
    if not current_admin.is_active:
        raise AuthenticationError("管理员账户已被禁用")
    return current_admin


def get_current_superuser(
    current_admin: Admin = Depends(get_current_active_admin)
) -> Admin:
    """获取当前超级管理员"""
    if not current_admin.is_superuser:
        raise PermissionError("需要超级管理员权限")
    return current_admin


def admin_required(
    current_admin: Admin = Depends(get_current_active_admin)
) -> Admin:
    """管理员权限验证装饰器"""
    return current_admin


def superuser_required(
    current_admin: Admin = Depends(get_current_superuser)
) -> Admin:
    """超级管理员权限验证装饰器"""
    return current_admin


# 可选的管理员认证（不强制要求登录）
def get_optional_admin(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[Admin]:
    """获取可选的当前管理员（不强制要求登录）"""
    if credentials is None:
        return None
    
    try:
        return get_current_admin(credentials, db)
    except (AuthenticationError, PermissionError):
        return None
