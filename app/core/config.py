"""
应用配置模块
"""
import os
from typing import List
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    APP_NAME: str = "增减持数据分析平台"
    VERSION: str = "1.0.0"
    DEBUG: bool = True
    
    # API配置
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    API_PREFIX: str = "/api/v1"
    
    # 数据库配置
    DATABASE_URL: str = "postgresql://username:password@localhost:5432/stock_analysis"
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    CORS_ORIGINS: str = "http://localhost:3000,http://localhost:5173"

    # 管理员配置
    ADMIN_USERNAME: str = "admin"
    ADMIN_PASSWORD: str = "admin123"

    # JWT配置
    JWT_SECRET_KEY: str = "your-jwt-secret-key-change-this-in-production"
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 1440

    @property
    def cors_origins_list(self) -> List[str]:
        """将CORS_ORIGINS字符串转换为列表"""
        return [origin.strip() for origin in self.CORS_ORIGINS.split(",")]
    
    # 爬虫配置
    SCRAPING_INTERVAL_HOURS: float = 0.5  # 30分钟执行一次
    USER_AGENT: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

    # 数据源配置（敏感信息，不对外暴露）
    DATA_SOURCE_BASE_URL: str = "https://data.10jqka.com.cn"
    DATA_SOURCE_HOLDINGS_URL: str = "https://data.10jqka.com.cn/financial/ggjy/"

    # 数据源优先级配置
    PRIMARY_DATA_SOURCE: str = "tonghuashun"  # 主要数据源：同花顺
    FALLBACK_DATA_SOURCE: str = "akshare"     # 备用数据源：AkShare
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"

    # 通知配置
    # 邮件通知配置
    SMTP_HOST: str = "smtp.qq.com"
    SMTP_PORT: int = 465  # 使用SSL端口
    SMTP_USERNAME: str = ""
    SMTP_PASSWORD: str = ""
    SMTP_FROM_EMAIL: str = ""
    SMTP_FROM_NAME: str = "增减持数据分析平台"

    # 企业微信机器人配置
    WECHAT_WEBHOOK_URL: str = ""

    # 飞书机器人配置
    FEISHU_WEBHOOK_URL: str = ""
    FEISHU_SECRET_KEY: str = ""  # 飞书机器人签名密钥

    # 通知加密配置
    NOTIFICATION_ENCRYPTION_KEY: str = ""  # 通知配置加密密钥

    # 通知功能开关
    ENABLE_EMAIL_NOTIFICATION: bool = True  # 默认启用邮件通知
    ENABLE_WECHAT_NOTIFICATION: bool = False
    ENABLE_FEISHU_NOTIFICATION: bool = True  # 默认启用飞书通知

    # 通知时间配置
    DAILY_NOTIFICATION_TIME: str = "09:00"  # 每日通知时间
    NOTIFICATION_TIMEZONE: str = "Asia/Shanghai"

    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()
