"""
定时任务调度器
"""
import asyncio
from datetime import datetime
from typing import Optional
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from loguru import logger

from app.core.config import settings
from app.core.database import SessionLocal
from app.services.scraper import TongHuaShunScraper
from app.services.holding_service import HoldingChangeService
from app.services.system_config_service import SchedulerConfigManager
from app.services.notifications.notification_manager import notification_manager


class SchedulerManager:
    """定时任务管理器"""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.scraper = TongHuaShunScraper()
        self.is_running = False
    
    def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已经在运行中")
            return
        
        try:
            # 添加定时任务
            self._add_jobs()
            
            # 启动调度器
            self.scheduler.start()
            self.is_running = True
            
            logger.info("定时任务调度器启动成功")
            
        except Exception as e:
            logger.error(f"启动调度器失败: {e}")
            raise
    
    def stop(self):
        """停止调度器"""
        if not self.is_running:
            return
        
        try:
            self.scheduler.shutdown()
            self.is_running = False
            logger.info("定时任务调度器已停止")
            
        except Exception as e:
            logger.error(f"停止调度器失败: {e}")
    
    def _add_jobs(self):
        """添加定时任务"""

        # 获取配置管理器
        db = SessionLocal()
        try:
            config_manager = SchedulerConfigManager(db)
            scraping_config = config_manager.get_scraping_config()

            # 1. 数据抓取任务 - 根据配置设置间隔
            if scraping_config["enabled"]:
                self.scheduler.add_job(
                    func=self._scrape_holdings_data,
                    trigger=IntervalTrigger(minutes=scraping_config["interval_minutes"]),
                    id="scrape_holdings_data",
                    name="抓取增减持数据",
                    max_instances=1,  # 防止重复执行
                    replace_existing=True
                )
        finally:
            db.close()

        # 获取其他配置
        db = SessionLocal()
        try:
            config_manager = SchedulerConfigManager(db)
            cleanup_config = config_manager.get_cleanup_config()
            analysis_config = config_manager.get_analysis_config()
            system_config = config_manager.get_system_config()

            # 2. 数据清理任务 - 每天凌晨2点执行
            if cleanup_config["enabled"]:
                self.scheduler.add_job(
                    func=self._cleanup_old_data,
                    trigger=CronTrigger(hour=2, minute=0),
                    id="cleanup_old_data",
                    name="清理过期数据",
                    max_instances=1,
                    replace_existing=True
                )

            # 3. 影响力评分更新任务 - 每天凌晨3点执行
            if analysis_config["impact_score_update_enabled"]:
                self.scheduler.add_job(
                    func=self._update_impact_scores,
                    trigger=CronTrigger(hour=3, minute=0),
                    id="update_impact_scores",
                    name="更新影响力评分",
                    max_instances=1,
                    replace_existing=True
                )

            # 4. 健康检查任务 - 每小时执行一次
            if system_config["health_check_enabled"]:
                self.scheduler.add_job(
                    func=self._health_check,
                    trigger=IntervalTrigger(hours=1),
                    id="health_check",
                    name="系统健康检查",
                    max_instances=1,
                    replace_existing=True
                )

            # 5. 每日通知任务 - 根据配置时间发送
            notification_config = config_manager.get_notification_config()
            if notification_config["enabled"]:
                # 解析通知时间
                time_parts = settings.DAILY_NOTIFICATION_TIME.split(":")
                hour = int(time_parts[0])
                minute = int(time_parts[1]) if len(time_parts) > 1 else 0

                self.scheduler.add_job(
                    func=self._send_daily_notification,
                    trigger=CronTrigger(hour=hour, minute=minute),
                    id="daily_notification",
                    name="每日增减持数据通知",
                    max_instances=1,
                    replace_existing=True
                )
        finally:
            db.close()
        
        logger.info("定时任务添加完成")

    async def _send_daily_notification(self):
        """发送每日邮件通知任务（近7天增持数据）"""
        try:
            logger.info("开始执行每日邮件通知任务")

            # 获取通知配置
            db = SessionLocal()
            try:
                config_manager = SchedulerConfigManager(db)
                notification_config = config_manager.get_notification_config()

                # 检查是否启用邮件通知
                if not notification_config["enabled"] or not notification_config.get("email_enabled", False):
                    logger.info("每日邮件通知已禁用，跳过任务")
                    return

                # 只发送邮件通知（近7天增持数据）
                result = await notification_manager.send_weekly_increase_email_notification(db)

                if result and result.success:
                    logger.info(f"每日邮件通知任务完成: 发送成功")
                elif result:
                    logger.error(f"每日邮件通知发送失败: {result.message}")
                else:
                    logger.info("无邮件数据需要通知")

            finally:
                db.close()

        except Exception as e:
            logger.error(f"每日邮件通知任务执行失败: {str(e)}")
            # 不抛出异常，避免影响调度器
    
    async def _scrape_holdings_data(self):
        """抓取增减持数据任务"""
        try:
            logger.info("开始执行定时数据抓取任务")

            # 获取抓取配置
            db = SessionLocal()
            try:
                config_manager = SchedulerConfigManager(db)
                scraping_config = config_manager.get_scraping_config()

                # 检查是否启用抓取
                if not scraping_config["enabled"]:
                    logger.info("数据抓取已禁用，跳过任务")
                    return

                # 抓取数据
                scraped_data = await self.scraper.scrape_holdings_data(
                    page=1,
                    max_pages=scraping_config["max_pages"]
                )
            finally:
                db.close()

            if not scraped_data:
                logger.warning("未抓取到新数据")
                return

            # 保存到数据库
            db = SessionLocal()
            try:
                holding_service = HoldingChangeService(db)
                created_changes = holding_service.batch_create_holding_changes(scraped_data)

                logger.info(f"定时任务成功保存 {len(created_changes)} 条新增减持记录")

                # 如果有新增持数据，发送飞书通知
                if created_changes:
                    await self._send_new_increase_holdings_notification(db, created_changes)

            finally:
                db.close()

        except Exception as e:
            logger.error(f"定时数据抓取任务失败: {e}")

    async def _send_new_increase_holdings_notification(self, db, created_changes):
        """发送新增持数据通知（只发送增持，不发送减持）"""
        try:
            from datetime import datetime
            from app.schemas.notifications import NotificationContentBase, NotificationType, NotificationPriority

            # 检查飞书通知是否启用
            config_manager = SchedulerConfigManager(db)
            notification_config = config_manager.get_notification_config()

            if not notification_config.get("feishu_enabled", False):
                logger.info("飞书通知未启用，跳过推送")
                return

            # 只筛选增持数据
            increase_changes = [change for change in created_changes if change.direction == 'increase']

            if not increase_changes:
                logger.info("没有新增持数据，跳过飞书推送")
                return

            # 构造通知数据
            holdings_data = []
            for change in increase_changes:
                stock = change.stock
                holdings_data.append({
                    'stock_code': stock.code,
                    'stock_name': stock.name,
                    'holder_name': change.holder_name,
                    'direction': change.direction,
                    'change_shares': change.change_shares,
                    'change_amount': change.change_amount,
                    'announcement_date': change.announcement_date.strftime('%Y-%m-%d'),
                    'holding_ratio_after': change.holding_ratio_after,
                    'price_avg': change.price_avg,
                    'change_reason': change.change_reason
                })

            # 构造消息内容
            text_content = f"📈 检测到新的增持数据！\n\n"
            text_content += f"📊 数据概览：\n"
            text_content += f"• 新增持记录：{len(holdings_data)} 条\n"
            text_content += f"• 涉及股票：{len(set(h['stock_name'] for h in holdings_data))} 只\n"
            text_content += f"• 检测时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

            text_content += f"📋 详细信息：\n"
            display_count = min(8, len(holdings_data))  # 最多显示8条
            for i, holding in enumerate(holdings_data[:display_count], 1):
                text_content += f"{i}. 📈 {holding['stock_name']}({holding['stock_code']})\n"
                text_content += f"   持有人：{holding['holder_name']}\n"
                text_content += f"   增持：{holding['change_shares']:,.0f}股\n"

                # 变动比例单独一行
                if holding['holding_ratio_after'] is not None:
                    text_content += f"   变动比例：{holding['holding_ratio_after']:.2f}%\n"

                # 变动原因
                if holding['change_reason']:
                    text_content += f"   变动原因：{holding['change_reason']}\n"

                # 金额和均价
                if holding['change_amount']:
                    text_content += f"   金额：{holding['change_amount']:,.0f}万元\n"
                if holding['price_avg'] and holding['price_avg'] > 0:
                    text_content += f"   均价：{holding['price_avg']:.2f}元\n"

                text_content += f"   日期：{holding['announcement_date']}\n\n"

            if len(holdings_data) > display_count:
                text_content += f"... 还有 {len(holdings_data) - display_count} 条增持记录\n"

            # 构造通知内容
            content = NotificationContentBase(
                title="📈 新增持数据推送",
                content=text_content,
                summary=f"检测到{len(holdings_data)}条新增持变动，涉及{len(set(h['stock_name'] for h in holdings_data))}只股票"
            )

            # 发送飞书通知
            result = await notification_manager.send_notification(
                notification_type=NotificationType.FEISHU,
                content=content,
                config={},
                priority=NotificationPriority.HIGH  # 使用高优先级
            )

            if result.success:
                logger.info(f"新增持数据通知发送成功: {len(holdings_data)}条记录")
            else:
                logger.error(f"新增持数据通知发送失败: {result.message}")

        except Exception as e:
            logger.error(f"发送新增持数据通知失败: {e}")

    async def _cleanup_old_data(self):
        """清理过期数据任务"""
        try:
            logger.info("开始执行数据清理任务")
            
            db = SessionLocal()
            try:
                from datetime import date, timedelta
                from app.models.holdings import HoldingChange
                
                # 删除2年前的数据
                cutoff_date = date.today() - timedelta(days=730)
                
                deleted_count = db.query(HoldingChange).filter(
                    HoldingChange.announcement_date < cutoff_date
                ).delete()
                
                db.commit()
                
                logger.info(f"数据清理任务完成，删除了 {deleted_count} 条过期记录")
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"数据清理任务失败: {e}")
    
    async def _update_impact_scores(self):
        """更新影响力评分任务"""
        try:
            logger.info("开始执行影响力评分更新任务")
            
            db = SessionLocal()
            try:
                from datetime import date, timedelta
                from app.models.holdings import HoldingChange
                
                # 更新最近30天的记录评分
                recent_date = date.today() - timedelta(days=30)
                
                holding_service = HoldingChangeService(db)
                
                # 获取需要更新的记录
                records = db.query(HoldingChange).filter(
                    HoldingChange.announcement_date >= recent_date,
                    HoldingChange.impact_score == 0.0
                ).limit(100).all()  # 每次最多处理100条
                
                updated_count = 0
                for record in records:
                    try:
                        holding_service._calculate_impact_score(record)
                        updated_count += 1
                    except Exception as e:
                        logger.warning(f"更新记录 {record.id} 评分失败: {e}")
                        continue
                
                logger.info(f"影响力评分更新任务完成，更新了 {updated_count} 条记录")
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"影响力评分更新任务失败: {e}")
    
    async def _health_check(self):
        """系统健康检查任务"""
        try:
            logger.debug("执行系统健康检查")
            
            # 检查数据库连接
            db = SessionLocal()
            try:
                from app.models.holdings import Stock
                stock_count = db.query(Stock).count()
                logger.debug(f"数据库连接正常，当前股票数量: {stock_count}")
                
            finally:
                db.close()
            
            # 检查日志文件大小
            import os
            if os.path.exists(settings.LOG_FILE):
                file_size = os.path.getsize(settings.LOG_FILE) / (1024 * 1024)  # MB
                if file_size > 50:  # 超过50MB警告
                    logger.warning(f"日志文件过大: {file_size:.2f}MB")
            
        except Exception as e:
            logger.error(f"系统健康检查失败: {e}")
    
    def get_job_status(self) -> dict:
        """获取任务状态"""
        if not self.is_running:
            return {"status": "stopped", "jobs": []}
        
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        
        return {
            "status": "running",
            "jobs": jobs
        }
    
    def trigger_job(self, job_id: str) -> bool:
        """手动触发任务"""
        try:
            job = self.scheduler.get_job(job_id)
            if job:
                job.modify(next_run_time=datetime.now())
                logger.info(f"手动触发任务: {job_id}")
                return True
            else:
                logger.warning(f"任务不存在: {job_id}")
                return False

        except Exception as e:
            logger.error(f"触发任务失败 {job_id}: {e}")
            return False

    def reload_jobs(self) -> bool:
        """重新加载任务配置"""
        try:
            # 移除所有现有任务
            for job in self.scheduler.get_jobs():
                self.scheduler.remove_job(job.id)

            # 重新添加任务
            self._add_jobs()

            logger.info("任务配置重新加载成功")
            return True

        except Exception as e:
            logger.error(f"重新加载任务配置失败: {e}")
            return False

    def pause_job(self, job_id: str) -> bool:
        """暂停任务"""
        try:
            self.scheduler.pause_job(job_id)
            logger.info(f"任务已暂停: {job_id}")
            return True
        except Exception as e:
            logger.error(f"暂停任务失败 {job_id}: {e}")
            return False

    def resume_job(self, job_id: str) -> bool:
        """恢复任务"""
        try:
            self.scheduler.resume_job(job_id)
            logger.info(f"任务已恢复: {job_id}")
            return True
        except Exception as e:
            logger.error(f"恢复任务失败 {job_id}: {e}")
            return False


# 全局调度器实例
scheduler_manager = SchedulerManager()
