#!/usr/bin/env python3
"""
测试发送增减持数据到邮箱
"""
import asyncio
from datetime import datetime, date, timedelta
from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.holdings import HoldingChange, Stock
from app.services.notifications.notification_manager import notification_manager
from app.schemas.notifications import NotificationContentBase, NotificationType, NotificationPriority


async def test_send_holdings_to_email():
    """测试发送增减持数据到邮箱"""
    db = SessionLocal()
    
    try:
        print("📧 开始测试邮件通知功能...")
        
        # 获取最近的增减持数据
        end_date = date.today()
        start_date = end_date - timedelta(days=7)  # 获取最近7天的数据
        
        holdings = db.query(HoldingChange).join(Stock).filter(
            HoldingChange.announcement_date >= start_date,
            HoldingChange.announcement_date <= end_date
        ).order_by(HoldingChange.announcement_date.desc()).limit(20).all()
        
        if not holdings:
            print("⚠️ 没有找到增减持数据，将发送测试消息")
            
            # 发送简单测试消息
            content = NotificationContentBase(
                title="📧 邮件通知测试",
                content="这是一条邮件通知测试消息，用于验证邮件服务是否正常工作。",
                summary="邮件通知功能测试"
            )
            
            # 测试配置
            test_config = {
                'to_emails': ['<EMAIL>'],  # 请替换为实际的测试邮箱
                'subject': '📧 邮件通知测试 - 增减持数据分析平台'
            }
            
        else:
            print(f"📊 找到 {len(holdings)} 条增减持数据")
            
            # 处理数据
            holdings_data = []
            increase_count = 0
            decrease_count = 0
            
            for holding in holdings:
                direction = 'increase' if holding.change_shares and holding.change_shares > 0 else 'decrease'
                if direction == 'increase':
                    increase_count += 1
                else:
                    decrease_count += 1
                    
                holdings_data.append({
                    'stock_name': holding.stock.name if holding.stock else '未知',
                    'stock_code': holding.stock.code if holding.stock else '未知',
                    'holder_name': holding.holder_name or '未知',
                    'direction': direction,
                    'change_shares': holding.change_shares or 0,
                    'change_amount': holding.change_amount or 0,
                    'change_ratio': holding.holding_ratio_after or 0,
                    'announcement_date': holding.announcement_date.strftime('%Y-%m-%d') if holding.announcement_date else '未知'
                })
            
            # 构造邮件内容
            text_content = f"📈 股票增减持信息推送\n\n"
            text_content += f"📊 数据概览：\n"
            text_content += f"• 时间范围：{start_date} 至 {end_date}\n"
            text_content += f"• 总记录数：{len(holdings_data)} 条\n"
            text_content += f"• 增持记录：{increase_count} 条\n"
            text_content += f"• 减持记录：{decrease_count} 条\n"
            text_content += f"• 涉及股票：{len(set(h['stock_name'] for h in holdings_data))} 只\n"
            text_content += f"• 推送时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            
            text_content += "📋 重要变动（前5条）：\n"
            for i, holding in enumerate(holdings_data[:5], 1):
                direction_text = "🔺增持" if holding['direction'] == 'increase' else "🔻减持"
                text_content += f"{i}. {holding['stock_name']}({holding['stock_code']}) - {holding['holder_name']}\n"
                text_content += f"   {direction_text} {holding['change_shares']:.2f}万股，变动比例：{holding['change_ratio']:.2f}%\n"
                text_content += f"   公告日期：{holding['announcement_date']}\n\n"
            
            if len(holdings_data) > 5:
                text_content += f"... 还有 {len(holdings_data) - 5} 条记录\n"
            
            # 构造通知内容（包含data字段，用于生成HTML邮件）
            content = NotificationContentBase(
                title="📈 股票增减持信息推送",
                content=text_content,
                summary=f"最近{(end_date - start_date).days}天共{len(holdings_data)}条增减持变动，涉及{len(set(h['stock_name'] for h in holdings_data))}只股票",
                data={
                    'date': f"{start_date} 至 {end_date}",
                    'total_count': len(holdings_data),
                    'increase_count': increase_count,
                    'decrease_count': decrease_count,
                    'top_changes': holdings_data,
                    'summary': f"最近{(end_date - start_date).days}天增减持活动概览"
                }
            )
            
            # 邮件配置
            test_config = {
                'to_emails': ['<EMAIL>'],  # 请替换为实际的测试邮箱
                'subject': f'📊 {start_date} 至 {end_date} 增减持数据报告'
            }
        
        print("📧 开始发送到邮箱...")
        print(f"📮 收件人: {test_config['to_emails']}")
        print(f"📝 主题: {test_config['subject']}")
        
        # 发送到邮箱
        result = await notification_manager.send_notification(
            notification_type=NotificationType.EMAIL,
            content=content,
            config=test_config,
            priority=NotificationPriority.NORMAL
        )
        
        if result.success:
            print("✅ 邮件发送成功！")
            print(f"📝 发送结果: {result.message}")
            if result.data:
                print(f"📊 发送详情: {result.data}")
        else:
            print("❌ 邮件发送失败！")
            print(f"📝 错误信息: {result.message}")
            
    except Exception as e:
        print(f"❌ 发送过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 启动邮件通知测试...")
    print("⚠️ 请确保已在 .env 文件中配置了邮件相关环境变量：")
    print("   - ENABLE_EMAIL_NOTIFICATION=true")
    print("   - SMTP_HOST=smtp.qq.com")
    print("   - SMTP_PORT=587")
    print("   - SMTP_USERNAME=<EMAIL>")
    print("   - SMTP_PASSWORD=your_app_password")
    print("   - SMTP_FROM_EMAIL=<EMAIL>")
    print("   - SMTP_FROM_NAME=增减持数据分析平台")
    print()
    print("⚠️ 请修改脚本中的 <EMAIL> 为实际的测试邮箱地址")
    print()
    
    asyncio.run(test_send_holdings_to_email())
