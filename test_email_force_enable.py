#!/usr/bin/env python3
"""
强制启用邮件通知并测试
"""
import asyncio
from datetime import datetime
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 强制设置环境变量
os.environ['ENABLE_EMAIL_NOTIFICATION'] = 'true'
os.environ['SMTP_USERNAME'] = '<EMAIL>'
os.environ['SMTP_PASSWORD'] = 'bftshuinrvbsffdc'
os.environ['SMTP_FROM_EMAIL'] = '<EMAIL>'

from app.services.notifications.email_service import EmailNotificationService
from app.schemas.notifications import NotificationContentBase, NotificationPriority


async def test_email_service_directly():
    """直接测试邮件服务"""
    try:
        print("📧 开始直接测试邮件服务...")
        
        # 直接创建邮件服务（使用465端口SSL）
        email_config = {
            'enabled': True,
            'smtp_host': 'smtp.qq.com',
            'smtp_port': 465,  # 使用SSL端口
            'smtp_username': '<EMAIL>',
            'smtp_password': 'bftshuinrvbsffdc',
            'from_email': '<EMAIL>',
            'from_name': '增减持数据分析平台'
        }
        
        email_service = EmailNotificationService(email_config)
        
        # 构造测试消息内容
        content = NotificationContentBase(
            title="🧪 直接邮件服务测试",
            content=f"""这是一条直接测试邮件服务的消息

📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🔧 服务类型: EMAIL (直接调用)
✅ 配置状态: 强制启用
🎯 测试目的: 验证邮件服务连通性

如果您收到这条邮件，说明邮件服务工作正常！

---
此邮件由增减持数据分析平台自动发送
""",
            summary="直接邮件服务测试"
        )
        
        # 邮件配置
        test_config = {
            'to_emails': ['<EMAIL>'],  # 发送给自己
            'subject': '🧪 直接邮件服务测试 - 增减持数据分析平台'
        }
        
        print("📧 开始发送测试邮件...")
        print(f"📮 收件人: {test_config['to_emails']}")
        print(f"📝 主题: {test_config['subject']}")
        print(f"🔧 SMTP配置: {email_config['smtp_host']}:{email_config['smtp_port']}")
        print(f"👤 用户名: {email_config['smtp_username']}")
        
        # 发送邮件
        result = await email_service.send_notification(
            content=content,
            config=test_config,
            priority=NotificationPriority.NORMAL
        )
        
        if result.success:
            print("✅ 邮件发送成功！")
            print(f"📝 发送结果: {result.message}")
            if result.data:
                print(f"📊 发送详情: {result.data}")
        else:
            print("❌ 邮件发送失败！")
            print(f"📝 错误信息: {result.message}")
            
    except Exception as e:
        print(f"❌ 发送过程中出现异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 启动直接邮件服务测试...")
    print("📧 使用强制配置:")
    print("   - 收件人: <EMAIL>")
    print("   - 发件人: <EMAIL>")
    print("   - SMTP服务器: smtp.qq.com:465 (SSL)")
    print("   - 用户名: <EMAIL>")
    print()
    
    asyncio.run(test_email_service_directly())
