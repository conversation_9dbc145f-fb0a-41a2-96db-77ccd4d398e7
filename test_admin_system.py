#!/usr/bin/env python3
"""
管理员系统功能测试脚本
"""
import sys
import os
import asyncio
import requests
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings

# API基础URL
BASE_URL = f"http://localhost:{settings.API_PORT}/api/v1"

class AdminSystemTester:
    def __init__(self):
        self.session = requests.Session()
        self.admin_token = None
        self.test_results = []

    def log_test(self, test_name, success, message=""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.now()
        })
        print(f"{status} {test_name}: {message}")

    def test_admin_login(self):
        """测试管理员登录"""
        try:
            response = self.session.post(f"{BASE_URL}/admin/login", json={
                "username": "admin",
                "password": "admin123"
            })
            
            if response.status_code == 200:
                data = response.json()
                self.admin_token = data.get("access_token")
                self.session.headers.update({
                    "Authorization": f"Bearer {self.admin_token}"
                })
                self.log_test("管理员登录", True, "登录成功")
                return True
            else:
                self.log_test("管理员登录", False, f"状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("管理员登录", False, str(e))
            return False

    def test_get_current_admin(self):
        """测试获取当前管理员信息"""
        try:
            response = self.session.get(f"{BASE_URL}/admin/me")
            
            if response.status_code == 200:
                data = response.json()
                self.log_test("获取管理员信息", True, f"用户名: {data.get('username')}")
                return True
            else:
                self.log_test("获取管理员信息", False, f"状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("获取管理员信息", False, str(e))
            return False

    def test_system_config(self):
        """测试系统配置管理"""
        try:
            # 获取调度器配置
            response = self.session.get(f"{BASE_URL}/system/config/scheduler")
            
            if response.status_code == 200:
                config = response.json()
                self.log_test("获取系统配置", True, f"抓取间隔: {config.get('scraping_interval_minutes')}分钟")
                
                # 测试更新配置
                update_response = self.session.put(f"{BASE_URL}/system/config/scheduler", json={
                    "scraping_interval_minutes": 35
                })
                
                if update_response.status_code == 200:
                    self.log_test("更新系统配置", True, "配置更新成功")
                    return True
                else:
                    self.log_test("更新系统配置", False, f"状态码: {update_response.status_code}")
                    return False
            else:
                self.log_test("获取系统配置", False, f"状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("系统配置管理", False, str(e))
            return False

    def test_scheduler_status(self):
        """测试调度器状态"""
        try:
            response = self.session.get(f"{BASE_URL}/system/scheduler/status")
            
            if response.status_code == 200:
                status = response.json()
                self.log_test("调度器状态", True, f"状态: {status.get('status')}, 任务数: {len(status.get('jobs', []))}")
                return True
            else:
                self.log_test("调度器状态", False, f"状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("调度器状态", False, str(e))
            return False

    def test_data_management(self):
        """测试数据管理功能"""
        try:
            # 获取数据统计
            response = self.session.get(f"{BASE_URL}/data/stats")
            
            if response.status_code == 200:
                stats = response.json()
                self.log_test("数据统计", True, f"股票数: {stats.get('total_stocks')}, 记录数: {stats.get('total_holdings')}")
                
                # 检查重复记录
                dup_response = self.session.get(f"{BASE_URL}/data/duplicates/check")
                
                if dup_response.status_code == 200:
                    dup_data = dup_response.json()
                    self.log_test("重复记录检查", True, f"重复组数: {dup_data.get('total_duplicate_groups')}")
                    return True
                else:
                    self.log_test("重复记录检查", False, f"状态码: {dup_response.status_code}")
                    return False
            else:
                self.log_test("数据统计", False, f"状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("数据管理", False, str(e))
            return False

    def test_scraping_permissions(self):
        """测试抓取权限控制"""
        try:
            # 测试需要管理员权限的抓取端点
            response = self.session.post(f"{BASE_URL}/scraping/test", params={"max_pages": 1})
            
            if response.status_code == 200:
                self.log_test("抓取权限控制", True, "管理员可以访问抓取功能")
                return True
            else:
                self.log_test("抓取权限控制", False, f"状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("抓取权限控制", False, str(e))
            return False

    def test_without_auth(self):
        """测试未认证访问"""
        try:
            # 临时移除认证头
            old_headers = self.session.headers.copy()
            if "Authorization" in self.session.headers:
                del self.session.headers["Authorization"]
            
            response = self.session.get(f"{BASE_URL}/admin/me")
            
            # 恢复认证头
            self.session.headers.update(old_headers)
            
            if response.status_code in [401, 403]:
                self.log_test("未认证访问控制", True, f"正确拒绝未认证访问 (状态码: {response.status_code})")
                return True
            else:
                self.log_test("未认证访问控制", False, f"应该返回401或403，实际: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("未认证访问控制", False, str(e))
            return False

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始管理员系统功能测试...")
        print("=" * 50)
        
        # 测试顺序很重要，登录必须在最前面
        tests = [
            self.test_admin_login,
            self.test_get_current_admin,
            self.test_system_config,
            self.test_scheduler_status,
            self.test_data_management,
            self.test_scraping_permissions,
            self.test_without_auth,
        ]
        
        for test in tests:
            test()
            print("-" * 30)
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print("=" * 50)
        print("📊 测试结果统计:")
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['message']}")
        
        return failed_tests == 0


def main():
    """主函数"""
    print("管理员系统功能测试")
    print("确保后端服务正在运行...")
    
    tester = AdminSystemTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 所有测试通过！管理员系统功能正常。")
        sys.exit(0)
    else:
        print("\n💥 部分测试失败，请检查系统配置。")
        sys.exit(1)


if __name__ == "__main__":
    main()
