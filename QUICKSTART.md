# 🚀 快速开始指南

## 环境要求

- Python 3.8+
- PostgreSQL 12+
- Node.js 16+ (前端)

## 后端快速启动

### 1. 自动化设置（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd stock/

# 一键设置开发环境
./setup.sh

# 配置数据库连接
# 编辑 .env 文件，设置 DATABASE_URL

# 启动开发服务器
./dev.sh
```

### 2. 手动设置

```bash
# 1. 创建虚拟环境
python -m venv venv

# 2. 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 3. 安装依赖
pip install --upgrade pip
pip install -r requirements.txt

# 4. 配置环境
cp .env.example .env
# 编辑 .env 文件

# 5. 数据库迁移
alembic upgrade head

# 6. 启动服务
python run.py
```

## 前端快速启动

```bash
cd ../frontend/

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## Docker部署

```bash
# 一键部署
./deploy.sh

# 或手动部署
docker-compose up -d
```

## 验证安装

### 后端验证

访问以下地址确认后端正常运行：

- API服务: http://localhost:8000
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

### 前端验证

访问以下地址确认前端正常运行：

- 前端应用: http://localhost:3000

## 常见问题

### 1. 虚拟环境问题

```bash
# 如果虚拟环境损坏，重新创建
rm -rf venv
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### 2. 数据库连接问题

```bash
# 检查PostgreSQL是否运行
sudo systemctl status postgresql  # Linux
brew services list | grep postgres  # Mac

# 检查数据库连接配置
cat .env | grep DATABASE_URL
```

### 3. 端口占用问题

```bash
# 检查端口占用
lsof -i :8000  # 后端端口
lsof -i :3000  # 前端端口

# 杀死占用进程
kill -9 <PID>
```

### 4. 依赖安装问题

```bash
# 清理pip缓存
pip cache purge

# 重新安装依赖
pip install --no-cache-dir -r requirements.txt
```

## 开发工作流

### 1. 日常开发

```bash
# 激活虚拟环境
source venv/bin/activate

# 启动开发服务器
./dev.sh

# 在另一个终端启动前端
cd ../frontend && npm run dev
```

### 2. 添加新功能

```bash
# 1. 创建新分支
git checkout -b feature/new-feature

# 2. 开发代码
# ...

# 3. 运行测试
pytest

# 4. 提交代码
git add .
git commit -m "Add new feature"
git push origin feature/new-feature
```

### 3. 数据库变更

```bash
# 1. 修改模型
# 编辑 app/models/ 下的文件

# 2. 生成迁移文件
alembic revision --autogenerate -m "Add new table"

# 3. 执行迁移
alembic upgrade head
```

### 4. 更新依赖

```bash
# 1. 安装新依赖
pip install new-package

# 2. 更新requirements.txt
./update_requirements.sh

# 3. 提交更改
git add requirements.txt
git commit -m "Add new dependency"
```

## 生产部署

### 1. 环境准备

```bash
# 1. 服务器环境
sudo apt update
sudo apt install python3 python3-venv postgresql nginx

# 2. 克隆代码
git clone <repository-url>
cd stock/
```

### 2. 配置生产环境

```bash
# 1. 创建生产环境配置
cp .env.example .env.production

# 2. 编辑生产配置
# 设置 DEBUG=False
# 配置生产数据库
# 设置安全密钥

# 3. 使用生产配置
export ENV_FILE=.env.production
```

### 3. 部署应用

```bash
# 使用Docker部署
./deploy.sh

# 或手动部署
./setup.sh
./dev.sh
```

## 监控和维护

### 1. 日志查看

```bash
# 应用日志
tail -f logs/app.log

# Docker日志
docker-compose logs -f backend
```

### 2. 数据库备份

```bash
# 备份数据库
pg_dump stock_analysis > backup.sql

# 恢复数据库
psql stock_analysis < backup.sql
```

### 3. 性能监控

```bash
# 检查系统资源
htop
df -h
free -h

# 检查应用状态
curl http://localhost:8000/health
```

## 获取帮助

- 查看API文档: http://localhost:8000/docs
- 查看项目README: README.md
- 提交Issue: GitHub Issues
- 联系开发团队: [联系方式]
