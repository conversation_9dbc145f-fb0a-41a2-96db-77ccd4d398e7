#!/usr/bin/env python3
"""
测试简化的爬虫功能
"""
import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.scraper import TongHuaShunScraper

async def test_simple_scraper():
    """测试简化的爬虫功能"""
    scraper = TongHuaShunScraper()
    
    print("开始测试简化的爬虫功能...")
    
    # 测试抓取第一页
    data = await scraper.scrape_holdings_data()
    
    print(f"总共抓取到 {len(data)} 条数据")
    
    # 分析数据重复情况
    unique_records = set()
    for item in data:
        key = f"{item['stock_code']}-{item['holder_name']}-{item['announcement_date']}-{item['direction']}-{item['change_shares']}-{item['change_reason']}"
        unique_records.add(key)
    
    print(f"唯一记录数: {len(unique_records)}")
    print(f"重复记录数: {len(data) - len(unique_records)}")
    
    if len(unique_records) < len(data):
        print("❌ 存在重复数据")
    else:
        print("✅ 没有重复数据")
    
    # 显示前几条数据
    print("\n前10条数据:")
    for i, item in enumerate(data[:10]):
        print(f"{i+1}. {item['stock_code']} {item['stock_name']} - {item['holder_name']} - {item['announcement_date']} - {item['direction']} - {item['change_shares']}")
    
    # 分析数据质量
    print(f"\n数据质量分析:")
    print(f"- 股票代码数量: {len(set(item['stock_code'] for item in data))}")
    print(f"- 持有人数量: {len(set(item['holder_name'] for item in data))}")
    print(f"- 公告日期范围: {min(item['announcement_date'] for item in data)} 到 {max(item['announcement_date'] for item in data)}")
    
    # 统计增减持方向
    direction_stats = {}
    for item in data:
        direction = item['direction']
        direction_stats[direction] = direction_stats.get(direction, 0) + 1
    
    print(f"- 增减持方向统计: {direction_stats}")

if __name__ == "__main__":
    asyncio.run(test_simple_scraper())
