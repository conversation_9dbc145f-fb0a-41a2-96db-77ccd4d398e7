# 通知配置管理系统使用指南

## 🎯 功能概述

通知配置管理系统为您的股票增减持平台提供了完整的通知配置管理功能，支持：

- 📧 **邮件通知配置**: SMTP服务器设置，支持HTML格式邮件
- 💬 **企业微信通知配置**: 机器人Webhook配置，支持@用户
- 🚀 **飞书通知配置**: 机器人Webhook配置，支持富文本消息
- 🔐 **密钥安全管理**: 自动加密存储，编辑时遮掩显示
- 🎛️ **可视化管理**: 友好的Web界面，支持增删改查
- ⚡ **动态加载**: 配置修改后自动生效，无需重启服务

## 🚀 快速开始

### 1. 初始化数据库表

```bash
# 运行初始化脚本
python init_notification_config.py
```

### 2. 访问配置页面

在浏览器中访问：`http://your-domain/notifications`

### 3. 配置通知服务

根据需要配置邮件、微信或飞书通知服务。

## 📋 详细配置说明

### 邮件通知配置

| 字段 | 说明 | 示例 |
|------|------|------|
| SMTP服务器 | 邮件服务器地址 | smtp.qq.com |
| SMTP端口 | 服务器端口 | 587 |
| SMTP用户名 | 登录用户名 | <EMAIL> |
| SMTP密码 | 登录密码/应用密码 | **************** |
| 发件人邮箱 | 发送邮件的地址 | <EMAIL> |
| 发件人名称 | 显示的发件人名称 | 增减持数据分析平台 |

### 企业微信通知配置

| 字段 | 说明 | 示例 |
|------|------|------|
| Webhook URL | 机器人Webhook地址 | https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=*** |
| @用户列表 | 默认@的用户列表 | ["user1", "user2"] |
| @手机号列表 | 默认@的手机号列表 | ["13800138000"] |

### 飞书通知配置

| 字段 | 说明 | 示例 |
|------|------|------|
| Webhook URL | 机器人Webhook地址 | https://open.feishu.cn/open-apis/bot/v2/hook/*** |
| @所有人 | 是否默认@所有人 | false |
| @用户ID列表 | 默认@的用户ID列表 | ["ou_xxx", "ou_yyy"] |

## 🔐 安全特性

### 密钥加密存储

- 所有敏感信息（密码、Webhook URL）都会自动加密存储
- 使用AES加密算法，确保数据安全
- 加密密钥通过环境变量管理

### 遮掩显示

- 列表页面：敏感信息自动遮掩显示
- 编辑页面：可选择显示/隐藏敏感信息
- 新增页面：正常输入，保存后自动加密

### 环境变量配置

```bash
# 设置加密密钥（可选，系统会自动生成）
export NOTIFICATION_ENCRYPTION_KEY="your-encryption-key"
```

## 🎛️ 页面功能说明

### 配置列表页面

- **查看配置**: 显示所有通知配置，敏感信息自动遮掩
- **状态管理**: 显示配置状态（启用/禁用/默认）
- **快速操作**: 测试、编辑、删除配置
- **密钥查看**: 点击眼睛图标可临时显示遮掩的密钥

### 配置编辑页面

- **基础信息**: 配置名称、描述、状态设置
- **服务配置**: 根据服务类型显示相应的配置字段
- **默认配置**: 每种服务类型只能有一个默认配置
- **实时验证**: 表单验证，确保配置完整性

### 配置测试功能

- **连接测试**: 验证配置是否正确
- **实时反馈**: 显示测试结果和错误信息
- **安全测试**: 不会发送实际通知，仅验证连接

## 🔄 API接口说明

### 配置管理接口

```bash
# 获取配置列表
GET /api/v1/notifications/configs

# 创建配置
POST /api/v1/notifications/configs

# 更新配置
PUT /api/v1/notifications/configs/{id}

# 删除配置
DELETE /api/v1/notifications/configs/{id}

# 测试配置
POST /api/v1/notifications/configs/{id}/test
```

### 通知发送接口

```bash
# 发送单个通知
POST /api/v1/notifications/send

# 批量发送通知
POST /api/v1/notifications/send/batch

# 发送每日增减持通知
POST /api/v1/notifications/send/daily-holdings
```

## 🔧 高级配置

### 定时任务集成

系统会自动使用数据库中的配置发送每日通知：

1. 每日定时任务执行时，自动加载最新配置
2. 使用启用状态的配置发送通知
3. 优先使用默认配置，如无默认配置则使用第一个启用的配置

### 配置优先级

1. **数据库配置**: 优先使用数据库中的配置
2. **环境变量配置**: 数据库配置不可用时的备选方案
3. **默认配置**: 系统内置的基础配置

### 批量操作

支持批量启用、禁用、删除配置：

```bash
POST /api/v1/notifications/configs/batch
{
  "config_ids": [1, 2, 3],
  "operation": "enable"  // enable, disable, delete
}
```

## 🐛 故障排除

### 常见问题

1. **邮件发送失败**
   - 检查SMTP配置是否正确
   - 确认使用应用专用密码而非登录密码
   - 验证网络连接和防火墙设置

2. **微信通知失败**
   - 确认Webhook URL是否正确
   - 检查机器人是否被移除群聊
   - 验证消息格式是否符合要求

3. **飞书通知失败**
   - 确认Webhook URL是否有效
   - 检查机器人权限设置
   - 验证消息内容是否超出限制

4. **配置无法保存**
   - 检查必填字段是否完整
   - 确认邮箱格式是否正确
   - 验证端口号是否在有效范围内

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log | grep notification

# 查看通知相关日志
grep "notification\|NotificationManager" logs/app.log
```

## 📈 最佳实践

1. **配置管理**
   - 为每种服务类型设置一个默认配置
   - 定期测试配置确保服务正常
   - 备份重要的配置信息

2. **安全管理**
   - 定期更换密钥和Webhook URL
   - 限制配置管理页面的访问权限
   - 监控通知发送日志

3. **性能优化**
   - 合理设置通知频率
   - 避免在高峰期发送大量通知
   - 监控通知服务的响应时间

4. **内容优化**
   - 根据接收者需求调整通知格式
   - 保持通知内容简洁明了
   - 定期收集用户反馈优化内容

## 🔮 未来扩展

系统设计支持轻松扩展新的通知渠道：

1. 继承 `BaseNotificationService` 类
2. 实现相应的发送和验证方法
3. 在数据库模型中添加新的字段
4. 在前端页面中添加新的配置表单

示例扩展钉钉通知：

```python
class DingTalkNotificationService(BaseNotificationService):
    async def send_notification(self, content, config, priority):
        # 实现钉钉通知发送逻辑
        pass
    
    def validate_config(self, config):
        # 实现配置验证逻辑
        pass
```

---

🎉 **恭喜！** 您已经成功配置了完整的通知管理系统。现在可以享受自动化的增减持数据通知服务了！
