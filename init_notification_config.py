#!/usr/bin/env python3
"""
初始化通知配置表
"""
import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.models.notification_config import NotificationConfig, NotificationServiceType
from app.services.notification_config_service import NotificationConfigService


def init_notification_config_table():
    """初始化通知配置表"""
    try:
        # 创建数据库连接
        engine = create_engine(settings.DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        # 执行SQL文件创建表
        with open('create_notification_config_table.sql', 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        with engine.connect() as connection:
            # 分割SQL语句并执行
            statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            for statement in statements:
                if statement:
                    connection.execute(text(statement))
            connection.commit()
        
        print("✅ 通知配置表创建成功")
        
        # 创建默认配置
        db = SessionLocal()
        try:
            config_service = NotificationConfigService(db)
            
            # 检查是否已存在配置
            existing_configs = config_service.get_configs()
            if existing_configs:
                print("⚠️  通知配置已存在，跳过默认配置创建")
                return
            
            # 创建默认邮件配置
            email_config = {
                'name': '默认邮件配置',
                'service_type': 'EMAIL',
                'is_enabled': False,
                'is_default': True,
                'description': '系统默认邮件通知配置，请根据实际情况修改',
                'smtp_host': 'smtp.qq.com',
                'smtp_port': 587,
                'smtp_username': '',
                'smtp_password': '',
                'from_email': '',
                'from_name': '增减持数据分析平台'
            }

            # 创建默认微信配置
            wechat_config = {
                'name': '默认微信配置',
                'service_type': 'WECHAT',
                'is_enabled': False,
                'is_default': True,
                'description': '系统默认企业微信通知配置，请配置Webhook URL',
                'wechat_webhook_url': '',
                'wechat_mentioned_list': [],
                'wechat_mentioned_mobile_list': []
            }

            # 创建默认飞书配置
            feishu_config = {
                'name': '默认飞书配置',
                'service_type': 'FEISHU',
                'is_enabled': False,
                'is_default': True,
                'description': '系统默认飞书通知配置，请配置Webhook URL',
                'feishu_webhook_url': '',
                'feishu_at_all': False,
                'feishu_at_users': []
            }
            
            # 创建配置
            configs = [email_config, wechat_config, feishu_config]
            for config_data in configs:
                try:
                    config_service.create_config(config_data)
                    print(f"✅ 创建默认配置: {config_data['name']}")
                except Exception as e:
                    print(f"❌ 创建配置失败 {config_data['name']}: {e}")
            
            print("✅ 默认通知配置创建完成")
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 初始化通知配置表失败: {e}")
        raise


def show_usage():
    """显示使用说明"""
    print("""
📧 通知配置管理系统初始化完成

🔧 配置说明:
1. 访问 /notifications 页面进行通知配置管理
2. 支持邮件、企业微信、飞书三种通知方式
3. 密钥信息会自动加密存储
4. 编辑时密钥会遮掩显示，保护敏感信息

📝 配置步骤:
1. 邮件通知: 配置SMTP服务器信息
2. 企业微信: 配置机器人Webhook URL
3. 飞书通知: 配置机器人Webhook URL

🔐 安全提示:
- 系统会自动生成加密密钥
- 请妥善保管 NOTIFICATION_ENCRYPTION_KEY 环境变量
- 生产环境建议预先设置加密密钥

🚀 启动服务:
- 重启应用后，通知管理器会自动加载数据库配置
- 定时任务会使用最新的通知配置发送每日报告
""")


if __name__ == "__main__":
    print("🚀 开始初始化通知配置表...")
    init_notification_config_table()
    show_usage()
    print("🎉 初始化完成！")
