#!/usr/bin/env python3
"""
删除数据库中的重复记录
"""
import sys
import os
from collections import defaultdict

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.holdings import HoldingChange
from sqlalchemy import and_

def remove_duplicates():
    """删除重复的增减持记录"""
    db = SessionLocal()
    
    try:
        print("开始查找重复记录...")
        
        # 获取所有记录
        all_records = db.query(HoldingChange).all()
        print(f"总记录数: {len(all_records)}")
        
        # 按唯一标识分组
        groups = defaultdict(list)
        for record in all_records:
            # 创建唯一标识
            key = (
                record.stock_id,
                record.announcement_date,
                record.holder_name,
                record.direction,
                record.change_shares,
                record.change_reason
            )
            groups[key].append(record)
        
        # 找出重复的组
        duplicates = {k: v for k, v in groups.items() if len(v) > 1}
        print(f"发现 {len(duplicates)} 组重复记录")
        
        if not duplicates:
            print("没有重复记录")
            return
        
        # 删除重复记录（保留第一条）
        deleted_count = 0
        for key, records in duplicates.items():
            print(f"处理重复组: 股票ID={key[0]}, 持有人={key[2]}, 日期={key[1]}")
            print(f"  该组有 {len(records)} 条记录")
            
            # 保留第一条，删除其余的
            for record in records[1:]:
                db.delete(record)
                deleted_count += 1
                print(f"  删除记录ID: {record.id}")
        
        # 提交更改
        db.commit()
        print(f"成功删除 {deleted_count} 条重复记录")
        
        # 验证结果
        remaining_count = db.query(HoldingChange).count()
        print(f"剩余记录数: {remaining_count}")
        
    except Exception as e:
        print(f"删除重复记录失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    remove_duplicates()
