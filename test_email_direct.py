#!/usr/bin/env python3
"""
直接使用配置值测试邮件发送
"""
import asyncio
from datetime import datetime
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.notifications.notification_manager import notification_manager
from app.schemas.notifications import NotificationContentBase, NotificationType, NotificationPriority


async def test_email_with_direct_config():
    """使用直接配置测试邮件发送"""
    try:
        print("📧 开始测试邮件通知功能（使用直接配置）...")
        
        # 构造测试消息内容
        content = NotificationContentBase(
            title="🧪 邮件通知测试（直接配置）",
            content=f"""这是一条来自股票增减持分析平台的测试邮件

📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🔧 服务类型: EMAIL
✅ 配置状态: 使用直接配置
🎯 测试目的: 验证邮件服务连通性

如果您收到这条邮件，说明邮件通知配置工作正常！

---
此邮件由增减持数据分析平台自动发送
""",
            summary="如果您收到这条邮件，说明邮件通知配置工作正常！"
        )
        
        # 直接指定邮件配置（使用您在.env中配置的真实值）
        test_config = {
            'to_emails': ['<EMAIL>'],  # 发送给自己
            'subject': '🧪 邮件通知测试（直接配置） - 增减持数据分析平台'
        }
        
        print("📧 开始发送测试邮件...")
        print(f"📮 收件人: {test_config['to_emails']}")
        print(f"📝 主题: {test_config['subject']}")
        
        # 发送邮件
        result = await notification_manager.send_notification(
            notification_type=NotificationType.EMAIL,
            content=content,
            config=test_config,
            priority=NotificationPriority.NORMAL
        )
        
        if result.success:
            print("✅ 邮件发送成功！")
            print(f"📝 发送结果: {result.message}")
            if result.data:
                print(f"📊 发送详情: {result.data}")
        else:
            print("❌ 邮件发送失败！")
            print(f"📝 错误信息: {result.message}")
            
    except Exception as e:
        print(f"❌ 发送过程中出现异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 启动邮件通知测试（直接配置）...")
    print("📧 使用配置:")
    print("   - 收件人: <EMAIL>")
    print("   - 发件人: <EMAIL>")
    print("   - SMTP服务器: smtp.qq.com:587")
    print()
    
    asyncio.run(test_email_with_direct_config())
