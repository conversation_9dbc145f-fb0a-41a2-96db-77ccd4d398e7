#!/usr/bin/env python3
"""
清除测试数据
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.core.database import get_db

def clear_test_data():
    """清除测试数据"""
    db = next(get_db())
    
    try:
        print("开始清除测试数据...")
        
        # 1. 查看当前数据量
        print("1. 查看当前数据量...")
        result = db.execute(text("SELECT COUNT(*) FROM holding_changes;"))
        holding_count = result.fetchone()[0]
        
        result = db.execute(text("SELECT COUNT(*) FROM stocks;"))
        stock_count = result.fetchone()[0]
        
        print(f"当前增减持记录数: {holding_count}")
        print(f"当前股票记录数: {stock_count}")
        
        # 2. 删除增减持记录
        print("2. 删除增减持记录...")
        result = db.execute(text("DELETE FROM holding_changes;"))
        print(f"删除了 {result.rowcount} 条增减持记录")
        
        # 3. 删除股票记录（可选，如果想保留股票基础数据可以注释掉）
        print("3. 删除股票记录...")
        result = db.execute(text("DELETE FROM stocks;"))
        print(f"删除了 {result.rowcount} 条股票记录")
        
        # 4. 重置序列
        print("4. 重置序列...")
        db.execute(text("ALTER SEQUENCE holding_changes_id_seq RESTART WITH 1;"))
        db.execute(text("ALTER SEQUENCE stocks_id_seq RESTART WITH 1;"))
        
        # 提交事务
        db.commit()
        
        # 5. 验证清除结果
        print("5. 验证清除结果...")
        result = db.execute(text("SELECT COUNT(*) FROM holding_changes;"))
        holding_count_after = result.fetchone()[0]
        
        result = db.execute(text("SELECT COUNT(*) FROM stocks;"))
        stock_count_after = result.fetchone()[0]
        
        print(f"清除后增减持记录数: {holding_count_after}")
        print(f"清除后股票记录数: {stock_count_after}")
        
        print("✅ 测试数据清除完成！")
        
    except Exception as e:
        print(f"清除测试数据失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    clear_test_data()
