#!/usr/bin/env python3
"""
插入测试数据
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import date
from app.core.database import get_db
from app.models.holdings import HoldingChange, ChangeDirection, HolderType, Stock

def insert_test_data():
    """插入测试数据"""
    db = next(get_db())
    
    try:
        # 创建测试股票
        test_stocks = [
            Stock(code="000001", name="平安银行", exchange="SZSE"),
            Stock(code="000002", name="万科A", exchange="SZSE"),
            Stock(code="600000", name="浦发银行", exchange="SSE"),
        ]
        
        for stock in test_stocks:
            existing = db.query(Stock).filter(Stock.code == stock.code).first()
            if not existing:
                db.add(stock)
        
        db.commit()
        
        # 获取股票ID
        stock1 = db.query(Stock).filter(Stock.code == "000001").first()
        stock2 = db.query(Stock).filter(Stock.code == "000002").first()
        stock3 = db.query(Stock).filter(Stock.code == "600000").first()
        
        # 创建测试增减持记录
        test_holdings = [
            HoldingChange(
                stock_id=stock1.id,
                announcement_date=date(2025, 6, 24),
                change_date=date(2025, 6, 24),
                holder_name="张三",
                holder_type=HolderType.EXECUTIVE,
                direction=ChangeDirection.INCREASE,
                change_shares=100000.0,
                total_shares_after=1000000.0,
                holding_ratio_after=1.5,
                price_min=10.0,
                price_max=12.0,
                price_avg=11.0,
                change_amount=1100000.0,
                change_reason="股权激励"
            ),
            HoldingChange(
                stock_id=stock1.id,
                announcement_date=date(2025, 6, 23),
                change_date=date(2025, 6, 23),
                holder_name="李四",
                holder_type=HolderType.EXECUTIVE,
                direction=ChangeDirection.DECREASE,
                change_shares=50000.0,
                total_shares_after=500000.0,
                holding_ratio_after=0.8,
                price_min=9.5,
                price_max=10.5,
                price_avg=10.0,
                change_amount=500000.0,
                change_reason="减持套现"
            ),
            HoldingChange(
                stock_id=stock2.id,
                announcement_date=date(2025, 6, 22),
                change_date=date(2025, 6, 22),
                holder_name="王五",
                holder_type=HolderType.INSTITUTION,
                direction=ChangeDirection.INCREASE,
                change_shares=200000.0,
                total_shares_after=2000000.0,
                holding_ratio_after=2.0,
                price_min=15.0,
                price_max=16.0,
                price_avg=15.5,
                change_amount=3100000.0,
                change_reason="战略投资"
            ),
            HoldingChange(
                stock_id=stock3.id,
                announcement_date=date(2025, 6, 21),
                change_date=date(2025, 6, 21),
                holder_name="赵六",
                holder_type=HolderType.MAJOR_SHAREHOLDER,
                direction=ChangeDirection.DECREASE,
                change_shares=300000.0,
                total_shares_after=3000000.0,
                holding_ratio_after=3.0,
                price_min=8.0,
                price_max=9.0,
                price_avg=8.5,
                change_amount=2550000.0,
                change_reason="资金需求"
            ),
            HoldingChange(
                stock_id=stock2.id,
                announcement_date=date(2025, 6, 20),
                change_date=date(2025, 6, 20),
                holder_name="孙七",
                holder_type=HolderType.OTHER,
                direction=ChangeDirection.INCREASE,
                change_shares=80000.0,
                total_shares_after=800000.0,
                holding_ratio_after=0.5,
                price_min=14.0,
                price_max=15.0,
                price_avg=14.5,
                change_amount=1160000.0,
                change_reason="看好前景"
            )
        ]
        
        for holding in test_holdings:
            # 检查是否已存在
            existing = db.query(HoldingChange).filter(
                HoldingChange.stock_id == holding.stock_id,
                HoldingChange.announcement_date == holding.announcement_date,
                HoldingChange.holder_name == holding.holder_name,
                HoldingChange.direction == holding.direction
            ).first()
            
            if not existing:
                db.add(holding)
        
        db.commit()
        
        # 验证插入结果
        total_count = db.query(HoldingChange).count()
        increase_count = db.query(HoldingChange).filter(HoldingChange.direction == ChangeDirection.INCREASE).count()
        decrease_count = db.query(HoldingChange).filter(HoldingChange.direction == ChangeDirection.DECREASE).count()
        
        print(f"✅ 测试数据插入成功！")
        print(f"总记录数: {total_count}")
        print(f"增持记录数: {increase_count}")
        print(f"减持记录数: {decrease_count}")
        
    except Exception as e:
        print(f"插入测试数据失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    insert_test_data()
