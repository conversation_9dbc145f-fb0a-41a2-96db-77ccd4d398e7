#!/usr/bin/env python3
"""
初始化简化通知配置表
"""
from sqlalchemy import create_engine, text
from app.core.config import settings
from app.core.database import SessionLocal
from app.services.simple_notification_config_service import SimpleNotificationConfigService


def init_simple_notification_config():
    """初始化简化通知配置"""
    print("🚀 开始初始化简化通知配置...")
    
    # 创建数据库会话
    db = SessionLocal()
    
    try:
        # 创建配置服务
        config_service = SimpleNotificationConfigService(db)
        
        # 初始化默认配置
        config_service.initialize_default_configs()
        
        print("✅ 简化通知配置初始化完成！")
        
        # 显示当前配置
        configs = config_service.get_all_configs()
        print(f"\n📋 当前配置列表 ({len(configs)} 项):")
        for config in configs:
            status = "✅ 启用" if config.is_enabled else "❌ 禁用"
            print(f"  • {config.service_type.value.upper()}: {status}")
            print(f"    描述: {config.description}")
            print(f"    通知时间: {config.notification_time}")
            print()
        
        print("🔧 配置说明:")
        print("1. 访问 /notifications 页面进行通知配置管理")
        print("2. 支持邮件、企业微信、飞书三种通知方式")
        print("3. 敏感信息（密码、Webhook URL等）在环境变量中配置")
        print("4. 页面只管理启用状态和基本设置（收件人、@用户等）")
        print()
        print("📝 配置步骤:")
        print("1. 邮件通知: 在环境变量中配置SMTP信息，页面中配置收件人")
        print("2. 企业微信: 在环境变量中配置Webhook URL，页面中配置@用户")
        print("3. 飞书通知: 在环境变量中配置Webhook URL，页面中配置@用户")
        print()
        print("🚀 启动服务:")
        print("- 重启应用后，通知管理器会自动加载数据库配置")
        print("- 定时任务会使用最新的通知配置发送每日报告")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


def show_env_config_example():
    """显示环境变量配置示例"""
    print("\n📋 环境变量配置示例 (.env 文件):")
    print("""
# ==================== 邮件通知配置 ====================
ENABLE_EMAIL_NOTIFICATION=true
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=增减持数据分析平台

# ==================== 企业微信通知配置 ====================
ENABLE_WECHAT_NOTIFICATION=true
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your_key

# ==================== 飞书通知配置 ====================
ENABLE_FEISHU_NOTIFICATION=true
FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/your_hook_id
FEISHU_SECRET_KEY=your_secret_key

# ==================== 通知时间配置 ====================
DAILY_NOTIFICATION_TIME=09:00
NOTIFICATION_TIMEZONE=Asia/Shanghai
""")


if __name__ == "__main__":
    print("🎯 简化通知配置管理系统初始化")
    print("=" * 50)
    
    init_simple_notification_config()
    show_env_config_example()
    
    print("\n🎉 初始化完成！")
    print("💡 提示: 请根据上述示例配置环境变量，然后访问 /notifications 页面进行配置管理")
