#!/usr/bin/env python3
"""
测试近7天增持数据邮件发送
"""
import asyncio
from datetime import datetime, date, timedelta
from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.services.notifications.notification_manager import notification_manager


async def test_weekly_increase_notification():
    """测试近7天增持数据通知"""
    db = SessionLocal()
    
    try:
        print("📧 开始测试近7天增持数据邮件通知...")
        print("🔧 这是新的功能：只发送增持信息，包含变动比例")
        print()
        
        # 发送近7天增持数据通知
        results = await notification_manager.send_daily_holding_changes_notification(db)
        
        if not results:
            print("⚠️ 没有发送任何通知（可能没有数据或服务未启用）")
            return
        
        print("📊 通知发送结果：")
        for notification_type, result in results.items():
            if result.success:
                print(f"✅ {notification_type.value}: 发送成功")
                print(f"   📝 消息: {result.message}")
                if result.data:
                    print(f"   📊 详情: {result.data}")
            else:
                print(f"❌ {notification_type.value}: 发送失败")
                print(f"   📝 错误: {result.message}")
        
        print("\n🎉 测试完成！")
        print("💡 新功能特点：")
        print("  • 只发送近7天的增持信息（不包括减持）")
        print("  • 邮件表格中包含变动比例列")
        print("  • 每天早上9点自动发送")
        print("  • 按变动金额排序显示重要增持")
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


async def test_manual_weekly_increase():
    """手动测试近7天增持数据获取"""
    db = SessionLocal()
    
    try:
        print("\n📊 手动测试数据获取...")
        
        # 直接调用数据获取方法
        end_date = date.today()
        holding_data = await notification_manager._get_weekly_increase_holding_data(db, end_date)
        
        print(f"📅 统计时间: {holding_data['date']}")
        print(f"📊 增持记录: {holding_data['total_count']} 条")
        print(f"💰 总金额: {holding_data['total_amount']/10000:.2f} 万元")
        print(f"📈 涉及股票: {holding_data['stats']['stock_count']} 只")
        print(f"📋 平均金额: {holding_data['stats']['avg_amount']/10000:.2f} 万元/笔")
        
        if holding_data['top_changes']:
            print(f"\n🔥 重要增持（前5名）:")
            for i, change in enumerate(holding_data['top_changes'][:5], 1):
                amount_text = f"{change['change_amount']/10000:.2f}万元" if change['change_amount'] else "未知"
                ratio_text = f"，变动比例{change['change_ratio']:.2f}%" if change['change_ratio'] else ""
                price_text = f"，均价{change['price_avg']:.2f}元" if change['price_avg'] and change['price_avg'] > 0 else ""
                
                print(f"  {i}. {change['stock_name']}({change['stock_code']}) - {change['holder_name']}")
                print(f"     增持{change['change_shares']:.2f}万股，金额{amount_text}{ratio_text}{price_text}")
                print(f"     公告日期: {change['announcement_date']}")
        
        print(f"\n💡 数据摘要: {holding_data['summary']}")
        
    except Exception as e:
        print(f"❌ 数据获取失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 启动近7天增持数据邮件测试...")
    print("📧 新功能测试：")
    print("  1. 只发送增持信息（不包括减持）")
    print("  2. 邮件表格包含变动比例")
    print("  3. 统计近7天数据")
    print("  4. 每天早上9点自动发送")
    print()
    
    # 运行测试
    asyncio.run(test_manual_weekly_increase())
    asyncio.run(test_weekly_increase_notification())
