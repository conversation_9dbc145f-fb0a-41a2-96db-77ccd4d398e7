#!/usr/bin/env python3
"""
调试重复数据的脚本
"""
import asyncio
import sys
import os
from collections import Counter

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.scraper import TongHuaShunScraper

async def debug_duplicates():
    """调试重复数据问题"""
    scraper = TongHuaShunScraper()
    
    print("开始抓取前3页数据进行重复检查...")
    
    # 抓取前3页数据
    data = await scraper.scrape_holdings_data(page=1, max_pages=3)
    
    if not data:
        print("没有抓取到数据")
        return
    
    print(f"总共抓取到 {len(data)} 条数据")
    
    # 创建唯一标识符来检查重复
    unique_keys = []
    for item in data:
        # 使用股票代码+持有人+日期+方向+变动股数+变动原因作为唯一标识
        key = f"{item['stock_code']}-{item['holder_name']}-{item['announcement_date']}-{item['direction']}-{item['change_shares']}-{item['change_reason']}"
        unique_keys.append(key)
    
    # 统计重复
    key_counter = Counter(unique_keys)
    duplicates = {k: v for k, v in key_counter.items() if v > 1}
    
    print(f"\n=== 重复数据分析 ===")
    print(f"唯一记录数: {len(set(unique_keys))}")
    print(f"总记录数: {len(unique_keys)}")
    print(f"重复记录数: {len(duplicates)}")
    
    if duplicates:
        print(f"\n发现 {len(duplicates)} 组重复数据:")
        for key, count in list(duplicates.items())[:5]:  # 只显示前5个
            print(f"  {key}: 出现 {count} 次")
            
        # 找到重复的具体数据
        print(f"\n=== 重复数据详情 ===")
        first_duplicate_key = list(duplicates.keys())[0]
        duplicate_items = []
        for i, item in enumerate(data):
            key = f"{item['stock_code']}-{item['holder_name']}-{item['announcement_date']}-{item['direction']}-{item['change_shares']}-{item['change_reason']}"
            if key == first_duplicate_key:
                duplicate_items.append((i, item))
        
        print(f"以 {first_duplicate_key} 为例:")
        for idx, (pos, item) in enumerate(duplicate_items):
            print(f"  第{idx+1}次出现 (位置{pos}):")
            print(f"    股票: {item['stock_code']} {item['stock_name']}")
            print(f"    持有人: {item['holder_name']}")
            print(f"    日期: {item['announcement_date']}")
            print(f"    方向: {item['direction']}")
            print(f"    变动股数: {item['change_shares']}")
            print(f"    变动原因: {item['change_reason']}")
            print()
    else:
        print("没有发现重复数据")
    
    # 检查600508的数据
    print(f"\n=== 600508数据检查 ===")
    stock_600508_data = [item for item in data if item['stock_code'] == '600508']
    print(f"600508共有 {len(stock_600508_data)} 条记录:")
    for i, item in enumerate(stock_600508_data):
        print(f"  第{i+1}条:")
        print(f"    持有人: {item['holder_name']}")
        print(f"    日期: {item['announcement_date']}")
        print(f"    方向: {item['direction']}")
        print(f"    变动股数: {item['change_shares']}")
        print()

if __name__ == "__main__":
    asyncio.run(debug_duplicates())
